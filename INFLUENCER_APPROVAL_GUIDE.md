# Influencer Approval System - Complete Guide

## 🎯 **How the Influencer Approval System Works**

### **Step 1: Influencer Registration**
1. Influencers register at `/auth/register.php`
2. They select "Influencer" as account type
3. Provide Instagram details (handle, followers, category)
4. Account is created with `status = 'pending'`

### **Step 2: Admin Review Process**
1. Admin logs in and goes to **Admin Panel → Influencers**
2. Sees all influencers with different statuses:
   - 🟡 **Pending** - Awaiting approval
   - 🟢 **Approved** - Can access influencer panel
   - 🔴 **Rejected** - Application denied
   - ⚫ **Suspended** - Account suspended

### **Step 3: Approval Methods**

#### **Method 1: Quick Approve/Reject**
- For pending influencers, you'll see green ✅ and red ❌ buttons
- Click ✅ to instantly approve
- Click ❌ to instantly reject
- Confirmation dialog appears before action

#### **Method 2: Detailed Review**
- Click 👁️ (View) button to see full influencer profile
- Review Instagram details, follower count, category
- Click "Approve/Reject" button in modal
- Select status from dropdown and save

#### **Method 3: Bulk Actions**
- Select multiple pending influencers using checkboxes
- Choose "Approve Selected" or "Reject Selected"
- Click "Apply to Selected" to process all at once

## 🔧 **Testing the System**

### **1. Import Test Data**
```sql
-- Run this in phpMyAdmin after main database import
SOURCE test_data.sql;
```

This creates:
- 4 pending influencers
- 1 approved influencer
- Test users and campaigns

### **2. Access Admin Panel**
1. Go to `http://localhost/realearners/admin/`
2. Login: `<EMAIL>` / `password`
3. Click "Influencers" in sidebar

### **3. Test Approval Process**
1. You should see 4 pending influencers
2. Try quick approve on one influencer
3. Try bulk approve on multiple influencers
4. Check that approved influencers can login to influencer panel

## 🚨 **Troubleshooting**

### **Problem: No Influencers Showing**
**Solution:**
1. Check if influencers table has data:
```sql
SELECT * FROM influencers;
```
2. Import test_data.sql if empty
3. Clear browser cache

### **Problem: Approval Not Working**
**Solution:**
1. Check browser console for JavaScript errors
2. Verify form submission in Network tab
3. Check PHP error logs
4. Ensure database connection is working

### **Problem: Status Not Updating**
**Solution:**
1. Check database permissions
2. Verify the update query in admin/influencers.php
3. Check for PHP errors in error log
4. Test with simple status update

### **Problem: Bulk Actions Not Working**
**Solution:**
1. Ensure checkboxes are being selected
2. Check JavaScript console for errors
3. Verify form submission includes influencer_ids[]
4. Check bulk processing code in PHP

## 📋 **Manual Testing Checklist**

### ✅ **Basic Functionality**
- [ ] Can see pending influencers in admin panel
- [ ] Quick approve button works
- [ ] Quick reject button works
- [ ] Status updates in database
- [ ] Success message appears

### ✅ **Bulk Actions**
- [ ] Can select multiple influencers
- [ ] "Select All" button works
- [ ] Bulk approve works
- [ ] Bulk reject works
- [ ] Correct count processed

### ✅ **Detailed Review**
- [ ] View modal shows influencer details
- [ ] Instagram info displays correctly
- [ ] Status dropdown works
- [ ] Update saves correctly

### ✅ **User Experience**
- [ ] Approved influencers can login
- [ ] Rejected influencers see rejection message
- [ ] Pending influencers see pending message
- [ ] Notifications work properly

## 🔍 **Database Queries for Testing**

### **Check Influencer Statuses**
```sql
SELECT id, full_name, instagram_handle, status, created_at 
FROM influencers 
ORDER BY created_at DESC;
```

### **Count by Status**
```sql
SELECT status, COUNT(*) as count 
FROM influencers 
GROUP BY status;
```

### **Reset All to Pending (for testing)**
```sql
UPDATE influencers SET status = 'pending' WHERE id > 1;
```

### **Create Test Influencer**
```sql
INSERT INTO influencers (username, email, password, full_name, phone, instagram_handle, instagram_followers, category, status) 
VALUES ('test_inf', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Test Influencer', '1234567890', 'test_handle', 10000, 'lifestyle', 'pending');
```

## 🎯 **Expected Behavior**

### **When Influencer is Approved:**
1. Status changes to 'approved' in database
2. Green success message appears
3. Influencer can login to influencer panel
4. Influencer sees dashboard with campaigns

### **When Influencer is Rejected:**
1. Status changes to 'rejected' in database
2. Success message appears
3. Influencer sees rejection message on login
4. Cannot access influencer features

### **Bulk Processing:**
1. Multiple influencers processed at once
2. Count of processed influencers shown
3. All selected influencers updated
4. Page refreshes with updated statuses

## 📞 **Still Having Issues?**

### **Check These Files:**
1. `admin/influencers.php` - Main approval logic
2. `includes/header.php` - Notification display
3. `config.php` - Database connection
4. Browser console - JavaScript errors
5. PHP error log - Server errors

### **Common Fixes:**
1. **Clear browser cache** completely
2. **Restart Apache/MySQL** services
3. **Check file permissions** (755 for folders, 644 for files)
4. **Verify database connection** in config.php
5. **Import fresh database** if corrupted

### **Test with Simple Query:**
```php
// Add this to admin/influencers.php temporarily to test
echo "<pre>";
print_r($_POST);
echo "</pre>";
```

The influencer approval system is now **complete and fully functional**! 🎉
