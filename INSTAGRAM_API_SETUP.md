# Instagram API Setup Guide for RealEarners

## 🚀 Live Instagram Integration

This guide will help you connect your real Instagram accounts (@thesyedabubakkar and @real_earners.in) to display live posts and profile data on your homepage.

## 📋 Prerequisites

1. **Instagram Business/Creator Accounts**
   - Convert both accounts to Business or Creator accounts
   - This is required for API access

2. **Facebook Developer Account**
   - Instagram API is managed through Facebook
   - Create account at: https://developers.facebook.com

## 🔧 Setup Steps

### Step 1: Create Facebook App

1. Go to https://developers.facebook.com
2. Click "Create App"
3. Choose "Business" as app type
4. Fill in app details:
   - App Name: "RealEarners Instagram Integration"
   - Contact Email: your email
   - Business Account: your business account

### Step 2: Add Instagram Basic Display

1. In your Facebook app dashboard
2. Go to "Add Products"
3. Find "Instagram Basic Display" and click "Set Up"
4. This allows you to access Instagram content

### Step 3: Configure Instagram Basic Display

1. Go to Instagram Basic Display > Basic Display
2. Click "Create New App"
3. Fill in the required fields:
   - Display Name: "RealEarners"
   - Valid <PERSON>uth Redirect URIs: `https://yourdomain.com/auth/instagram/callback`
   - Deauthorize Callback URL: `https://yourdomain.com/auth/instagram/deauth`
   - Data Deletion Request URL: `https://yourdomain.com/auth/instagram/delete`

### Step 4: Add Instagram Testers

1. Go to Roles > Roles
2. Click "Add Instagram Testers"
3. Add both Instagram accounts:
   - @thesyedabubakkar
   - @real_earners.in
4. Accept the tester invitations on both accounts

### Step 5: Generate Access Tokens

1. Go to Instagram Basic Display > Basic Display
2. Click "Generate Token" for each account
3. Follow the authorization flow
4. Save the long-lived access tokens

### Step 6: Update Configuration

Edit `includes/instagram_api.php` and replace the mock data:

```php
class InstagramAPI {
    private $access_tokens = [
        'thesyedabubakkar' => 'YOUR_FOUNDER_ACCESS_TOKEN_HERE',
        'real_earners.in' => 'YOUR_BRAND_ACCESS_TOKEN_HERE'
    ];
    
    // Replace getMockProfileData with real API calls
    private function getRealProfileData($username) {
        $access_token = $this->access_tokens[$username];
        
        $url = "https://graph.instagram.com/me?fields=id,username,media_count,account_type&access_token=" . $access_token;
        
        $response = file_get_contents($url);
        return json_decode($response, true);
    }
    
    // Replace getMockPostsData with real API calls
    private function getRealPostsData($username, $limit) {
        $access_token = $this->access_tokens[$username];
        
        $url = "https://graph.instagram.com/me/media?fields=id,caption,media_type,media_url,permalink,timestamp,like_count,comments_count&limit=" . $limit . "&access_token=" . $access_token;
        
        $response = file_get_contents($url);
        $data = json_decode($response, true);
        
        return $data['data'] ?? [];
    }
}
```

## 🔄 Token Refresh

Instagram access tokens expire after 60 days. Set up automatic refresh:

```php
private function refreshToken($access_token) {
    $url = "https://graph.instagram.com/refresh_access_token?grant_type=ig_refresh_token&access_token=" . $access_token;
    
    $response = file_get_contents($url);
    $data = json_decode($response, true);
    
    return $data['access_token'] ?? null;
}
```

## 📊 Available Data

### Profile Information:
- Username
- Display name
- Bio
- Follower count
- Following count
- Media count
- Profile picture
- Website

### Post Information:
- Post ID
- Caption
- Media type (IMAGE, VIDEO, CAROUSEL)
- Media URL
- Permalink
- Timestamp
- Like count
- Comments count

## 🛡️ Rate Limits

Instagram API has rate limits:
- **200 requests per hour** per access token
- Cache data for at least 1 hour to avoid limits
- Use the built-in caching system in the API class

## 🔒 Security Best Practices

1. **Store tokens securely**
   - Use environment variables
   - Never commit tokens to version control

2. **Validate data**
   - Always sanitize API responses
   - Check for required fields

3. **Error handling**
   - Gracefully handle API failures
   - Fall back to cached data

## 🚀 Going Live

1. **Test thoroughly** with both accounts
2. **Monitor API usage** to stay within limits
3. **Set up monitoring** for token expiration
4. **Cache aggressively** to improve performance

## 📞 Support

If you need help setting up the Instagram API:
- Facebook Developer Documentation: https://developers.facebook.com/docs/instagram-basic-display-api
- Instagram Platform Policy: https://developers.facebook.com/docs/instagram-api/overview#instagram-platform-policy

## 🎯 Current Status

✅ **Mock Data System** - Currently showing realistic demo data
🔄 **Ready for Live Integration** - Follow steps above to connect real accounts
📱 **Responsive Design** - Works perfectly on all devices
🎨 **Instagram-Style UI** - Authentic Instagram look and feel

Once you complete the setup, your homepage will display real, live Instagram posts from both @thesyedabubakkar and @real_earners.in accounts!
