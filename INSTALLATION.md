# RealEarners - Complete Installation Guide

## 🚀 Quick Start (5 Minutes Setup)

### Prerequisites
- XAMPP/WAMP/LAMP with PHP 7.4+
- MySQL 5.7+
- Web browser

### Step 1: Download & Extract
1. Download the complete RealEarners package
2. Extract to your web server directory:
   - **XAMPP**: `C:\xampp\htdocs\realearners\`
   - **WAMP**: `C:\wamp64\www\realearners\`
   - **Linux**: `/var/www/html/realearners/`

### Step 2: Database Setup
1. Start Apache and MySQL in XAMPP/WAMP
2. Open phpMyAdmin: `http://localhost/phpmyadmin`
3. Create new database: `realearners`
4. Import the database:
   - Click "Import" tab
   - Choose file: `database.sql`
   - Click "Go"

### Step 3: Configuration
1. Open `config.php` in a text editor
2. Update database settings:
```php
define('DB_HOST', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', '');  // Usually empty for <PERSON>AM<PERSON>
define('DB_NAME', 'realearners');
define('SITE_URL', 'http://localhost/realearners');
```

### Step 4: Set Permissions
Create these folders and set write permissions:
```
uploads/
uploads/profiles/
uploads/submissions/
uploads/brands/
```

**Windows (XAMPP)**: Right-click folders → Properties → Security → Give "Full Control"
**Linux**: `chmod 755 uploads/ -R`

### Step 5: Access the Platform
- **Homepage**: `http://localhost/realearners/`
- **Admin Login**: 
  - Email: `<EMAIL>`
  - Password: `password`

## 🎯 Default Accounts

### Admin Account
- **Email**: <EMAIL>
- **Password**: password
- **Access**: Complete platform control

### Test User Account
- **Email**: <EMAIL>
- **Password**: password
- **Type**: Regular User

### Test Influencer Account
- **Email**: <EMAIL>
- **Password**: password
- **Type**: Influencer (Approved)

## 📁 Folder Structure

```
realearners/
├── admin/              # Admin panel
│   ├── dashboard.php   # Admin dashboard
│   ├── users.php       # User management
│   ├── campaigns.php   # Campaign management
│   ├── submissions.php # Review submissions
│   ├── payouts.php     # Process payouts
│   ├── brands.php      # Brand management
│   ├── influencers.php # Influencer management
│   ├── settings.php    # Platform settings
│   └── export.php      # Data export
├── user/               # User panel
│   ├── dashboard.php   # User dashboard
│   ├── tasks.php       # Available tasks
│   ├── submissions.php # Submit work
│   ├── wallet.php      # Earnings & payouts
│   └── profile.php     # Profile management
├── influencer/         # Influencer panel
│   ├── dashboard.php   # Influencer dashboard
│   ├── campaigns.php   # Available campaigns
│   ├── wallet.php      # Earnings & payouts
│   └── profile.php     # Profile management
├── auth/               # Authentication
│   ├── login.php       # Multi-role login
│   ├── register.php    # Registration
│   └── logout.php      # Logout
├── includes/           # Shared files
│   ├── header.php      # Common header
│   ├── footer.php      # Common footer
│   └── functions.php   # Helper functions
├── uploads/            # File uploads
├── assets/             # CSS, JS, Images
├── config.php          # Configuration
├── database.sql        # Database schema
└── index.php           # Homepage
```

## ⚙️ Advanced Configuration

### Payment Gateway Setup (Razorpay)
1. Sign up at [razorpay.com](https://razorpay.com)
2. Get API keys from dashboard
3. Go to Admin → Settings
4. Enter Razorpay Key ID and Secret
5. Set minimum payout amount

### Email Configuration
1. Go to Admin → Settings
2. Configure SMTP settings:
   - **Host**: smtp.gmail.com (for Gmail)
   - **Port**: 587
   - **Username**: <EMAIL>
   - **Password**: your-app-password
   - **Encryption**: TLS

### File Upload Limits
Edit `php.ini` to increase upload limits:
```ini
upload_max_filesize = 10M
post_max_size = 10M
max_execution_time = 300
```

### Security Settings
1. Change default admin password immediately
2. Update `ENCRYPTION_KEY` in config.php
3. Set up SSL certificate for production
4. Configure firewall rules

## 🔧 Customization

### Branding
1. Replace logo in `assets/images/`
2. Update site name in `config.php`
3. Modify colors in `assets/css/style.css`
4. Update email templates

### Adding Features
1. Create new PHP files following existing structure
2. Use Bootstrap 5 classes for styling
3. Follow security best practices
4. Update database schema if needed

### Language Localization
1. Create language files in `includes/lang/`
2. Use translation functions in templates
3. Update admin settings for default language

## 🚀 Production Deployment

### Server Requirements
- **PHP**: 7.4 or higher
- **MySQL**: 5.7 or higher
- **Apache/Nginx**: Latest stable
- **SSL Certificate**: Required
- **Memory**: 512MB minimum
- **Storage**: 5GB minimum

### Deployment Steps
1. Upload files to web server
2. Create production database
3. Import database.sql
4. Update config.php with production settings
5. Set proper file permissions
6. Configure SSL certificate
7. Set up automated backups
8. Configure monitoring

### Performance Optimization
1. Enable PHP OPcache
2. Use CDN for static assets
3. Implement database indexing
4. Set up caching (Redis/Memcached)
5. Optimize images and compress files

## 🛠️ Troubleshooting

### Common Issues

**Database Connection Error**
- Check database credentials in config.php
- Ensure MySQL service is running
- Verify database exists

**File Upload Not Working**
- Check folder permissions (755 or 777)
- Verify PHP upload settings
- Ensure uploads/ directory exists

**Login Issues**
- Clear browser cache and cookies
- Check session configuration
- Verify user exists in database

**Payment Gateway Errors**
- Verify API keys are correct
- Check Razorpay account status
- Ensure SSL is enabled for production

### Debug Mode
Enable debug mode in config.php:
```php
define('DEBUG_MODE', true);
```

### Log Files
Check these locations for errors:
- PHP error log
- Apache/Nginx error log
- Application logs in `logs/` directory

## 📞 Support

### Getting Help
- Check documentation thoroughly
- Review error logs
- Test with default accounts first
- Verify all requirements are met

### Common Solutions
1. **Clear cache**: Delete browser cache and cookies
2. **Reset permissions**: Set folder permissions to 755
3. **Restart services**: Restart Apache and MySQL
4. **Check logs**: Review error logs for specific issues

## 🔄 Updates & Maintenance

### Regular Maintenance
1. **Backup database** weekly
2. **Update PHP** and dependencies
3. **Monitor disk space** and logs
4. **Review security** settings
5. **Clean old files** from uploads

### Backup Strategy
1. **Database**: Export via phpMyAdmin or mysqldump
2. **Files**: Copy entire application folder
3. **Automated**: Set up cron jobs for regular backups
4. **Storage**: Keep backups in multiple locations

### Version Updates
1. Backup current installation
2. Download new version
3. Compare config files
4. Run database migrations if needed
5. Test thoroughly before going live

---

## ✅ Installation Checklist

- [ ] XAMPP/WAMP installed and running
- [ ] Database created and imported
- [ ] Config.php updated with correct settings
- [ ] Upload folders created with proper permissions
- [ ] Admin login working
- [ ] User registration working
- [ ] File uploads working
- [ ] Email configuration (optional)
- [ ] Payment gateway setup (optional)
- [ ] SSL certificate (for production)

**🎉 Congratulations! Your RealEarners platform is ready to use!**

Start by logging in as admin and creating your first campaign. Users can then register and start earning money by completing tasks.

For any issues, check the troubleshooting section or review the error logs.
