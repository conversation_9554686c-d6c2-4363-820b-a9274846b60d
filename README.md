# RealEarners - Complete Multi-Role Advertising Platform

A comprehensive web platform that allows users to earn money by posting ads, influencers to manage brand collaborations, and admins to control the entire ecosystem.

## 🚀 Features

### 👥 For Users (Students, Youth, Housewives)
- **Simple Ad Posting**: Earn ₹50-200 per post
- **Easy Submissions**: Upload Instagram post links or screenshots
- **Instant Payments**: UPI payouts within 24-48 hours
- **Task Management**: Track assigned, submitted, and approved tasks
- **Wallet System**: Monitor earnings and request payouts

### 🌟 For Influencers
- **Brand Collaborations**: Earn ₹500-5000 per campaign
- **Campaign Management**: Accept/decline brand partnerships
- **Analytics Dashboard**: Track performance and earnings
- **Profile Management**: Showcase follower count and engagement
- **Premium Payouts**: Higher rewards for quality content

### 👑 For Admins
- **Complete Control**: Manage users, influencers, and campaigns
- **Campaign Creation**: Set up brand advertising campaigns
- **Submission Review**: Approve/reject user submissions
- **Payout Management**: Process payments via UPI/Razorpay
- **Analytics**: Track platform performance and revenue
- **Brand Management**: Add and manage advertising brands

## 🛠️ Technology Stack

- **Backend**: PHP 7.4+ with PDO
- **Database**: MySQL 5.7+
- **Frontend**: Bootstrap 5, jQuery, Font Awesome
- **Security**: CSRF protection, password hashing, input sanitization
- **Payments**: Razorpay integration (configurable)
- **File Uploads**: Secure image handling with validation

## 📁 Project Structure

```
realearners/
├── admin/                  # Admin panel files
│   ├── dashboard.php      # Admin dashboard
│   ├── users.php          # User management
│   ├── campaigns.php      # Campaign management
│   └── ...
├── user/                   # User panel files
│   ├── dashboard.php      # User dashboard
│   ├── tasks.php          # Available tasks
│   ├── submissions.php    # Submit work
│   ├── wallet.php         # Earnings & payouts
│   └── profile.php        # Profile management
├── influencer/            # Influencer panel (to be created)
├── auth/                  # Authentication
│   ├── login.php          # Multi-role login
│   ├── register.php       # User/Influencer registration
│   └── logout.php         # Session termination
├── includes/              # Shared components
│   ├── header.php         # Common header
│   └── footer.php         # Common footer
├── uploads/               # File uploads
│   ├── profiles/          # Profile images
│   ├── submissions/       # Task submissions
│   └── brands/            # Brand logos
├── assets/                # Static assets
├── config.php             # Configuration
├── database.sql           # Database schema
├── index.php              # Homepage
└── .htaccess              # URL rewriting & security
```

## 🔧 Installation & Setup

### 1. Prerequisites
- XAMPP/WAMP/LAMP with PHP 7.4+
- MySQL 5.7+
- Web browser

### 2. Database Setup
1. Open phpMyAdmin
2. Create a new database named `realearners`
3. Import the `database.sql` file
4. Default admin credentials:
   - Email: `<EMAIL>`
   - Password: `password`

### 3. Configuration
1. Edit `config.php`:
   ```php
   define('DB_HOST', 'localhost');
   define('DB_USERNAME', 'root');
   define('DB_PASSWORD', '');
   define('DB_NAME', 'realearners');
   define('SITE_URL', 'http://localhost/realearners');
   ```

2. Set up file permissions:
   ```bash
   chmod 755 uploads/
   chmod 755 uploads/profiles/
   chmod 755 uploads/submissions/
   chmod 755 uploads/brands/
   ```

### 4. Payment Gateway (Optional)
1. Sign up for Razorpay account
2. Get API keys from Razorpay dashboard
3. Update in `config.php`:
   ```php
   define('RAZORPAY_KEY_ID', 'your_key_id');
   define('RAZORPAY_KEY_SECRET', 'your_key_secret');
   ```

### 5. Access the Platform
- **Homepage**: `http://localhost/realearners/`
- **Admin Panel**: Login with admin credentials
- **User Registration**: Create user/influencer accounts

## 🎯 User Roles & Access

### User Panel (`/user/`)
- Dashboard with earnings overview
- Browse and apply for campaigns
- Submit work (Instagram posts/screenshots)
- Wallet management and payout requests
- Profile settings

### Influencer Panel (`/influencer/`)
- Campaign invitations and collaborations
- Higher payout rates
- Performance analytics
- Brand partnership management

### Admin Panel (`/admin/`)
- Complete platform control
- User and influencer management
- Campaign creation and management
- Submission review and approval
- Payout processing
- Brand management
- System settings

## 💰 Payment Flow

1. **User/Influencer** completes assigned task
2. **Submits** proof (Instagram link/screenshot)
3. **Admin** reviews and approves submission
4. **Earnings** added to user wallet
5. **User** requests payout (minimum ₹100)
6. **Admin** processes payment via UPI/Razorpay
7. **Payment** sent to user's UPI ID

## 🔐 Security Features

- **Password Hashing**: bcrypt encryption
- **CSRF Protection**: Token-based form security
- **Input Sanitization**: XSS prevention
- **File Upload Security**: Type and size validation
- **Session Management**: Secure user sessions
- **SQL Injection Prevention**: PDO prepared statements

## 📱 Mobile Responsive

- Bootstrap 5 responsive design
- Mobile-friendly navigation
- Touch-optimized interface
- Progressive Web App ready

## 🚀 Getting Started

1. **Clone/Download** the project
2. **Set up** XAMPP and start Apache/MySQL
3. **Import** database.sql
4. **Configure** config.php
5. **Access** http://localhost/realearners/
6. **Login** as admin to set up campaigns
7. **Register** users to start earning!

## 🔧 Customization

### Adding New Features
- Extend database schema in `database.sql`
- Create new PHP files following existing structure
- Use Bootstrap classes for consistent styling
- Follow security best practices

### Branding
- Update `SITE_NAME` in config.php
- Replace logo and favicon in assets/
- Customize colors in CSS variables
- Update email templates and content

## 📞 Support

For technical support or customization requests:
- Email: <EMAIL>
- Documentation: Check inline code comments
- Issues: Review error logs in XAMPP

## 📄 License

This project is created for educational and commercial use. Feel free to modify and distribute according to your needs.

---

**Ready to launch your advertising platform? Start earning and helping others earn today! 🚀**
