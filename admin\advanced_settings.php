<?php
require_once '../config.php';
require_login(['admin']);

$db = Database::getInstance();

$success = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_settings') {
        try {
            $updated_count = 0;
            
            // Process all settings
            foreach ($_POST as $key => $value) {
                if (strpos($key, 'setting_') === 0) {
                    $setting_key = substr($key, 8); // Remove 'setting_' prefix
                    
                    // Update the setting
                    $db->update('settings', [
                        'setting_value' => $value,
                        'updated_at' => date('Y-m-d H:i:s')
                    ], 'setting_key = ?', [$setting_key]);
                    
                    $updated_count++;
                }
            }
            
            $success = "Successfully updated $updated_count settings!";
        } catch (Exception $e) {
            $error = 'Error updating settings: ' . $e->getMessage();
        }
    } elseif ($action === 'add_setting') {
        $setting_key = trim($_POST['new_setting_key'] ?? '');
        $setting_value = trim($_POST['new_setting_value'] ?? '');
        $description = trim($_POST['new_description'] ?? '');
        $category = trim($_POST['new_category'] ?? 'general');
        $setting_type = trim($_POST['new_setting_type'] ?? 'text');
        
        if (!empty($setting_key)) {
            try {
                $db->insert('settings', [
                    'setting_key' => $setting_key,
                    'setting_value' => $setting_value,
                    'description' => $description,
                    'category' => $category,
                    'setting_type' => $setting_type
                ]);
                
                $success = 'New setting added successfully!';
            } catch (Exception $e) {
                $error = 'Error adding setting: ' . $e->getMessage();
            }
        } else {
            $error = 'Setting key is required!';
        }
    }
}

// Get all settings grouped by category
$settings_by_category = [];
$all_settings = $db->fetchAll("SELECT * FROM settings ORDER BY category, setting_key");

foreach ($all_settings as $setting) {
    $category = $setting['category'] ?: 'general';
    if (!isset($settings_by_category[$category])) {
        $settings_by_category[$category] = [];
    }
    $settings_by_category[$category][] = $setting;
}

// Category display names
$category_names = [
    'general' => 'General Settings',
    'payment' => 'Payment & Financial',
    'email' => 'Email Configuration',
    'notifications' => 'SMS & WhatsApp',
    'instagram' => 'Instagram Integration',
    'security' => 'Security & Authentication',
    'campaigns' => 'Campaign Management',
    'users' => 'User Management',
    'badges' => 'Badge System',
    'analytics' => 'Analytics & Tracking',
    'maintenance' => 'Maintenance & Backup',
    'api' => 'API Configuration',
    'uploads' => 'File Upload Settings',
    'performance' => 'Performance & Optimization'
];

$page_title = 'Advanced Settings';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-xl-2 sidebar p-0">
            <div class="p-3">
                <h5 class="text-white mb-4">
                    <i class="fas fa-shield-alt me-2"></i>Admin Panel
                </h5>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a class="nav-link" href="users.php">
                        <i class="fas fa-users"></i>Users
                    </a>
                    <a class="nav-link" href="influencers.php">
                        <i class="fas fa-star"></i>Influencers
                    </a>
                    <a class="nav-link" href="brands.php">
                        <i class="fas fa-building"></i>Brands
                    </a>
                    <a class="nav-link" href="campaigns.php">
                        <i class="fas fa-bullhorn"></i>Campaigns
                    </a>
                    <a class="nav-link" href="submissions.php">
                        <i class="fas fa-clipboard-check"></i>Submissions
                    </a>
                    <a class="nav-link" href="assignments.php">
                        <i class="fas fa-user-check"></i>Assignments
                    </a>
                    <a class="nav-link" href="payouts.php">
                        <i class="fas fa-money-bill-wave"></i>Payouts
                    </a>
                    
                    <!-- Instagram & Verification Section -->
                    <hr class="my-2 text-light">
                    <small class="text-light px-3 mb-2">Instagram & Verification</small>
                    
                    <a class="nav-link" href="instagram_verifications.php">
                        <i class="fab fa-instagram"></i>Instagram Verifications
                    </a>
                    <a class="nav-link" href="influencer_follow_check.php">
                        <i class="fas fa-user-check"></i>Follow Status Check
                    </a>
                    <a class="nav-link" href="instagram_blue_check.php">
                        <i class="fas fa-check-circle"></i>Blue Check Management
                    </a>
                    <a class="nav-link" href="user_verification_status.php">
                        <i class="fas fa-clipboard-check"></i>User Follow Status
                    </a>
                    <a class="nav-link" href="suspended_users.php">
                        <i class="fas fa-ban"></i>Suspended Users
                    </a>
                    
                    <!-- Badge Management Section -->
                    <hr class="my-2 text-light">
                    <small class="text-light px-3 mb-2">Badge Management</small>
                    
                    <a class="nav-link" href="badge_management.php">
                        <i class="fas fa-award"></i>Badge Management
                    </a>
                    <a class="nav-link" href="user_badge_assignment.php">
                        <i class="fas fa-user-tag"></i>Assign Badges
                    </a>
                    
                    <!-- System Section -->
                    <hr class="my-2 text-light">
                    <small class="text-light px-3 mb-2">System</small>
                    
                    <a class="nav-link" href="settings.php">
                        <i class="fas fa-cog"></i>Settings
                    </a>
                    <a class="nav-link active" href="advanced_settings.php">
                        <i class="fas fa-sliders-h"></i>Advanced Settings
                    </a>
                    <a class="nav-link" href="data_export.php">
                        <i class="fas fa-download"></i>Data Export
                    </a>
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10 main-content" style="margin-left: auto;">
            <!-- Top Bar -->
            <div class="bg-white shadow-sm p-3 mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">Advanced Settings</h4>
                        <small class="text-muted">Configure comprehensive platform settings</small>
                    </div>
                    <div class="col-auto">
                        <div class="btn-group">
                            <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addSettingModal">
                                <i class="fas fa-plus me-2"></i>Add Setting
                            </button>
                            <a href="data_export.php" class="btn btn-info">
                                <i class="fas fa-download me-2"></i>Export Data
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="p-3">
                <!-- Success/Error Messages -->
                <?php if ($success): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Settings Form -->
                <form method="POST" id="settingsForm">
                    <input type="hidden" name="action" value="update_settings">
                    
                    <!-- Settings by Category -->
                    <?php foreach ($settings_by_category as $category => $settings): ?>
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-cog me-2"></i>
                                    <?php echo $category_names[$category] ?? ucfirst($category); ?>
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <?php foreach ($settings as $setting): ?>
                                        <div class="col-md-6 mb-3">
                                            <label for="setting_<?php echo $setting['setting_key']; ?>" class="form-label">
                                                <?php echo htmlspecialchars($setting['description']); ?>
                                                <?php if ($setting['is_sensitive']): ?>
                                                    <i class="fas fa-lock text-warning ms-1" title="Sensitive setting"></i>
                                                <?php endif; ?>
                                            </label>
                                            
                                            <?php
                                            $field_name = "setting_" . $setting['setting_key'];
                                            $field_value = htmlspecialchars($setting['setting_value']);
                                            $field_id = "setting_" . $setting['setting_key'];
                                            ?>
                                            
                                            <?php if ($setting['setting_type'] === 'boolean'): ?>
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" 
                                                           id="<?php echo $field_id; ?>" 
                                                           name="<?php echo $field_name; ?>" 
                                                           value="1" 
                                                           <?php echo $setting['setting_value'] ? 'checked' : ''; ?>>
                                                    <label class="form-check-label" for="<?php echo $field_id; ?>">
                                                        <?php echo $setting['setting_value'] ? 'Enabled' : 'Disabled'; ?>
                                                    </label>
                                                </div>
                                                <input type="hidden" name="<?php echo $field_name; ?>" value="0">
                                                
                                            <?php elseif ($setting['setting_type'] === 'textarea'): ?>
                                                <textarea class="form-control" 
                                                          id="<?php echo $field_id; ?>" 
                                                          name="<?php echo $field_name; ?>" 
                                                          rows="3"><?php echo $field_value; ?></textarea>
                                                          
                                            <?php elseif ($setting['setting_type'] === 'select' && $setting['options']): ?>
                                                <select class="form-select" 
                                                        id="<?php echo $field_id; ?>" 
                                                        name="<?php echo $field_name; ?>">
                                                    <?php foreach (explode(',', $setting['options']) as $option): ?>
                                                        <option value="<?php echo trim($option); ?>" 
                                                                <?php echo $setting['setting_value'] === trim($option) ? 'selected' : ''; ?>>
                                                            <?php echo ucfirst(trim($option)); ?>
                                                        </option>
                                                    <?php endforeach; ?>
                                                </select>
                                                
                                            <?php elseif ($setting['setting_type'] === 'password'): ?>
                                                <div class="input-group">
                                                    <input type="password" 
                                                           class="form-control" 
                                                           id="<?php echo $field_id; ?>" 
                                                           name="<?php echo $field_name; ?>" 
                                                           value="<?php echo $field_value; ?>"
                                                           placeholder="Enter <?php echo strtolower($setting['description']); ?>">
                                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('<?php echo $field_id; ?>')">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                </div>
                                                
                                            <?php else: ?>
                                                <input type="<?php echo $setting['setting_type']; ?>" 
                                                       class="form-control" 
                                                       id="<?php echo $field_id; ?>" 
                                                       name="<?php echo $field_name; ?>" 
                                                       value="<?php echo $field_value; ?>"
                                                       <?php if ($setting['setting_type'] === 'number'): ?>
                                                           min="0" step="any"
                                                       <?php endif; ?>>
                                            <?php endif; ?>
                                            
                                            <small class="form-text text-muted">
                                                Key: <?php echo $setting['setting_key']; ?>
                                                <?php if ($setting['updated_at']): ?>
                                                    | Last updated: <?php echo date('M j, Y g:i A', strtotime($setting['updated_at'])); ?>
                                                <?php endif; ?>
                                            </small>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                    
                    <!-- Save Button -->
                    <div class="card">
                        <div class="card-body text-center">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save me-2"></i>Save All Settings
                            </button>
                            <button type="button" class="btn btn-secondary btn-lg ms-2" onclick="resetForm()">
                                <i class="fas fa-undo me-2"></i>Reset Changes
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Add Setting Modal -->
<div class="modal fade" id="addSettingModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Setting</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <input type="hidden" name="action" value="add_setting">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="new_setting_key" class="form-label">Setting Key</label>
                        <input type="text" class="form-control" id="new_setting_key" name="new_setting_key" required>
                        <small class="form-text text-muted">Use lowercase with underscores (e.g., my_custom_setting)</small>
                    </div>

                    <div class="mb-3">
                        <label for="new_setting_value" class="form-label">Setting Value</label>
                        <input type="text" class="form-control" id="new_setting_value" name="new_setting_value">
                    </div>

                    <div class="mb-3">
                        <label for="new_description" class="form-label">Description</label>
                        <input type="text" class="form-control" id="new_description" name="new_description" required>
                    </div>

                    <div class="mb-3">
                        <label for="new_category" class="form-label">Category</label>
                        <select class="form-select" id="new_category" name="new_category">
                            <?php foreach ($category_names as $cat_key => $cat_name): ?>
                                <option value="<?php echo $cat_key; ?>"><?php echo $cat_name; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="new_setting_type" class="form-label">Setting Type</label>
                        <select class="form-select" id="new_setting_type" name="new_setting_type">
                            <option value="text">Text</option>
                            <option value="textarea">Textarea</option>
                            <option value="number">Number</option>
                            <option value="boolean">Boolean (On/Off)</option>
                            <option value="select">Select Dropdown</option>
                            <option value="password">Password</option>
                            <option value="email">Email</option>
                            <option value="url">URL</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Setting</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.nextElementSibling.querySelector('i');

    if (field.type === 'password') {
        field.type = 'text';
        button.className = 'fas fa-eye-slash';
    } else {
        field.type = 'password';
        button.className = 'fas fa-eye';
    }
}

function resetForm() {
    if (confirm('Are you sure you want to reset all changes?')) {
        document.getElementById('settingsForm').reset();
    }
}

// Handle boolean switches
document.addEventListener('DOMContentLoaded', function() {
    const switches = document.querySelectorAll('.form-check-input[type="checkbox"]');
    switches.forEach(function(switchEl) {
        switchEl.addEventListener('change', function() {
            const label = this.nextElementSibling;
            label.textContent = this.checked ? 'Enabled' : 'Disabled';
        });
    });
});
</script>

<?php include '../includes/footer.php'; ?>
