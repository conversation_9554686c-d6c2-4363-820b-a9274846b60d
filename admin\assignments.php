<?php
require_once '../config.php';
require_login(['admin']);

$db = Database::getInstance();

// Handle assignment approval/rejection
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['approve_assignment'])) {
        $assignment_id = intval($_POST['assignment_id']);
        $admin_notes = sanitize_input($_POST['admin_notes']);
        
        try {
            // Get assignment details
            $assignment = $db->fetch("
                SELECT iua.*, u.id as user_id, c.id as campaign_id, bc.id as brand_campaign_id
                FROM influencer_user_assignments iua
                JOIN users u ON iua.user_id = u.id
                LEFT JOIN campaigns c ON iua.campaign_id = c.id
                LEFT JOIN brand_campaigns bc ON iua.brand_campaign_id = bc.id
                WHERE iua.id = ? AND iua.status = 'pending_admin_approval'
            ", [$assignment_id]);
            
            if ($assignment) {
                // Update assignment status
                $db->update('influencer_user_assignments', [
                    'status' => 'approved',
                    'approved_by_admin_at' => date('Y-m-d H:i:s'),
                    'assigned_to_user_at' => date('Y-m-d H:i:s'),
                    'admin_notes' => $admin_notes,
                    'admin_approved_by' => get_user_id()
                ], 'id = ?', [$assignment_id]);
                
                // Create user task
                if ($assignment['assignment_type'] === 'admin_campaign') {
                    $db->insert('user_tasks', [
                        'user_id' => $assignment['user_id'],
                        'campaign_id' => $assignment['campaign_id'],
                        'status' => 'assigned',
                        'reward_amount' => $assignment['reward_amount']
                    ]);
                } else {
                    // For brand campaigns, we might need a different table or approach
                    // For now, let's create a user task with brand campaign reference
                    $db->insert('user_tasks', [
                        'user_id' => $assignment['user_id'],
                        'campaign_id' => null, // No admin campaign
                        'status' => 'assigned',
                        'reward_amount' => $assignment['reward_amount']
                    ]);
                }
                
                $_SESSION['success'] = 'Assignment approved successfully!';
            } else {
                $_SESSION['error'] = 'Assignment not found or already processed.';
            }
        } catch (Exception $e) {
            $_SESSION['error'] = 'Failed to approve assignment.';
        }
    } elseif (isset($_POST['reject_assignment'])) {
        $assignment_id = intval($_POST['assignment_id']);
        $admin_notes = sanitize_input($_POST['admin_notes']);
        
        try {
            $db->update('influencer_user_assignments', [
                'status' => 'rejected',
                'admin_notes' => $admin_notes,
                'admin_approved_by' => get_user_id()
            ], 'id = ? AND status = ?', [$assignment_id, 'pending_admin_approval']);
            
            $_SESSION['success'] = 'Assignment rejected.';
        } catch (Exception $e) {
            $_SESSION['error'] = 'Failed to reject assignment.';
        }
    }
}

// Get filter parameters
$status_filter = $_GET['status'] ?? 'pending_admin_approval';
$assignment_type_filter = $_GET['assignment_type'] ?? '';

// Build query conditions
$conditions = ['1=1'];
$params = [];

if ($status_filter) {
    $conditions[] = "iua.status = ?";
    $params[] = $status_filter;
}

if ($assignment_type_filter) {
    $conditions[] = "iua.assignment_type = ?";
    $params[] = $assignment_type_filter;
}

$where_clause = implode(' AND ', $conditions);

// Get assignments
$assignments = $db->fetchAll("
    SELECT iua.*, 
           u.full_name as user_name, u.username, u.email as user_email, u.instagram_handle as user_instagram,
           i.full_name as influencer_name, i.instagram_handle as influencer_instagram, i.instagram_followers,
           c.title as admin_campaign_title, c.description as admin_campaign_description,
           bc.title as brand_campaign_title, bc.description as brand_campaign_description,
           b.company_name as brand_name,
           admin.full_name as approved_by_name
    FROM influencer_user_assignments iua
    JOIN users u ON iua.user_id = u.id
    JOIN influencers i ON iua.influencer_id = i.id
    LEFT JOIN campaigns c ON iua.campaign_id = c.id
    LEFT JOIN brand_campaigns bc ON iua.brand_campaign_id = bc.id
    LEFT JOIN brands b ON bc.brand_id = b.id
    LEFT JOIN admin ON iua.admin_approved_by = admin.id
    WHERE {$where_clause}
    ORDER BY iua.assigned_by_influencer_at DESC
", $params);

$page_title = 'Assignment Approvals';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-xl-2 sidebar p-0">
            <div class="p-4">
                <h4 class="text-white mb-4">
                    <i class="fas fa-crown me-2"></i>Admin Panel
                </h4>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a class="nav-link" href="users.php">
                        <i class="fas fa-users"></i>Users
                    </a>
                    <a class="nav-link" href="influencers.php">
                        <i class="fas fa-star"></i>Influencers
                    </a>
                    <a class="nav-link" href="campaigns.php">
                        <i class="fas fa-bullhorn"></i>Campaigns
                    </a>
                    <a class="nav-link" href="submissions.php">
                        <i class="fas fa-clipboard-check"></i>Submissions
                    </a>
                    <a class="nav-link active" href="assignments.php">
                        <i class="fas fa-user-check"></i>Assignments
                    </a>
                    <a class="nav-link" href="payouts.php">
                        <i class="fas fa-money-bill-wave"></i>Payouts
                    </a>
                    <a class="nav-link" href="brands.php">
                        <i class="fas fa-building"></i>Brands
                    </a>
                    <a class="nav-link" href="settings.php">
                        <i class="fas fa-cog"></i>Settings
                    </a>
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10 main-content" style="margin-left: auto;">
            <!-- Top Bar -->
            <div class="bg-white shadow-sm p-3 mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">Assignment Approvals</h4>
                        <small class="text-muted">Review and approve influencer-to-user campaign assignments</small>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-outline-primary d-lg-none" onclick="toggleSidebar()">
                            <i class="fas fa-bars"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="container-fluid py-4">
                <!-- Filters -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <form method="GET" class="row g-3">
                                    <div class="col-md-4">
                                        <label for="status" class="form-label">Status</label>
                                        <select class="form-select" id="status" name="status">
                                            <option value="pending_admin_approval" <?php echo $status_filter === 'pending_admin_approval' ? 'selected' : ''; ?>>Pending Approval</option>
                                            <option value="approved" <?php echo $status_filter === 'approved' ? 'selected' : ''; ?>>Approved</option>
                                            <option value="rejected" <?php echo $status_filter === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="assignment_type" class="form-label">Campaign Type</label>
                                        <select class="form-select" id="assignment_type" name="assignment_type">
                                            <option value="">All Types</option>
                                            <option value="admin_campaign" <?php echo $assignment_type_filter === 'admin_campaign' ? 'selected' : ''; ?>>Admin Campaign</option>
                                            <option value="brand_campaign" <?php echo $assignment_type_filter === 'brand_campaign' ? 'selected' : ''; ?>>Brand Campaign</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">&nbsp;</label>
                                        <div class="d-grid">
                                            <button type="submit" class="btn btn-outline-primary">
                                                <i class="fas fa-search me-1"></i>Filter
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Assignments List -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <?php if (empty($assignments)): ?>
                                    <div class="text-center py-5">
                                        <i class="fas fa-user-check fa-4x text-gray-300 mb-4"></i>
                                        <h4 class="text-muted">No assignments found</h4>
                                        <p class="text-muted">No assignments match your current filter criteria.</p>
                                    </div>
                                <?php else: ?>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Campaign</th>
                                                    <th>Influencer</th>
                                                    <th>User</th>
                                                    <th>Type</th>
                                                    <th>Reward</th>
                                                    <th>Status</th>
                                                    <th>Assigned</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($assignments as $assignment): ?>
                                                    <tr>
                                                        <td>
                                                            <div>
                                                                <strong>
                                                                    <?php 
                                                                    $campaign_title = $assignment['assignment_type'] === 'admin_campaign' ? 
                                                                        $assignment['admin_campaign_title'] : $assignment['brand_campaign_title'];
                                                                    echo htmlspecialchars($campaign_title);
                                                                    ?>
                                                                </strong>
                                                                <?php if ($assignment['assignment_type'] === 'brand_campaign' && $assignment['brand_name']): ?>
                                                                    <br><small class="text-muted">by <?php echo htmlspecialchars($assignment['brand_name']); ?></small>
                                                                <?php endif; ?>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div>
                                                                <strong><?php echo htmlspecialchars($assignment['influencer_name']); ?></strong>
                                                                <br>
                                                                <small class="text-muted">
                                                                    @<?php echo htmlspecialchars($assignment['influencer_instagram']); ?>
                                                                    (<?php echo number_format($assignment['instagram_followers']); ?> followers)
                                                                </small>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div>
                                                                <strong><?php echo htmlspecialchars($assignment['user_name']); ?></strong>
                                                                <br>
                                                                <small class="text-muted">@<?php echo htmlspecialchars($assignment['username']); ?></small>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-<?php echo $assignment['assignment_type'] === 'admin_campaign' ? 'primary' : 'info'; ?>">
                                                                <?php echo $assignment['assignment_type'] === 'admin_campaign' ? 'Admin' : 'Brand'; ?>
                                                            </span>
                                                        </td>
                                                        <td>₹<?php echo number_format($assignment['reward_amount'], 2); ?></td>
                                                        <td>
                                                            <span class="badge bg-<?php 
                                                                echo $assignment['status'] === 'approved' ? 'success' : 
                                                                    ($assignment['status'] === 'rejected' ? 'danger' : 'warning'); 
                                                            ?>">
                                                                <?php echo ucfirst(str_replace('_', ' ', $assignment['status'])); ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <small class="text-muted">
                                                                <?php echo time_ago($assignment['assigned_by_influencer_at']); ?>
                                                            </small>
                                                        </td>
                                                        <td>
                                                            <?php if ($assignment['status'] === 'pending_admin_approval'): ?>
                                                                <button class="btn btn-sm btn-success me-1" 
                                                                        data-bs-toggle="modal" 
                                                                        data-bs-target="#approveModal<?php echo $assignment['id']; ?>">
                                                                    <i class="fas fa-check"></i>
                                                                </button>
                                                                <button class="btn btn-sm btn-danger" 
                                                                        data-bs-toggle="modal" 
                                                                        data-bs-target="#rejectModal<?php echo $assignment['id']; ?>">
                                                                    <i class="fas fa-times"></i>
                                                                </button>
                                                            <?php else: ?>
                                                                <button class="btn btn-sm btn-outline-info" 
                                                                        data-bs-toggle="modal" 
                                                                        data-bs-target="#viewModal<?php echo $assignment['id']; ?>">
                                                                    <i class="fas fa-eye"></i>
                                                                </button>
                                                            <?php endif; ?>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modals for each assignment -->
<?php foreach ($assignments as $assignment): ?>
    <?php if ($assignment['status'] === 'pending_admin_approval'): ?>
        <!-- Approve Modal -->
        <div class="modal fade" id="approveModal<?php echo $assignment['id']; ?>" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Approve Assignment</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form method="POST">
                        <div class="modal-body">
                            <input type="hidden" name="assignment_id" value="<?php echo $assignment['id']; ?>">
                            
                            <div class="mb-3">
                                <strong>Campaign:</strong> 
                                <?php 
                                $campaign_title = $assignment['assignment_type'] === 'admin_campaign' ? 
                                    $assignment['admin_campaign_title'] : $assignment['brand_campaign_title'];
                                echo htmlspecialchars($campaign_title);
                                ?>
                            </div>
                            
                            <div class="mb-3">
                                <strong>Influencer:</strong> <?php echo htmlspecialchars($assignment['influencer_name']); ?>
                                (@<?php echo htmlspecialchars($assignment['influencer_instagram']); ?>)
                            </div>
                            
                            <div class="mb-3">
                                <strong>User:</strong> <?php echo htmlspecialchars($assignment['user_name']); ?>
                                (@<?php echo htmlspecialchars($assignment['username']); ?>)
                            </div>
                            
                            <div class="mb-3">
                                <strong>Reward:</strong> ₹<?php echo number_format($assignment['reward_amount'], 2); ?>
                            </div>
                            
                            <?php if ($assignment['influencer_notes']): ?>
                                <div class="mb-3">
                                    <strong>Influencer Notes:</strong>
                                    <p class="text-muted"><?php echo nl2br(htmlspecialchars($assignment['influencer_notes'])); ?></p>
                                </div>
                            <?php endif; ?>
                            
                            <div class="mb-3">
                                <label for="admin_notes" class="form-label">Admin Notes (Optional)</label>
                                <textarea class="form-control" name="admin_notes" rows="3" 
                                          placeholder="Add any notes for this approval..."></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" name="approve_assignment" class="btn btn-success">Approve Assignment</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Reject Modal -->
        <div class="modal fade" id="rejectModal<?php echo $assignment['id']; ?>" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Reject Assignment</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form method="POST">
                        <div class="modal-body">
                            <input type="hidden" name="assignment_id" value="<?php echo $assignment['id']; ?>">
                            
                            <p>Are you sure you want to reject this assignment?</p>
                            
                            <div class="mb-3">
                                <label for="admin_notes" class="form-label">Reason for Rejection *</label>
                                <textarea class="form-control" name="admin_notes" rows="3" required
                                          placeholder="Please provide a reason for rejecting this assignment..."></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" name="reject_assignment" class="btn btn-danger">Reject Assignment</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    <?php endif; ?>
<?php endforeach; ?>

<script>
function toggleSidebar() {
    document.querySelector('.sidebar').classList.toggle('show');
}
</script>

<?php include '../includes/footer.php'; ?>
