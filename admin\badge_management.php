<?php
require_once '../config.php';
require_once '../includes/badge_system.php';
require_login(['admin']);

$db = Database::getInstance();
$badge_system = new BadgeSystem();

$success = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'create_badge') {
        $name = trim($_POST['name'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $icon = trim($_POST['icon'] ?? '');
        $color = trim($_POST['color'] ?? '');
        $badge_type = trim($_POST['badge_type'] ?? '');
        
        if (empty($name) || empty($icon) || empty($color) || empty($badge_type)) {
            $error = 'Please fill in all required fields.';
        } else {
            if ($badge_system->createBadge($name, $description, $icon, $color, $badge_type)) {
                $success = 'Badge created successfully!';
            } else {
                $error = 'Failed to create badge.';
            }
        }
    } elseif ($action === 'update_badge') {
        $badge_id = intval($_POST['badge_id'] ?? 0);
        $name = trim($_POST['name'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $icon = trim($_POST['icon'] ?? '');
        $color = trim($_POST['color'] ?? '');
        $badge_type = trim($_POST['badge_type'] ?? '');
        $is_active = isset($_POST['is_active']) ? 1 : 0;
        
        if (empty($name) || empty($icon) || empty($color) || empty($badge_type)) {
            $error = 'Please fill in all required fields.';
        } else {
            if ($badge_system->updateBadge($badge_id, $name, $description, $icon, $color, $badge_type, $is_active)) {
                $success = 'Badge updated successfully!';
            } else {
                $error = 'Failed to update badge.';
            }
        }
    } elseif ($action === 'delete_badge') {
        $badge_id = intval($_POST['badge_id'] ?? 0);
        if ($badge_system->deleteBadge($badge_id)) {
            $success = 'Badge deleted successfully!';
        } else {
            $error = 'Failed to delete badge.';
        }
    }
}

// Get filter parameters
$filter_type = $_GET['type'] ?? 'all';
$search = trim($_GET['search'] ?? '');

// Get badges
if ($filter_type === 'all') {
    $badges = $badge_system->getAllBadges(false);
} else {
    $badges = $badge_system->getBadgesByType($filter_type, false);
}

// Apply search filter
if (!empty($search)) {
    $badges = array_filter($badges, function($badge) use ($search) {
        return stripos($badge['name'], $search) !== false || 
               stripos($badge['description'], $search) !== false;
    });
}

// Get statistics
$stats = $badge_system->getBadgeStats();

$page_title = 'Badge Management';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-xl-2 sidebar p-0">
            <div class="p-3">
                <h5 class="text-white mb-4">
                    <i class="fas fa-shield-alt me-2"></i>Admin Panel
                </h5>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a class="nav-link" href="users.php">
                        <i class="fas fa-users"></i>Users
                    </a>
                    <a class="nav-link" href="influencers.php">
                        <i class="fas fa-star"></i>Influencers
                    </a>
                    <a class="nav-link" href="brands.php">
                        <i class="fas fa-building"></i>Brands
                    </a>
                    <a class="nav-link" href="campaigns.php">
                        <i class="fas fa-bullhorn"></i>Campaigns
                    </a>
                    <a class="nav-link" href="instagram_verifications.php">
                        <i class="fab fa-instagram"></i>Instagram Verifications
                    </a>
                    <a class="nav-link" href="influencer_follow_check.php">
                        <i class="fas fa-user-check"></i>Follow Status Check
                    </a>
                    <a class="nav-link" href="instagram_blue_check.php">
                        <i class="fas fa-check-circle"></i>Blue Check Management
                    </a>
                    <a class="nav-link active" href="badge_management.php">
                        <i class="fas fa-award"></i>Badge Management
                    </a>
                    <a class="nav-link" href="user_badge_assignment.php">
                        <i class="fas fa-user-tag"></i>Assign Badges
                    </a>
                    <a class="nav-link" href="suspended_users.php">
                        <i class="fas fa-ban"></i>Suspended Users
                    </a>
                    <a class="nav-link" href="user_verification_status.php">
                        <i class="fas fa-clipboard-check"></i>User Follow Status
                    </a>
                    <a class="nav-link" href="payouts.php">
                        <i class="fas fa-money-bill-wave"></i>Payouts
                    </a>
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10 main-content" style="margin-left: auto;">
            <!-- Top Bar -->
            <div class="bg-white shadow-sm p-3 mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">Badge Management</h4>
                        <small class="text-muted">Create, edit, and manage platform badges</small>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createBadgeModal">
                            <i class="fas fa-plus me-2"></i>Create New Badge
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="p-3">
                <!-- Success/Error Messages -->
                <?php if ($success): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <i class="fas fa-award fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['total_badges']; ?></h3>
                                <p class="mb-0">Total Badges</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card stats-card success">
                            <div class="card-body text-center">
                                <i class="fas fa-user-tag fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['total_assignments']; ?></h3>
                                <p class="mb-0">Total Assignments</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card stats-card info">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['users_with_badges']; ?></h3>
                                <p class="mb-0">Users with Badges</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card stats-card warning">
                            <div class="card-body text-center">
                                <i class="fas fa-percentage fa-2x mb-2"></i>
                                <h3 class="fw-bold">
                                    <?php 
                                    $total_users = $db->fetch("SELECT COUNT(*) as count FROM (SELECT id FROM users UNION ALL SELECT id FROM influencers) as all_users")['count'];
                                    echo $total_users > 0 ? round(($stats['users_with_badges'] / $total_users) * 100) : 0; 
                                    ?>%
                                </h3>
                                <p class="mb-0">Badge Coverage</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Badge Type Statistics -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-pie me-2"></i>Badges by Type
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php foreach ($stats['by_type'] as $type_stat): ?>
                                <div class="col-md-2 col-sm-4 mb-2">
                                    <div class="text-center">
                                        <h5 class="mb-1"><?php echo $type_stat['count']; ?></h5>
                                        <small class="text-muted"><?php echo ucfirst($type_stat['badge_type']); ?></small>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>

                <!-- Filters and Search -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <label for="type" class="form-label">Filter by Type</label>
                                <select class="form-select" id="type" name="type">
                                    <option value="all" <?php echo $filter_type === 'all' ? 'selected' : ''; ?>>All Types</option>
                                    <?php foreach ($badge_system->getBadgeTypes() as $type => $label): ?>
                                        <option value="<?php echo $type; ?>" <?php echo $filter_type === $type ? 'selected' : ''; ?>>
                                            <?php echo $label; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="col-md-6">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" class="form-control" id="search" name="search"
                                       value="<?php echo htmlspecialchars($search); ?>"
                                       placeholder="Search by badge name or description">
                            </div>

                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-1"></i>Search
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Badges List -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>All Badges
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($badges)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-award fa-3x text-muted mb-3"></i>
                                <h6 class="text-muted">No badges found</h6>
                                <p class="text-muted">Create your first badge to get started.</p>
                                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createBadgeModal">
                                    <i class="fas fa-plus me-2"></i>Create Badge
                                </button>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Badge</th>
                                            <th>Type</th>
                                            <th>Description</th>
                                            <th>Assignments</th>
                                            <th>Status</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($badges as $badge): ?>
                                        <?php
                                        $assignments = $db->fetch("SELECT COUNT(*) as count FROM user_badges WHERE badge_id = ? AND is_active = 1", [$badge['id']])['count'];
                                        ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <span class="badge bg-<?php echo $badge['color']; ?> me-2">
                                                        <i class="<?php echo $badge['icon']; ?> me-1"></i>
                                                        <?php echo htmlspecialchars($badge['name']); ?>
                                                    </span>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary"><?php echo ucfirst($badge['badge_type']); ?></span>
                                            </td>
                                            <td>
                                                <small><?php echo htmlspecialchars($badge['description']); ?></small>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?php echo $assignments; ?> users</span>
                                            </td>
                                            <td>
                                                <?php if ($badge['is_active']): ?>
                                                    <span class="badge bg-success">Active</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">Inactive</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <small><?php echo date('M j, Y', strtotime($badge['created_at'])); ?></small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button class="btn btn-sm btn-outline-primary"
                                                            onclick="editBadge(<?php echo htmlspecialchars(json_encode($badge)); ?>)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>

                                                    <a href="user_badge_assignment.php?badge_id=<?php echo $badge['id']; ?>"
                                                       class="btn btn-sm btn-outline-success" title="Assign to Users">
                                                        <i class="fas fa-user-plus"></i>
                                                    </a>

                                                    <form method="POST" style="display: inline;"
                                                          onsubmit="return confirm('Are you sure you want to delete this badge? This will remove it from all users.')">
                                                        <input type="hidden" name="action" value="delete_badge">
                                                        <input type="hidden" name="badge_id" value="<?php echo $badge['id']; ?>">
                                                        <button type="submit" class="btn btn-sm btn-outline-danger">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Badge Modal -->
<div class="modal fade" id="createBadgeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Badge</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="create_badge">

                    <div class="mb-3">
                        <label for="create_name" class="form-label">Badge Name *</label>
                        <input type="text" class="form-control" id="create_name" name="name" required>
                    </div>

                    <div class="mb-3">
                        <label for="create_description" class="form-label">Description</label>
                        <textarea class="form-control" id="create_description" name="description" rows="3"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="create_icon" class="form-label">Icon Class *</label>
                            <input type="text" class="form-control" id="create_icon" name="icon"
                                   placeholder="fas fa-trophy" required>
                            <small class="form-text text-muted">FontAwesome icon class</small>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="create_color" class="form-label">Color *</label>
                            <select class="form-select" id="create_color" name="color" required>
                                <?php foreach ($badge_system->getBadgeColors() as $color => $label): ?>
                                    <option value="<?php echo $color; ?>"><?php echo $label; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="create_badge_type" class="form-label">Badge Type *</label>
                        <select class="form-select" id="create_badge_type" name="badge_type" required>
                            <?php foreach ($badge_system->getBadgeTypes() as $type => $label): ?>
                                <option value="<?php echo $type; ?>"><?php echo $label; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Preview</label>
                        <div id="create_preview">
                            <span class="badge bg-primary"><i class="fas fa-trophy me-1"></i>Badge Name</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Badge</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Badge Modal -->
<div class="modal fade" id="editBadgeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Badge</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_badge">
                    <input type="hidden" name="badge_id" id="edit_badge_id">

                    <div class="mb-3">
                        <label for="edit_name" class="form-label">Badge Name *</label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>

                    <div class="mb-3">
                        <label for="edit_description" class="form-label">Description</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_icon" class="form-label">Icon Class *</label>
                            <input type="text" class="form-control" id="edit_icon" name="icon" required>
                            <small class="form-text text-muted">FontAwesome icon class</small>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="edit_color" class="form-label">Color *</label>
                            <select class="form-select" id="edit_color" name="color" required>
                                <?php foreach ($badge_system->getBadgeColors() as $color => $label): ?>
                                    <option value="<?php echo $color; ?>"><?php echo $label; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_badge_type" class="form-label">Badge Type *</label>
                        <select class="form-select" id="edit_badge_type" name="badge_type" required>
                            <?php foreach ($badge_system->getBadgeTypes() as $type => $label): ?>
                                <option value="<?php echo $type; ?>"><?php echo $label; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active" checked>
                            <label class="form-check-label" for="edit_is_active">
                                Active Badge
                            </label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Preview</label>
                        <div id="edit_preview">
                            <span class="badge bg-primary"><i class="fas fa-trophy me-1"></i>Badge Name</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Badge</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Badge preview functionality
function updatePreview(prefix) {
    const name = document.getElementById(prefix + '_name').value || 'Badge Name';
    const icon = document.getElementById(prefix + '_icon').value || 'fas fa-trophy';
    const color = document.getElementById(prefix + '_color').value || 'primary';

    const preview = document.getElementById(prefix + '_preview');
    preview.innerHTML = `<span class="badge bg-${color}"><i class="${icon} me-1"></i>${name}</span>`;
}

// Edit badge function
function editBadge(badge) {
    document.getElementById('edit_badge_id').value = badge.id;
    document.getElementById('edit_name').value = badge.name;
    document.getElementById('edit_description').value = badge.description || '';
    document.getElementById('edit_icon').value = badge.icon;
    document.getElementById('edit_color').value = badge.color;
    document.getElementById('edit_badge_type').value = badge.badge_type;
    document.getElementById('edit_is_active').checked = badge.is_active == 1;

    updatePreview('edit');

    const modal = new bootstrap.Modal(document.getElementById('editBadgeModal'));
    modal.show();
}

// Add event listeners for preview updates
document.addEventListener('DOMContentLoaded', function() {
    // Create modal preview
    ['create_name', 'create_icon', 'create_color'].forEach(id => {
        document.getElementById(id).addEventListener('input', () => updatePreview('create'));
    });

    // Edit modal preview
    ['edit_name', 'edit_icon', 'edit_color'].forEach(id => {
        document.getElementById(id).addEventListener('input', () => updatePreview('edit'));
    });

    // Initial preview
    updatePreview('create');
});
</script>

<?php include '../includes/footer.php'; ?>
