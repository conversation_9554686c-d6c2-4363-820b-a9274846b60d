<?php
require_once '../config.php';
require_login(['admin']);

$db = Database::getInstance();

// Handle brand actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['create_brand'])) {
        $name = sanitize_input($_POST['name']);
        $description = sanitize_input($_POST['description']);
        $website = sanitize_input($_POST['website']);
        $contact_email = sanitize_input($_POST['contact_email']);
        $contact_phone = sanitize_input($_POST['contact_phone']);
        $budget = floatval($_POST['budget']);
        
        $errors = [];
        
        if (empty($name)) {
            $errors[] = 'Brand name is required.';
        }
        
        if ($contact_email && !filter_var($contact_email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Invalid email format.';
        }
        
        if (empty($errors)) {
            $data = [
                'name' => $name,
                'description' => $description,
                'website' => $website,
                'contact_email' => $contact_email,
                'contact_phone' => $contact_phone,
                'budget' => $budget
            ];
            
            // Handle logo upload
            if (isset($_FILES['logo']) && $_FILES['logo']['error'] === UPLOAD_ERR_OK) {
                try {
                    $logo_path = upload_file($_FILES['logo'], 'uploads/brands/');
                    $data['logo'] = $logo_path;
                } catch (Exception $e) {
                    $errors[] = 'Failed to upload logo: ' . $e->getMessage();
                }
            }
            
            if (empty($errors)) {
                try {
                    $db->insert('brands', $data);
                    $_SESSION['success'] = 'Brand created successfully!';
                    redirect('brands.php');
                } catch (Exception $e) {
                    $_SESSION['error'] = 'Failed to create brand.';
                }
            }
        }
        
        if (!empty($errors)) {
            $_SESSION['error'] = implode('<br>', $errors);
        }
    }
    
    if (isset($_POST['update_status'])) {
        $brand_id = intval($_POST['brand_id']);
        $status = sanitize_input($_POST['status']);
        
        try {
            $db->update('brands', ['status' => $status], 'id = ?', [$brand_id]);
            $_SESSION['success'] = 'Brand status updated successfully!';
        } catch (Exception $e) {
            $_SESSION['error'] = 'Failed to update brand status.';
        }
    }
    
    if (isset($_POST['delete_brand'])) {
        $brand_id = intval($_POST['brand_id']);
        
        try {
            // Check if brand has campaigns
            $campaigns = $db->fetch("SELECT COUNT(*) as count FROM campaigns WHERE brand_id = ?", [$brand_id])['count'];
            
            if ($campaigns > 0) {
                $_SESSION['error'] = 'Cannot delete brand with existing campaigns.';
            } else {
                $db->delete('brands', 'id = ?', [$brand_id]);
                $_SESSION['success'] = 'Brand deleted successfully!';
            }
        } catch (Exception $e) {
            $_SESSION['error'] = 'Failed to delete brand.';
        }
    }
}

// Get brands
$brands = $db->fetchAll("
    SELECT b.*, 
           COUNT(c.id) as campaign_count,
           SUM(c.total_budget) as total_spent
    FROM brands b
    LEFT JOIN campaigns c ON b.id = c.brand_id
    GROUP BY b.id
    ORDER BY b.created_at DESC
");

// Get statistics
$stats = $db->fetch("
    SELECT 
        COUNT(*) as total_brands,
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_brands,
        SUM(budget) as total_budget
    FROM brands
");

$page_title = 'Brand Management';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-xl-2 sidebar p-0">
            <div class="p-3">
                <h5 class="text-white mb-4">
                    <i class="fas fa-crown me-2"></i>Admin Panel
                </h5>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a class="nav-link" href="users.php">
                        <i class="fas fa-users"></i>Users
                    </a>
                    <a class="nav-link" href="influencers.php">
                        <i class="fas fa-star"></i>Influencers
                    </a>
                    <a class="nav-link" href="campaigns.php">
                        <i class="fas fa-bullhorn"></i>Campaigns
                    </a>
                    <a class="nav-link" href="submissions.php">
                        <i class="fas fa-clipboard-check"></i>Submissions
                    </a>
                    <a class="nav-link" href="payouts.php">
                        <i class="fas fa-money-bill-wave"></i>Payouts
                    </a>
                    <a class="nav-link active" href="brands.php">
                        <i class="fas fa-building"></i>Brands
                    </a>
                    <a class="nav-link" href="settings.php">
                        <i class="fas fa-cog"></i>Settings
                    </a>
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10 main-content" style="margin-left: auto;">
            <!-- Top Bar -->
            <div class="bg-white shadow-sm p-3 mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">Brand Management</h4>
                        <small class="text-muted">Manage advertising brands and partners</small>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createBrandModal">
                            <i class="fas fa-plus me-2"></i>Add Brand
                        </button>
                        <button class="btn btn-outline-primary d-lg-none ms-2" onclick="toggleSidebar()">
                            <i class="fas fa-bars"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="p-3">
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-lg-4 col-md-6 mb-3">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <i class="fas fa-building fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['total_brands']; ?></h3>
                                <p class="mb-0">Total Brands</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4 col-md-6 mb-3">
                        <div class="card stats-card success">
                            <div class="card-body text-center">
                                <i class="fas fa-check-circle fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['active_brands']; ?></h3>
                                <p class="mb-0">Active Brands</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4 col-md-6 mb-3">
                        <div class="card stats-card info">
                            <div class="card-body text-center">
                                <i class="fas fa-rupee-sign fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo format_currency($stats['total_budget'] ?? 0); ?></h3>
                                <p class="mb-0">Total Budget</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Brands Grid -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>All Brands (<?php echo count($brands); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($brands)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-building fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No brands found</h5>
                                <p class="text-muted">Add your first brand to get started with campaigns.</p>
                                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createBrandModal">
                                    <i class="fas fa-plus me-2"></i>Add Brand
                                </button>
                            </div>
                        <?php else: ?>
                            <div class="row">
                                <?php foreach ($brands as $brand): ?>
                                    <div class="col-lg-4 col-md-6 mb-4">
                                        <div class="card h-100 border-0 shadow-sm">
                                            <div class="card-header bg-white border-0 text-center">
                                                <?php if ($brand['logo']): ?>
                                                    <img src="<?php echo htmlspecialchars($brand['logo']); ?>" 
                                                         alt="<?php echo htmlspecialchars($brand['name']); ?>" 
                                                         class="rounded" style="width: 80px; height: 80px; object-fit: cover;">
                                                <?php else: ?>
                                                    <div class="bg-primary rounded d-inline-flex align-items-center justify-content-center" 
                                                         style="width: 80px; height: 80px;">
                                                        <i class="fas fa-building fa-2x text-white"></i>
                                                    </div>
                                                <?php endif; ?>
                                                <h6 class="mt-2 mb-0"><?php echo htmlspecialchars($brand['name']); ?></h6>
                                                <span class="badge bg-<?php echo $brand['status'] === 'active' ? 'success' : 'warning'; ?>">
                                                    <?php echo ucfirst($brand['status']); ?>
                                                </span>
                                            </div>
                                            
                                            <div class="card-body">
                                                <?php if ($brand['description']): ?>
                                                    <p class="text-muted small mb-3">
                                                        <?php echo substr(htmlspecialchars($brand['description']), 0, 100); ?>...
                                                    </p>
                                                <?php endif; ?>
                                                
                                                <div class="row text-center mb-3">
                                                    <div class="col-6">
                                                        <strong class="text-primary"><?php echo $brand['campaign_count']; ?></strong>
                                                        <small class="d-block text-muted">Campaigns</small>
                                                    </div>
                                                    <div class="col-6">
                                                        <strong class="text-success"><?php echo format_currency($brand['total_spent'] ?? 0); ?></strong>
                                                        <small class="d-block text-muted">Spent</small>
                                                    </div>
                                                </div>
                                                
                                                <?php if ($brand['website']): ?>
                                                    <div class="mb-2">
                                                        <small class="text-muted">Website:</small><br>
                                                        <a href="<?php echo htmlspecialchars($brand['website']); ?>" 
                                                           target="_blank" class="text-primary small">
                                                            <?php echo htmlspecialchars($brand['website']); ?>
                                                        </a>
                                                    </div>
                                                <?php endif; ?>
                                                
                                                <?php if ($brand['contact_email']): ?>
                                                    <div class="mb-2">
                                                        <small class="text-muted">Email:</small><br>
                                                        <a href="mailto:<?php echo htmlspecialchars($brand['contact_email']); ?>" 
                                                           class="text-primary small">
                                                            <?php echo htmlspecialchars($brand['contact_email']); ?>
                                                        </a>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            
                                            <div class="card-footer bg-white border-0">
                                                <div class="btn-group w-100" role="group">
                                                    <button class="btn btn-outline-primary btn-sm" 
                                                            data-bs-toggle="modal" 
                                                            data-bs-target="#brandModal<?php echo $brand['id']; ?>">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-warning btn-sm" 
                                                            data-bs-toggle="modal" 
                                                            data-bs-target="#statusModal<?php echo $brand['id']; ?>">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <?php if ($brand['campaign_count'] == 0): ?>
                                                        <button class="btn btn-outline-danger btn-sm delete-btn" 
                                                                data-bs-toggle="modal" 
                                                                data-bs-target="#deleteModal<?php echo $brand['id']; ?>">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Brand Modal -->
<div class="modal fade" id="createBrandModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Brand</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">Brand Name</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                            <div class="invalid-feedback">Please provide a brand name.</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="logo" class="form-label">Logo</label>
                            <input type="file" class="form-control file-input" id="logo" name="logo" accept="image/*">
                            <div class="file-preview mt-2"></div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="website" class="form-label">Website</label>
                            <input type="url" class="form-control" id="website" name="website" placeholder="https://example.com">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="budget" class="form-label">Budget (₹)</label>
                            <input type="number" class="form-control" id="budget" name="budget" min="0" step="0.01">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="contact_email" class="form-label">Contact Email</label>
                            <input type="email" class="form-control" id="contact_email" name="contact_email">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="contact_phone" class="form-label">Contact Phone</label>
                            <input type="tel" class="form-control" id="contact_phone" name="contact_phone">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="create_brand" class="btn btn-primary">Add Brand</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
