<?php
require_once '../config.php';
require_login(['admin']);

$db = Database::getInstance();

// Handle campaign actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['create_campaign'])) {
        $brand_id = intval($_POST['brand_id']) ?: null;
        $title = sanitize_input($_POST['title']);
        $description = sanitize_input($_POST['description']);
        $campaign_type = sanitize_input($_POST['campaign_type']);
        $target_audience = sanitize_input($_POST['target_audience']);
        $post_format = sanitize_input($_POST['post_format']);
        $reward_amount = floatval($_POST['reward_amount']);
        $total_budget = floatval($_POST['total_budget']);
        $max_participants = intval($_POST['max_participants']);
        $start_date = $_POST['start_date'];
        $end_date = $_POST['end_date'];
        $requirements = sanitize_input($_POST['requirements']);
        $hashtags = sanitize_input($_POST['hashtags']);

        $errors = [];

        if (empty($title) || empty($description) || empty($campaign_type) ||
            empty($reward_amount) || empty($start_date) || empty($end_date)) {
            $errors[] = 'All required fields must be filled.';
        }

        if ($reward_amount <= 0) {
            $errors[] = 'Reward amount must be greater than 0.';
        }

        if ($max_participants <= 0) {
            $errors[] = 'Maximum participants must be greater than 0.';
        }

        if (strtotime($end_date) <= strtotime($start_date)) {
            $errors[] = 'End date must be after start date.';
        }

        if (empty($errors)) {
            try {
                $campaign_id = $db->insert('campaigns', [
                    'brand_id' => $brand_id,
                    'title' => $title,
                    'description' => $description,
                    'campaign_type' => $campaign_type,
                    'target_audience' => $target_audience,
                    'post_format' => $post_format,
                    'reward_amount' => $reward_amount,
                    'total_budget' => $total_budget,
                    'max_participants' => $max_participants,
                    'start_date' => $start_date,
                    'end_date' => $end_date,
                    'requirements' => $requirements,
                    'hashtags' => $hashtags,
                    'created_by' => get_user_id(),
                    'status' => 'active'
                ]);

                $_SESSION['success'] = 'Campaign created successfully!';
                redirect('campaigns.php');
            } catch (Exception $e) {
                $_SESSION['error'] = 'Failed to create campaign.';
            }
        } else {
            $_SESSION['error'] = implode('<br>', $errors);
        }
    }

    if (isset($_POST['update_status'])) {
        $campaign_id = intval($_POST['campaign_id']);
        $status = sanitize_input($_POST['status']);

        try {
            $db->update('campaigns', ['status' => $status], 'id = ?', [$campaign_id]);
            $_SESSION['success'] = 'Campaign status updated successfully!';
        } catch (Exception $e) {
            $_SESSION['error'] = 'Failed to update campaign status.';
        }
    }

    if (isset($_POST['delete_campaign'])) {
        $campaign_id = intval($_POST['campaign_id']);

        try {
            // Check if campaign has participants
            $participants = $db->fetch("SELECT COUNT(*) as count FROM user_tasks WHERE campaign_id = ?", [$campaign_id])['count'];

            if ($participants > 0) {
                $_SESSION['error'] = 'Cannot delete campaign with active participants.';
            } else {
                $db->delete('campaigns', 'id = ?', [$campaign_id]);
                $_SESSION['success'] = 'Campaign deleted successfully!';
            }
        } catch (Exception $e) {
            $_SESSION['error'] = 'Failed to delete campaign.';
        }
    }
}

// Get filter parameters
$search = $_GET['search'] ?? '';
$status = $_GET['status'] ?? '';
$campaign_type = $_GET['campaign_type'] ?? '';
$brand_id = $_GET['brand_id'] ?? '';

// Build query
$conditions = ['1=1'];
$params = [];

if ($search) {
    $conditions[] = "(c.title LIKE ? OR c.description LIKE ?)";
    $search_param = "%{$search}%";
    $params[] = $search_param;
    $params[] = $search_param;
}

if ($status) {
    $conditions[] = "c.status = ?";
    $params[] = $status;
}

if ($campaign_type) {
    $conditions[] = "c.campaign_type = ?";
    $params[] = $campaign_type;
}

if ($brand_id) {
    $conditions[] = "c.brand_id = ?";
    $params[] = $brand_id;
}

$where_clause = implode(' AND ', $conditions);

// Get campaigns
$campaigns = $db->fetchAll("
    SELECT c.*, b.company_name as brand_name, b.logo as brand_logo,
           a.full_name as created_by_name,
           (c.max_participants - c.current_participants) as slots_remaining
    FROM campaigns c
    LEFT JOIN brands b ON c.brand_id = b.id
    LEFT JOIN admin a ON c.created_by = a.id
    WHERE {$where_clause}
    ORDER BY c.created_at DESC
", $params);

// Get brands for dropdown
$brands = $db->fetchAll("SELECT id, company_name as name FROM brands WHERE status = 'active' ORDER BY company_name");

// Get statistics
$stats = $db->fetch("
    SELECT
        COUNT(*) as total_campaigns,
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_campaigns,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_campaigns,
        SUM(CASE WHEN status = 'paused' THEN 1 ELSE 0 END) as paused_campaigns,
        SUM(total_budget) as total_budget,
        SUM(current_participants) as total_participants
    FROM campaigns
");

$page_title = 'Campaign Management';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-xl-2 sidebar p-0">
            <div class="p-3">
                <h5 class="text-white mb-4">
                    <i class="fas fa-crown me-2"></i>Admin Panel
                </h5>

                <nav class="nav flex-column">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a class="nav-link" href="users.php">
                        <i class="fas fa-users"></i>Users
                    </a>
                    <a class="nav-link" href="influencers.php">
                        <i class="fas fa-star"></i>Influencers
                    </a>
                    <a class="nav-link active" href="campaigns.php">
                        <i class="fas fa-bullhorn"></i>Campaigns
                    </a>
                    <a class="nav-link" href="submissions.php">
                        <i class="fas fa-clipboard-check"></i>Submissions
                    </a>
                    <a class="nav-link" href="payouts.php">
                        <i class="fas fa-money-bill-wave"></i>Payouts
                    </a>
                    <a class="nav-link" href="brands.php">
                        <i class="fas fa-building"></i>Brands
                    </a>
                    <a class="nav-link" href="settings.php">
                        <i class="fas fa-cog"></i>Settings
                    </a>
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </nav>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10 main-content" style="margin-left: auto;">
            <!-- Top Bar -->
            <div class="bg-white shadow-sm p-3 mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">Campaign Management</h4>
                        <small class="text-muted">Create and manage advertising campaigns</small>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createCampaignModal">
                            <i class="fas fa-plus me-2"></i>Create Campaign
                        </button>
                        <button class="btn btn-outline-primary d-lg-none ms-2" onclick="toggleSidebar()">
                            <i class="fas fa-bars"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="p-3">
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <i class="fas fa-bullhorn fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['total_campaigns']; ?></h3>
                                <p class="mb-0">Total Campaigns</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card success">
                            <div class="card-body text-center">
                                <i class="fas fa-play-circle fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['active_campaigns']; ?></h3>
                                <p class="mb-0">Active Campaigns</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card warning">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['total_participants']; ?></h3>
                                <p class="mb-0">Total Participants</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card info">
                            <div class="card-body text-center">
                                <i class="fas fa-rupee-sign fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo format_currency($stats['total_budget'] ?? 0); ?></h3>
                                <p class="mb-0">Total Budget</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-filter me-2"></i>Filter Campaigns
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-3">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" class="form-control" id="search" name="search"
                                       value="<?php echo htmlspecialchars($search); ?>"
                                       placeholder="Campaign title or description...">
                            </div>

                            <div class="col-md-2">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">All Status</option>
                                    <option value="draft" <?php echo $status === 'draft' ? 'selected' : ''; ?>>Draft</option>
                                    <option value="active" <?php echo $status === 'active' ? 'selected' : ''; ?>>Active</option>
                                    <option value="paused" <?php echo $status === 'paused' ? 'selected' : ''; ?>>Paused</option>
                                    <option value="completed" <?php echo $status === 'completed' ? 'selected' : ''; ?>>Completed</option>
                                    <option value="cancelled" <?php echo $status === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                                </select>
                            </div>

                            <div class="col-md-2">
                                <label for="campaign_type" class="form-label">Type</label>
                                <select class="form-select" id="campaign_type" name="campaign_type">
                                    <option value="">All Types</option>
                                    <option value="user_task" <?php echo $campaign_type === 'user_task' ? 'selected' : ''; ?>>User Task</option>
                                    <option value="influencer_campaign" <?php echo $campaign_type === 'influencer_campaign' ? 'selected' : ''; ?>>Influencer Campaign</option>
                                    <option value="both" <?php echo $campaign_type === 'both' ? 'selected' : ''; ?>>Both</option>
                                </select>
                            </div>

                            <div class="col-md-3">
                                <label for="brand_id" class="form-label">Brand</label>
                                <select class="form-select" id="brand_id" name="brand_id">
                                    <option value="">All Brands</option>
                                    <?php foreach ($brands as $brand): ?>
                                        <option value="<?php echo $brand['id']; ?>"
                                                <?php echo $brand_id == $brand['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($brand['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                <a href="campaigns.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Campaigns Table -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>All Campaigns (<?php echo count($campaigns); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($campaigns)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-bullhorn fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No campaigns found</h5>
                                <p class="text-muted">Create your first campaign to get started.</p>
                                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createCampaignModal">
                                    <i class="fas fa-plus me-2"></i>Create Campaign
                                </button>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Campaign</th>
                                            <th>Brand</th>
                                            <th>Type</th>
                                            <th>Participants</th>
                                            <th>Budget</th>
                                            <th>Status</th>
                                            <th>Dates</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($campaigns as $campaign): ?>
                                            <tr>
                                                <td>
                                                    <div>
                                                        <strong><?php echo htmlspecialchars($campaign['title']); ?></strong>
                                                        <br><small class="text-muted"><?php echo substr(htmlspecialchars($campaign['description']), 0, 60); ?>...</small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <?php if ($campaign['brand_logo']): ?>
                                                            <img src="<?php echo htmlspecialchars($campaign['brand_logo']); ?>"
                                                                 alt="Brand Logo" class="rounded me-2"
                                                                 style="width: 30px; height: 30px; object-fit: cover;">
                                                        <?php endif; ?>
                                                        <span><?php echo htmlspecialchars($campaign['brand_name'] ?? 'RealEarners'); ?></span>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge bg-info">
                                                        <?php echo ucfirst(str_replace('_', ' ', $campaign['campaign_type'])); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="text-center">
                                                        <strong><?php echo $campaign['current_participants']; ?></strong> / <?php echo $campaign['max_participants']; ?>
                                                        <br><small class="text-muted"><?php echo $campaign['slots_remaining']; ?> slots left</small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="text-center">
                                                        <strong class="text-success"><?php echo format_currency($campaign['reward_amount']); ?></strong>
                                                        <br><small class="text-muted">per task</small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <?php
                                                    $status_class = '';
                                                    switch ($campaign['status']) {
                                                        case 'active':
                                                            $status_class = 'bg-success';
                                                            break;
                                                        case 'draft':
                                                            $status_class = 'bg-secondary';
                                                            break;
                                                        case 'paused':
                                                            $status_class = 'bg-warning';
                                                            break;
                                                        case 'completed':
                                                            $status_class = 'bg-primary';
                                                            break;
                                                        case 'cancelled':
                                                            $status_class = 'bg-danger';
                                                            break;
                                                    }
                                                    ?>
                                                    <span class="badge <?php echo $status_class; ?>">
                                                        <?php echo ucfirst($campaign['status']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <small>
                                                        <strong>Start:</strong> <?php echo date('M j', strtotime($campaign['start_date'])); ?><br>
                                                        <strong>End:</strong> <?php echo date('M j', strtotime($campaign['end_date'])); ?>
                                                    </small>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <button class="btn btn-sm btn-outline-primary"
                                                                data-bs-toggle="modal"
                                                                data-bs-target="#campaignModal<?php echo $campaign['id']; ?>">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-warning"
                                                                data-bs-toggle="modal"
                                                                data-bs-target="#statusModal<?php echo $campaign['id']; ?>">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <?php if ($campaign['current_participants'] == 0): ?>
                                                            <button class="btn btn-sm btn-outline-danger delete-btn"
                                                                    data-bs-toggle="modal"
                                                                    data-bs-target="#deleteModal<?php echo $campaign['id']; ?>">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Campaign Modal -->
<div class="modal fade" id="createCampaignModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Campaign</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" class="needs-validation" novalidate>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="title" class="form-label">Campaign Title</label>
                            <input type="text" class="form-control" id="title" name="title" required>
                            <div class="invalid-feedback">Please provide a campaign title.</div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="brand_id" class="form-label">Brand</label>
                            <select class="form-select" id="brand_id" name="brand_id">
                                <option value="">RealEarners (Platform)</option>
                                <?php foreach ($brands as $brand): ?>
                                    <option value="<?php echo $brand['id']; ?>">
                                        <?php echo htmlspecialchars($brand['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3" required></textarea>
                        <div class="invalid-feedback">Please provide a campaign description.</div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="campaign_type" class="form-label">Campaign Type</label>
                            <select class="form-select" id="campaign_type" name="campaign_type" required>
                                <option value="">Select Type</option>
                                <option value="user_task">User Task Only</option>
                                <option value="influencer_campaign">Influencer Campaign Only</option>
                                <option value="both">Both Users & Influencers</option>
                            </select>
                            <div class="invalid-feedback">Please select a campaign type.</div>
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="post_format" class="form-label">Post Format</label>
                            <select class="form-select" id="post_format" name="post_format" required>
                                <option value="">Select Format</option>
                                <option value="post">Instagram Post</option>
                                <option value="story">Instagram Story</option>
                                <option value="reel">Instagram Reel</option>
                                <option value="any">Any Format</option>
                            </select>
                            <div class="invalid-feedback">Please select a post format.</div>
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="target_audience" class="form-label">Target Audience</label>
                            <input type="text" class="form-control" id="target_audience" name="target_audience"
                                   placeholder="e.g., Students, Youth, Professionals">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="reward_amount" class="form-label">Reward Amount (₹)</label>
                            <input type="number" class="form-control" id="reward_amount" name="reward_amount"
                                   min="1" step="0.01" required>
                            <div class="invalid-feedback">Please provide a reward amount.</div>
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="max_participants" class="form-label">Max Participants</label>
                            <input type="number" class="form-control" id="max_participants" name="max_participants"
                                   min="1" required>
                            <div class="invalid-feedback">Please provide maximum participants.</div>
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="total_budget" class="form-label">Total Budget (₹)</label>
                            <input type="number" class="form-control" id="total_budget" name="total_budget"
                                   min="1" step="0.01" readonly>
                            <div class="form-text">Auto-calculated: Reward × Max Participants</div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="start_date" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="start_date" name="start_date"
                                   min="<?php echo date('Y-m-d'); ?>" required>
                            <div class="invalid-feedback">Please provide a start date.</div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="end_date" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" required>
                            <div class="invalid-feedback">Please provide an end date.</div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="requirements" class="form-label">Requirements</label>
                        <textarea class="form-control" id="requirements" name="requirements" rows="3"
                                  placeholder="Specific requirements for participants..."></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="hashtags" class="form-label">Required Hashtags</label>
                        <input type="text" class="form-control" id="hashtags" name="hashtags"
                               placeholder="#hashtag1 #hashtag2 #hashtag3">
                        <div class="form-text">Hashtags that participants must include in their posts</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="create_campaign" class="btn btn-primary">Create Campaign</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Auto-calculate total budget
document.getElementById('reward_amount').addEventListener('input', calculateBudget);
document.getElementById('max_participants').addEventListener('input', calculateBudget);

function calculateBudget() {
    const reward = parseFloat(document.getElementById('reward_amount').value) || 0;
    const participants = parseInt(document.getElementById('max_participants').value) || 0;
    const total = reward * participants;
    document.getElementById('total_budget').value = total.toFixed(2);
}

// Set minimum end date
document.getElementById('start_date').addEventListener('change', function() {
    const startDate = this.value;
    const endDateInput = document.getElementById('end_date');
    endDateInput.min = startDate;

    if (endDateInput.value && endDateInput.value <= startDate) {
        endDateInput.value = '';
    }
});
</script>

<?php include '../includes/footer.php'; ?>