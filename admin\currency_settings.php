<?php
require_once '../config.php';
require_once '../includes/auth_check.php';
require_once '../includes/settings_manager.php';
require_once '../includes/currency_helper.php';

// Check if user is admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: ../auth/login.php');
    exit();
}

$settings = SettingsManager::getInstance();
$message = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['update_currency'])) {
        $currency_settings = [
            'currency_code' => $_POST['currency_code'],
            'currency_symbol' => $_POST['currency_symbol'],
            'currency_name' => $_POST['currency_name'],
            'currency_position' => $_POST['currency_position'],
            'decimal_places' => (int)$_POST['decimal_places'],
            'thousand_separator' => $_POST['thousand_separator'],
            'decimal_separator' => $_POST['decimal_separator'],
            'exchange_rate_usd' => (float)$_POST['exchange_rate_usd'],
            'min_payout_amount' => (float)$_POST['min_payout_amount'],
            'default_task_reward' => (float)$_POST['default_task_reward'],
            'platform_commission' => (float)$_POST['platform_commission'],
            'payment_gateway' => $_POST['payment_gateway'],
            'enable_international_payments' => isset($_POST['enable_international_payments']) ? 1 : 0
        ];
        
        if ($settings->updateCurrencySettings($currency_settings)) {
            $message = 'Currency settings updated successfully!';
        } else {
            $error = 'Failed to update currency settings.';
        }
    }
    
    if (isset($_POST['add_currency'])) {
        $currency_data = [
            'currency_code' => strtoupper($_POST['new_currency_code']),
            'currency_name' => $_POST['new_currency_name'],
            'currency_symbol' => $_POST['new_currency_symbol'],
            'symbol_position' => $_POST['new_symbol_position'],
            'decimal_places' => (int)$_POST['new_decimal_places'],
            'thousand_separator' => $_POST['new_thousand_separator'],
            'decimal_separator' => $_POST['new_decimal_separator'],
            'exchange_rate_usd' => (float)$_POST['new_exchange_rate_usd'],
            'is_active' => 1,
            'is_default' => 0,
            'country_code' => strtoupper($_POST['new_country_code'])
        ];
        
        CurrencyHelper::updateCurrency($currency_data);
        $message = 'New currency added successfully!';
    }
    
    if (isset($_POST['set_default_currency'])) {
        CurrencyHelper::setDefaultCurrency($_POST['default_currency_code']);
        $message = 'Default currency updated successfully!';
    }
}

// Get current settings
$current_settings = $settings->getCurrencySettings();
$all_currencies = CurrencyHelper::getAllCurrencies();

$page_title = 'Currency Settings';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-xl-2 sidebar p-0">
            <?php include '../includes/admin_sidebar.php'; ?>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10 main-content">
            <!-- Top Bar -->
            <div class="bg-white shadow-sm p-3 mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">Currency Settings</h4>
                        <small class="text-muted">Manage platform currency and payment settings</small>
                    </div>
                </div>
            </div>
            
            <div class="p-3">
                <?php if ($message): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo htmlspecialchars($error); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <!-- Current Currency Settings -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-money-bill-wave me-2"></i>Primary Currency Settings
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="currency_code" class="form-label">Currency Code</label>
                                        <input type="text" class="form-control" id="currency_code" name="currency_code" 
                                               value="<?php echo htmlspecialchars($current_settings['currency_code']); ?>" 
                                               maxlength="3" required>
                                        <div class="form-text">ISO 4217 currency code (e.g., INR, USD, EUR)</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="currency_symbol" class="form-label">Currency Symbol</label>
                                        <input type="text" class="form-control" id="currency_symbol" name="currency_symbol" 
                                               value="<?php echo htmlspecialchars($current_settings['currency_symbol']); ?>" 
                                               maxlength="10" required>
                                        <div class="form-text">Symbol to display (e.g., ₹, $, €)</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="currency_name" class="form-label">Currency Name</label>
                                        <input type="text" class="form-control" id="currency_name" name="currency_name" 
                                               value="<?php echo htmlspecialchars($current_settings['currency_name']); ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="currency_position" class="form-label">Symbol Position</label>
                                        <select class="form-select" id="currency_position" name="currency_position" required>
                                            <option value="before" <?php echo $current_settings['currency_position'] === 'before' ? 'selected' : ''; ?>>Before Amount (₹100)</option>
                                            <option value="after" <?php echo $current_settings['currency_position'] === 'after' ? 'selected' : ''; ?>>After Amount (100₹)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="decimal_places" class="form-label">Decimal Places</label>
                                        <input type="number" class="form-control" id="decimal_places" name="decimal_places" 
                                               value="<?php echo $current_settings['decimal_places']; ?>" min="0" max="4" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="thousand_separator" class="form-label">Thousand Separator</label>
                                        <input type="text" class="form-control" id="thousand_separator" name="thousand_separator" 
                                               value="<?php echo htmlspecialchars($current_settings['thousand_separator']); ?>" 
                                               maxlength="1" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="decimal_separator" class="form-label">Decimal Separator</label>
                                        <input type="text" class="form-control" id="decimal_separator" name="decimal_separator" 
                                               value="<?php echo htmlspecialchars($current_settings['decimal_separator']); ?>" 
                                               maxlength="1" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="exchange_rate_usd" class="form-label">Exchange Rate to USD</label>
                                        <input type="number" class="form-control" id="exchange_rate_usd" name="exchange_rate_usd" 
                                               value="<?php echo $current_settings['exchange_rate_usd']; ?>" 
                                               step="0.0001" min="0" required>
                                        <div class="form-text">1 USD = ? <?php echo $current_settings['currency_code']; ?></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="payment_gateway" class="form-label">Primary Payment Gateway</label>
                                        <select class="form-select" id="payment_gateway" name="payment_gateway" required>
                                            <option value="razorpay" <?php echo $current_settings['payment_gateway'] === 'razorpay' ? 'selected' : ''; ?>>Razorpay</option>
                                            <option value="upi" <?php echo $current_settings['payment_gateway'] === 'upi' ? 'selected' : ''; ?>>UPI Direct</option>
                                            <option value="paytm" <?php echo $current_settings['payment_gateway'] === 'paytm' ? 'selected' : ''; ?>>Paytm</option>
                                            <option value="paypal" <?php echo $current_settings['payment_gateway'] === 'paypal' ? 'selected' : ''; ?>>PayPal</option>
                                            <option value="stripe" <?php echo $current_settings['payment_gateway'] === 'stripe' ? 'selected' : ''; ?>>Stripe</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <hr>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="min_payout_amount" class="form-label">Minimum Payout Amount</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><?php echo $current_settings['currency_symbol']; ?></span>
                                            <input type="number" class="form-control" id="min_payout_amount" name="min_payout_amount" 
                                                   value="<?php echo $current_settings['min_payout_amount']; ?>" 
                                                   step="0.01" min="1" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="default_task_reward" class="form-label">Default Task Reward</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><?php echo $current_settings['currency_symbol']; ?></span>
                                            <input type="number" class="form-control" id="default_task_reward" name="default_task_reward" 
                                                   value="<?php echo $current_settings['default_task_reward']; ?>" 
                                                   step="0.01" min="1" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="platform_commission" class="form-label">Platform Commission (%)</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="platform_commission" name="platform_commission" 
                                                   value="<?php echo $current_settings['platform_commission']; ?>" 
                                                   step="0.01" min="0" max="50" required>
                                            <span class="input-group-text">%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enable_international_payments" 
                                           name="enable_international_payments" value="1" 
                                           <?php echo $current_settings['enable_international_payments'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="enable_international_payments">
                                        Enable International Payments
                                    </label>
                                    <div class="form-text">Allow payments in multiple currencies</div>
                                </div>
                            </div>
                            
                            <button type="submit" name="update_currency" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Currency Settings
                            </button>
                        </form>
                    </div>
                </div>
                
                <!-- Preview -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-eye me-2"></i>Currency Preview
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center p-3 bg-light rounded">
                                    <h6>Small Amount</h6>
                                    <h4 class="text-primary"><?php echo format_currency(50); ?></h4>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-3 bg-light rounded">
                                    <h6>Medium Amount</h6>
                                    <h4 class="text-success"><?php echo format_currency(1250.75); ?></h4>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-3 bg-light rounded">
                                    <h6>Large Amount</h6>
                                    <h4 class="text-warning"><?php echo format_currency(125000); ?></h4>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-3 bg-light rounded">
                                    <h6>Very Large Amount</h6>
                                    <h4 class="text-info"><?php echo format_currency_short(12500000); ?></h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
