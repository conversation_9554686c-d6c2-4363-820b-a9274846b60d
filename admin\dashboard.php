<?php
require_once '../config.php';
require_login(['admin']);

$db = Database::getInstance();

// Get dashboard statistics
$stats = [
    'total_users' => $db->fetch("SELECT COUNT(*) as count FROM users")['count'],
    'total_influencers' => $db->fetch("SELECT COUNT(*) as count FROM influencers")['count'],
    'active_campaigns' => $db->fetch("SELECT COUNT(*) as count FROM campaigns WHERE status = 'active'")['count'],
    'pending_submissions' => $db->fetch("SELECT COUNT(*) as count FROM user_tasks WHERE status = 'submitted'")['count'],
    'pending_payouts' => $db->fetch("SELECT COUNT(*) as count FROM payouts WHERE status = 'pending'")['count'],
    'total_earnings' => $db->fetch("SELECT SUM(total_earned) as total FROM users")['total'] ?? 0,
    'monthly_signups' => $db->fetch("
        SELECT COUNT(*) as count FROM users 
        WHERE MONTH(created_at) = MONTH(CURRENT_DATE()) 
        AND YEAR(created_at) = YEAR(CURRENT_DATE())
    ")['count']
];

// Get recent activities
$recent_users = $db->fetchAll("
    SELECT id, username, full_name, created_at 
    FROM users 
    ORDER BY created_at DESC 
    LIMIT 5
");

$recent_submissions = $db->fetchAll("
    SELECT ut.*, u.username, c.title as campaign_title
    FROM user_tasks ut
    JOIN users u ON ut.user_id = u.id
    JOIN campaigns c ON ut.campaign_id = c.id
    WHERE ut.status = 'submitted'
    ORDER BY ut.submitted_at DESC
    LIMIT 5
");

$recent_payouts = $db->fetchAll("
    SELECT p.*, u.username, u.full_name
    FROM payouts p
    JOIN users u ON p.user_id = u.id
    WHERE p.user_type = 'user' AND p.status = 'pending'
    ORDER BY p.created_at DESC
    LIMIT 5
");

// Get pending influencer assignments
$pending_assignments = $db->fetchAll("
    SELECT iua.*, u.full_name as user_name, u.username,
           i.full_name as influencer_name, i.instagram_handle,
           c.title as admin_campaign_title,
           bc.title as brand_campaign_title
    FROM influencer_user_assignments iua
    JOIN users u ON iua.user_id = u.id
    JOIN influencers i ON iua.influencer_id = i.id
    LEFT JOIN campaigns c ON iua.campaign_id = c.id
    LEFT JOIN brand_campaigns bc ON iua.brand_campaign_id = bc.id
    WHERE iua.status = 'pending_admin_approval'
    ORDER BY iua.assigned_by_influencer_at DESC
    LIMIT 5
");

$page_title = 'Admin Dashboard';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-xl-2 sidebar p-0">
            <div class="p-3">
                <h5 class="text-white mb-4">
                    <i class="fas fa-crown me-2"></i>Admin Panel
                </h5>
                
                <nav class="nav flex-column">
                    <a class="nav-link active" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a class="nav-link" href="users.php">
                        <i class="fas fa-users"></i>Users
                    </a>
                    <a class="nav-link" href="influencers.php">
                        <i class="fas fa-star"></i>Influencers
                    </a>
                    <a class="nav-link" href="campaigns.php">
                        <i class="fas fa-bullhorn"></i>Campaigns
                    </a>
                    <a class="nav-link" href="submissions.php">
                        <i class="fas fa-clipboard-check"></i>Submissions
                    </a>
                    <a class="nav-link" href="payouts.php">
                        <i class="fas fa-money-bill-wave"></i>Payouts
                    </a>
                    <a class="nav-link" href="brands.php">
                        <i class="fas fa-building"></i>Brands
                    </a>
                    <a class="nav-link" href="settings.php">
                        <i class="fas fa-cog"></i>Settings
                    </a>
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10 main-content" style="margin-left: auto;">
            <!-- Top Bar -->
            <div class="bg-white shadow-sm p-3 mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">Admin Dashboard</h4>
                        <small class="text-muted">Welcome back, <?php echo htmlspecialchars($_SESSION['full_name']); ?>!</small>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-outline-primary d-lg-none" onclick="toggleSidebar()">
                            <i class="fas fa-bars"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="p-3">
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['total_users']; ?></h3>
                                <p class="mb-0">Total Users</p>
                                <small class="text-light">+<?php echo $stats['monthly_signups']; ?> this month</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card success">
                            <div class="card-body text-center">
                                <i class="fas fa-star fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['total_influencers']; ?></h3>
                                <p class="mb-0">Influencers</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card warning">
                            <div class="card-body text-center">
                                <i class="fas fa-bullhorn fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['active_campaigns']; ?></h3>
                                <p class="mb-0">Active Campaigns</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card info">
                            <div class="card-body text-center">
                                <i class="fas fa-rupee-sign fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo format_currency($stats['total_earnings']); ?></h3>
                                <p class="mb-0">Total Paid</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-bolt me-2"></i>Quick Actions
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3 mb-2">
                                        <a href="campaigns.php?action=create" class="btn btn-primary w-100">
                                            <i class="fas fa-plus me-2"></i>New Campaign
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <a href="submissions.php" class="btn btn-warning w-100">
                                            <i class="fas fa-clipboard-check me-2"></i>Review Submissions
                                            <?php if ($stats['pending_submissions'] > 0): ?>
                                                <span class="badge bg-danger ms-1"><?php echo $stats['pending_submissions']; ?></span>
                                            <?php endif; ?>
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <a href="payouts.php" class="btn btn-success w-100">
                                            <i class="fas fa-money-bill-wave me-2"></i>Process Payouts
                                            <?php if ($stats['pending_payouts'] > 0): ?>
                                                <span class="badge bg-danger ms-1"><?php echo $stats['pending_payouts']; ?></span>
                                            <?php endif; ?>
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <a href="assignments.php" class="btn btn-info w-100">
                                            <i class="fas fa-user-check me-2"></i>Approve Assignments
                                            <?php if (count($pending_assignments) > 0): ?>
                                                <span class="badge bg-danger ms-1"><?php echo count($pending_assignments); ?></span>
                                            <?php endif; ?>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <!-- Recent Users -->
                    <div class="col-lg-4 mb-4">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-user-plus me-2"></i>Recent Users
                                </h5>
                                <a href="users.php" class="btn btn-sm btn-outline-primary">View All</a>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recent_users)): ?>
                                    <div class="text-center py-3">
                                        <i class="fas fa-users fa-2x text-muted mb-2"></i>
                                        <p class="text-muted small">No users yet</p>
                                    </div>
                                <?php else: ?>
                                    <?php foreach ($recent_users as $user): ?>
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="flex-shrink-0">
                                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" 
                                                     style="width: 40px; height: 40px;">
                                                    <i class="fas fa-user text-white"></i>
                                                </div>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <h6 class="mb-1"><?php echo htmlspecialchars($user['full_name']); ?></h6>
                                                <p class="mb-0 small text-muted">
                                                    @<?php echo htmlspecialchars($user['username']); ?> • 
                                                    <?php echo time_ago($user['created_at']); ?>
                                                </p>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Pending Submissions -->
                    <div class="col-lg-4 mb-4">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-clock me-2"></i>Pending Reviews
                                </h5>
                                <a href="submissions.php" class="btn btn-sm btn-outline-warning">Review All</a>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recent_submissions)): ?>
                                    <div class="text-center py-3">
                                        <i class="fas fa-clipboard-check fa-2x text-muted mb-2"></i>
                                        <p class="text-muted small">No pending submissions</p>
                                    </div>
                                <?php else: ?>
                                    <?php foreach ($recent_submissions as $submission): ?>
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="flex-shrink-0">
                                                <div class="bg-warning rounded-circle d-flex align-items-center justify-content-center" 
                                                     style="width: 40px; height: 40px;">
                                                    <i class="fas fa-upload text-white"></i>
                                                </div>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <h6 class="mb-1 small"><?php echo htmlspecialchars($submission['campaign_title']); ?></h6>
                                                <p class="mb-0 small text-muted">
                                                    by @<?php echo htmlspecialchars($submission['username']); ?> • 
                                                    <?php echo time_ago($submission['submitted_at']); ?>
                                                </p>
                                            </div>
                                            <div class="flex-shrink-0">
                                                <a href="submissions.php?task=<?php echo $submission['id']; ?>" 
                                                   class="btn btn-sm btn-outline-primary">Review</a>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Pending Assignments -->
                    <div class="col-lg-4 mb-4">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-user-check me-2"></i>Pending Assignments
                                </h5>
                                <a href="assignments.php" class="btn btn-sm btn-outline-info">Approve All</a>
                            </div>
                            <div class="card-body">
                                <?php if (empty($pending_assignments)): ?>
                                    <div class="text-center py-3">
                                        <i class="fas fa-user-check fa-2x text-muted mb-2"></i>
                                        <p class="text-muted small">No pending assignments</p>
                                    </div>
                                <?php else: ?>
                                    <?php foreach ($pending_assignments as $assignment): ?>
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="flex-shrink-0">
                                                <div class="bg-info rounded-circle d-flex align-items-center justify-content-center"
                                                     style="width: 40px; height: 40px;">
                                                    <i class="fas fa-user-plus text-white"></i>
                                                </div>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <h6 class="mb-1 small">
                                                    <?php
                                                    $campaign_title = $assignment['assignment_type'] === 'admin_campaign' ?
                                                        $assignment['admin_campaign_title'] : $assignment['brand_campaign_title'];
                                                    echo htmlspecialchars($campaign_title);
                                                    ?>
                                                </h6>
                                                <p class="mb-0 small text-muted">
                                                    <?php echo htmlspecialchars($assignment['influencer_name']); ?> →
                                                    <?php echo htmlspecialchars($assignment['user_name']); ?> •
                                                    ₹<?php echo number_format($assignment['reward_amount'], 0); ?>
                                                </p>
                                            </div>
                                            <div class="flex-shrink-0">
                                                <a href="assignments.php?assignment=<?php echo $assignment['id']; ?>"
                                                   class="btn btn-sm btn-outline-info">Review</a>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- System Status -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-server me-2"></i>System Status
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3 text-center mb-3">
                                        <div class="d-flex align-items-center justify-content-center mb-2">
                                            <i class="fas fa-database fa-2x text-success me-2"></i>
                                            <div>
                                                <h6 class="mb-0">Database</h6>
                                                <span class="badge bg-success">Online</span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-3 text-center mb-3">
                                        <div class="d-flex align-items-center justify-content-center mb-2">
                                            <i class="fas fa-upload fa-2x text-success me-2"></i>
                                            <div>
                                                <h6 class="mb-0">File Uploads</h6>
                                                <span class="badge bg-success">Working</span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-3 text-center mb-3">
                                        <div class="d-flex align-items-center justify-content-center mb-2">
                                            <i class="fas fa-credit-card fa-2x text-warning me-2"></i>
                                            <div>
                                                <h6 class="mb-0">Payments</h6>
                                                <span class="badge bg-warning">Setup Required</span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-3 text-center mb-3">
                                        <div class="d-flex align-items-center justify-content-center mb-2">
                                            <i class="fas fa-envelope fa-2x text-warning me-2"></i>
                                            <div>
                                                <h6 class="mb-0">Email</h6>
                                                <span class="badge bg-warning">Setup Required</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="alert alert-info mt-3">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Setup Required:</strong> Configure payment gateway and email settings in 
                                    <a href="settings.php" class="alert-link">Settings</a> to enable full functionality.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
