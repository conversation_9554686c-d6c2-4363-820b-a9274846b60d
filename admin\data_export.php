<?php
require_once '../config.php';
require_login(['admin']);

$db = Database::getInstance();

// Handle export requests
if (isset($_GET['export'])) {
    $export_type = $_GET['export'];
    $format = $_GET['format'] ?? 'csv';
    
    // Set headers for download
    $filename = $export_type . '_export_' . date('Y-m-d_H-i-s');
    
    if ($format === 'json') {
        header('Content-Type: application/json');
        header('Content-Disposition: attachment; filename="' . $filename . '.json"');
    } else {
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
    }
    
    $data = [];
    
    switch ($export_type) {
        case 'settings':
            $data = $db->fetchAll("SELECT * FROM settings ORDER BY category, setting_key");
            break;
            
        case 'users':
            $data = $db->fetchAll("
                SELECT id, username, full_name, email, phone, instagram_handle, 
                       total_earned, pending_payout, status, created_at, updated_at
                FROM users ORDER BY created_at DESC
            ");
            break;
            
        case 'influencers':
            $data = $db->fetchAll("
                SELECT id, username, full_name, email, phone, instagram_handle, 
                       instagram_followers, category, total_earned, pending_payout, 
                       status, created_at, updated_at
                FROM influencers ORDER BY created_at DESC
            ");
            break;
            
        case 'brands':
            $data = $db->fetchAll("
                SELECT id, company_name, contact_name, email, phone, website, 
                       industry, status, created_at, updated_at
                FROM brands ORDER BY created_at DESC
            ");
            break;
            
        case 'campaigns':
            $data = $db->fetchAll("
                SELECT c.*, b.company_name as brand_name
                FROM campaigns c
                LEFT JOIN brands b ON c.brand_id = b.id
                ORDER BY c.created_at DESC
            ");
            break;
            
        case 'payouts':
            $data = $db->fetchAll("
                SELECT p.*, 
                       CASE 
                           WHEN p.user_type = 'user' THEN u.full_name
                           WHEN p.user_type = 'influencer' THEN i.full_name
                       END as user_name,
                       CASE 
                           WHEN p.user_type = 'user' THEN u.email
                           WHEN p.user_type = 'influencer' THEN i.email
                       END as user_email
                FROM payouts p
                LEFT JOIN users u ON p.user_type = 'user' AND p.user_id = u.id
                LEFT JOIN influencers i ON p.user_type = 'influencer' AND p.user_id = i.id
                ORDER BY p.created_at DESC
            ");
            break;
            
        case 'badges':
            $data = $db->fetchAll("SELECT * FROM badges ORDER BY badge_type, name");
            break;
            
        case 'user_badges':
            $data = $db->fetchAll("
                SELECT ub.*, b.name as badge_name, b.description as badge_description,
                       CASE 
                           WHEN ub.user_type = 'user' THEN u.full_name
                           WHEN ub.user_type = 'influencer' THEN i.full_name
                       END as user_name
                FROM user_badges ub
                LEFT JOIN badges b ON ub.badge_id = b.id
                LEFT JOIN users u ON ub.user_type = 'user' AND ub.user_id = u.id
                LEFT JOIN influencers i ON ub.user_type = 'influencer' AND ub.user_id = i.id
                ORDER BY ub.assigned_at DESC
            ");
            break;
            
        case 'instagram_verifications':
            $data = $db->fetchAll("
                SELECT iv.*, 
                       CASE 
                           WHEN iv.user_type = 'user' THEN u.full_name
                           WHEN iv.user_type = 'influencer' THEN i.full_name
                       END as user_name
                FROM instagram_verifications iv
                LEFT JOIN users u ON iv.user_type = 'user' AND iv.user_id = u.id
                LEFT JOIN influencers i ON iv.user_type = 'influencer' AND iv.user_id = i.id
                ORDER BY iv.submitted_at DESC
            ");
            break;
            
        case 'complete_backup':
            // Complete database backup
            $tables = ['settings', 'users', 'influencers', 'brands', 'campaigns', 'payouts', 'badges', 'user_badges', 'instagram_verifications'];
            $backup_data = [];
            
            foreach ($tables as $table) {
                $backup_data[$table] = $db->fetchAll("SELECT * FROM $table");
            }
            
            if ($format === 'json') {
                echo json_encode($backup_data, JSON_PRETTY_PRINT);
            } else {
                // For CSV, create a zip file with multiple CSV files
                $zip = new ZipArchive();
                $zip_filename = tempnam(sys_get_temp_dir(), 'backup_') . '.zip';
                
                if ($zip->open($zip_filename, ZipArchive::CREATE) === TRUE) {
                    foreach ($backup_data as $table_name => $table_data) {
                        if (!empty($table_data)) {
                            $csv_content = '';
                            
                            // Add headers
                            $headers = array_keys($table_data[0]);
                            $csv_content .= implode(',', $headers) . "\n";
                            
                            // Add data
                            foreach ($table_data as $row) {
                                $csv_content .= implode(',', array_map(function($value) {
                                    return '"' . str_replace('"', '""', $value) . '"';
                                }, $row)) . "\n";
                            }
                            
                            $zip->addFromString($table_name . '.csv', $csv_content);
                        }
                    }
                    $zip->close();
                    
                    header('Content-Type: application/zip');
                    header('Content-Disposition: attachment; filename="complete_backup_' . date('Y-m-d_H-i-s') . '.zip"');
                    readfile($zip_filename);
                    unlink($zip_filename);
                }
            }
            exit;
    }
    
    if ($format === 'json') {
        echo json_encode($data, JSON_PRETTY_PRINT);
    } else {
        // CSV output
        if (!empty($data)) {
            $output = fopen('php://output', 'w');
            
            // Add headers
            $headers = array_keys($data[0]);
            fputcsv($output, $headers);
            
            // Add data
            foreach ($data as $row) {
                fputcsv($output, $row);
            }
            
            fclose($output);
        }
    }
    exit;
}

$page_title = 'Data Export';
include '../includes/header.php';

// Get statistics for each export type
$stats = [
    'settings' => $db->fetch("SELECT COUNT(*) as count FROM settings")['count'],
    'users' => $db->fetch("SELECT COUNT(*) as count FROM users")['count'],
    'influencers' => $db->fetch("SELECT COUNT(*) as count FROM influencers")['count'],
    'brands' => $db->fetch("SELECT COUNT(*) as count FROM brands")['count'],
    'campaigns' => $db->fetch("SELECT COUNT(*) as count FROM campaigns")['count'],
    'payouts' => $db->fetch("SELECT COUNT(*) as count FROM payouts")['count'],
    'badges' => $db->fetch("SELECT COUNT(*) as count FROM badges")['count'],
    'user_badges' => $db->fetch("SELECT COUNT(*) as count FROM user_badges")['count'],
    'instagram_verifications' => $db->fetch("SELECT COUNT(*) as count FROM instagram_verifications")['count']
];
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-xl-2 sidebar p-0">
            <div class="p-3">
                <h5 class="text-white mb-4">
                    <i class="fas fa-shield-alt me-2"></i>Admin Panel
                </h5>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a class="nav-link" href="users.php">
                        <i class="fas fa-users"></i>Users
                    </a>
                    <a class="nav-link" href="influencers.php">
                        <i class="fas fa-star"></i>Influencers
                    </a>
                    <a class="nav-link" href="brands.php">
                        <i class="fas fa-building"></i>Brands
                    </a>
                    <a class="nav-link" href="campaigns.php">
                        <i class="fas fa-bullhorn"></i>Campaigns
                    </a>
                    <a class="nav-link" href="submissions.php">
                        <i class="fas fa-clipboard-check"></i>Submissions
                    </a>
                    <a class="nav-link" href="assignments.php">
                        <i class="fas fa-user-check"></i>Assignments
                    </a>
                    <a class="nav-link" href="payouts.php">
                        <i class="fas fa-money-bill-wave"></i>Payouts
                    </a>
                    
                    <!-- Instagram & Verification Section -->
                    <hr class="my-2 text-light">
                    <small class="text-light px-3 mb-2">Instagram & Verification</small>
                    
                    <a class="nav-link" href="instagram_verifications.php">
                        <i class="fab fa-instagram"></i>Instagram Verifications
                    </a>
                    <a class="nav-link" href="influencer_follow_check.php">
                        <i class="fas fa-user-check"></i>Follow Status Check
                    </a>
                    <a class="nav-link" href="instagram_blue_check.php">
                        <i class="fas fa-check-circle"></i>Blue Check Management
                    </a>
                    <a class="nav-link" href="user_verification_status.php">
                        <i class="fas fa-clipboard-check"></i>User Follow Status
                    </a>
                    <a class="nav-link" href="suspended_users.php">
                        <i class="fas fa-ban"></i>Suspended Users
                    </a>
                    
                    <!-- Badge Management Section -->
                    <hr class="my-2 text-light">
                    <small class="text-light px-3 mb-2">Badge Management</small>
                    
                    <a class="nav-link" href="badge_management.php">
                        <i class="fas fa-award"></i>Badge Management
                    </a>
                    <a class="nav-link" href="user_badge_assignment.php">
                        <i class="fas fa-user-tag"></i>Assign Badges
                    </a>
                    
                    <!-- System Section -->
                    <hr class="my-2 text-light">
                    <small class="text-light px-3 mb-2">System</small>
                    
                    <a class="nav-link" href="settings.php">
                        <i class="fas fa-cog"></i>Settings
                    </a>
                    <a class="nav-link" href="advanced_settings.php">
                        <i class="fas fa-sliders-h"></i>Advanced Settings
                    </a>
                    <a class="nav-link active" href="data_export.php">
                        <i class="fas fa-download"></i>Data Export
                    </a>
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </nav>
            </div>
        </div>
