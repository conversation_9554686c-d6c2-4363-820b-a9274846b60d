<?php
require_once '../config.php';
require_login(['admin']);

$db = Database::getInstance();

echo "<h2>Influencer Debug Page</h2>";

// Test 1: Check if influencers table exists and has data
echo "<h3>1. Database Connection Test</h3>";
try {
    $count = $db->fetch("SELECT COUNT(*) as count FROM influencers");
    echo "✅ Database connected successfully<br>";
    echo "📊 Total influencers in database: " . $count['count'] . "<br><br>";
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br><br>";
}

// Test 2: Show all influencers
echo "<h3>2. All Influencers</h3>";
try {
    $influencers = $db->fetchAll("SELECT id, username, full_name, email, status, created_at FROM influencers ORDER BY id");
    
    if (empty($influencers)) {
        echo "⚠️ No influencers found. <a href='#' onclick='createTestInfluencer()'>Create test influencer</a><br><br>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Username</th><th>Full Name</th><th>Email</th><th>Status</th><th>Created</th><th>Actions</th></tr>";
        
        foreach ($influencers as $inf) {
            echo "<tr>";
            echo "<td>" . $inf['id'] . "</td>";
            echo "<td>" . htmlspecialchars($inf['username']) . "</td>";
            echo "<td>" . htmlspecialchars($inf['full_name']) . "</td>";
            echo "<td>" . htmlspecialchars($inf['email']) . "</td>";
            echo "<td><span style='color: " . ($inf['status'] === 'pending' ? 'orange' : ($inf['status'] === 'approved' ? 'green' : 'red')) . ";'>" . $inf['status'] . "</span></td>";
            echo "<td>" . $inf['created_at'] . "</td>";
            echo "<td>";
            if ($inf['status'] === 'pending') {
                echo "<button onclick='testApprove(" . $inf['id'] . ")' style='background: green; color: white; border: none; padding: 5px;'>Approve</button> ";
                echo "<button onclick='testReject(" . $inf['id'] . ")' style='background: red; color: white; border: none; padding: 5px;'>Reject</button>";
            } else {
                echo "<button onclick='testReset(" . $inf['id'] . ")' style='background: blue; color: white; border: none; padding: 5px;'>Reset to Pending</button>";
            }
            echo "</td>";
            echo "</tr>";
        }
        echo "</table><br>";
    }
} catch (Exception $e) {
    echo "❌ Error fetching influencers: " . $e->getMessage() . "<br><br>";
}

// Test 3: Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    $action = $_POST['action'] ?? '';
    $influencer_id = intval($_POST['influencer_id'] ?? 0);
    
    try {
        switch ($action) {
            case 'approve':
                $result = $db->update('influencers', ['status' => 'approved'], 'id = ?', [$influencer_id]);
                echo json_encode(['success' => true, 'message' => 'Influencer approved successfully!']);
                break;
                
            case 'reject':
                $result = $db->update('influencers', ['status' => 'rejected'], 'id = ?', [$influencer_id]);
                echo json_encode(['success' => true, 'message' => 'Influencer rejected successfully!']);
                break;
                
            case 'reset':
                $result = $db->update('influencers', ['status' => 'pending'], 'id = ?', [$influencer_id]);
                echo json_encode(['success' => true, 'message' => 'Influencer reset to pending!']);
                break;
                
            case 'create_test':
                $test_id = $db->insert('influencers', [
                    'username' => 'test_inf_' . time(),
                    'email' => 'test' . time() . '@test.com',
                    'password' => password_hash('password', PASSWORD_DEFAULT),
                    'full_name' => 'Test Influencer ' . time(),
                    'phone' => '9876543210',
                    'instagram_handle' => 'test_handle_' . time(),
                    'instagram_followers' => 10000,
                    'category' => 'lifestyle',
                    'status' => 'pending'
                ]);
                echo json_encode(['success' => true, 'message' => 'Test influencer created with ID: ' . $test_id]);
                break;
                
            default:
                echo json_encode(['success' => false, 'message' => 'Invalid action']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
    }
    exit;
}

// Test 4: Show recent activity logs
echo "<h3>3. Recent Activity Logs</h3>";
try {
    $logs = $db->fetchAll("
        SELECT * FROM activity_logs 
        WHERE action LIKE '%influencer%' 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    
    if (empty($logs)) {
        echo "No activity logs found.<br><br>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>User Type</th><th>User ID</th><th>Action</th><th>Details</th><th>Time</th></tr>";
        
        foreach ($logs as $log) {
            echo "<tr>";
            echo "<td>" . $log['user_type'] . "</td>";
            echo "<td>" . $log['user_id'] . "</td>";
            echo "<td>" . $log['action'] . "</td>";
            echo "<td>" . htmlspecialchars($log['details']) . "</td>";
            echo "<td>" . $log['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table><br>";
    }
} catch (Exception $e) {
    echo "Activity logs table may not exist: " . $e->getMessage() . "<br><br>";
}

?>

<script>
function testApprove(id) {
    if (confirm('Test approve influencer ID ' + id + '?')) {
        sendRequest('approve', id);
    }
}

function testReject(id) {
    if (confirm('Test reject influencer ID ' + id + '?')) {
        sendRequest('reject', id);
    }
}

function testReset(id) {
    if (confirm('Reset influencer ID ' + id + ' to pending?')) {
        sendRequest('reset', id);
    }
}

function createTestInfluencer() {
    if (confirm('Create a test influencer?')) {
        sendRequest('create_test', 0);
    }
}

function sendRequest(action, influencerId) {
    const formData = new FormData();
    formData.append('action', action);
    formData.append('influencer_id', influencerId);
    
    fetch('debug_influencers.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ ' + data.message);
            location.reload();
        } else {
            alert('❌ ' + data.message);
        }
    })
    .catch(error => {
        alert('❌ Network error: ' + error);
    });
}
</script>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
button { margin: 2px; padding: 5px 10px; cursor: pointer; }
</style>

<p><a href="influencers.php">← Back to Influencer Management</a></p>
