<?php
require_once '../config.php';
require_login(['admin']);

$db = Database::getInstance();

// Get export type
$type = $_GET['type'] ?? '';

if (!in_array($type, ['users', 'influencers', 'campaigns', 'payouts', 'submissions'])) {
    $_SESSION['error'] = 'Invalid export type.';
    redirect('dashboard.php');
}

// Set headers for CSV download
header('Content-Type: text/csv');
header('Content-Disposition: attachment; filename="' . $type . '_export_' . date('Y-m-d') . '.csv"');

$output = fopen('php://output', 'w');

switch ($type) {
    case 'users':
        // CSV headers
        fputcsv($output, [
            'ID', 'Username', 'Full Name', 'Email', 'Phone', 'Instagram Handle',
            'Total Earned', 'Pending Payout', 'Status', 'Created At'
        ]);
        
        // Get users data
        $users = $db->fetchAll("
            SELECT id, username, full_name, email, phone, instagram_handle,
                   total_earned, pending_payout, status, created_at
            FROM users
            ORDER BY created_at DESC
        ");
        
        foreach ($users as $user) {
            fputcsv($output, [
                $user['id'],
                $user['username'],
                $user['full_name'],
                $user['email'],
                $user['phone'],
                $user['instagram_handle'],
                $user['total_earned'],
                $user['pending_payout'],
                $user['status'],
                $user['created_at']
            ]);
        }
        break;
        
    case 'influencers':
        // CSV headers
        fputcsv($output, [
            'ID', 'Username', 'Full Name', 'Email', 'Phone', 'Instagram Handle',
            'Followers', 'Category', 'Total Earned', 'Pending Payout', 'Status', 'Created At'
        ]);
        
        // Get influencers data
        $influencers = $db->fetchAll("
            SELECT id, username, full_name, email, phone, instagram_handle,
                   instagram_followers, category, total_earned, pending_payout, status, created_at
            FROM influencers
            ORDER BY created_at DESC
        ");
        
        foreach ($influencers as $influencer) {
            fputcsv($output, [
                $influencer['id'],
                $influencer['username'],
                $influencer['full_name'],
                $influencer['email'],
                $influencer['phone'],
                $influencer['instagram_handle'],
                $influencer['instagram_followers'],
                $influencer['category'],
                $influencer['total_earned'],
                $influencer['pending_payout'],
                $influencer['status'],
                $influencer['created_at']
            ]);
        }
        break;
        
    case 'campaigns':
        // CSV headers
        fputcsv($output, [
            'ID', 'Title', 'Brand', 'Campaign Type', 'Reward Amount', 'Total Budget',
            'Max Participants', 'Current Participants', 'Status', 'Start Date', 'End Date', 'Created At'
        ]);
        
        // Get campaigns data
        $campaigns = $db->fetchAll("
            SELECT c.id, c.title, b.company_name as brand_name, c.campaign_type, c.reward_amount,
                   c.total_budget, c.max_participants, c.current_participants, c.status,
                   c.start_date, c.end_date, c.created_at
            FROM campaigns c
            LEFT JOIN brands b ON c.brand_id = b.id
            ORDER BY c.created_at DESC
        ");
        
        foreach ($campaigns as $campaign) {
            fputcsv($output, [
                $campaign['id'],
                $campaign['title'],
                $campaign['brand_name'] ?? 'RealEarners',
                $campaign['campaign_type'],
                $campaign['reward_amount'],
                $campaign['total_budget'],
                $campaign['max_participants'],
                $campaign['current_participants'],
                $campaign['status'],
                $campaign['start_date'],
                $campaign['end_date'],
                $campaign['created_at']
            ]);
        }
        break;
        
    case 'payouts':
        // CSV headers
        fputcsv($output, [
            'ID', 'User Type', 'User Name', 'Email', 'Amount', 'UPI ID',
            'Status', 'Transaction ID', 'Created At', 'Processed At'
        ]);
        
        // Get payouts data
        $payouts = $db->fetchAll("
            SELECT p.id, p.user_type, p.amount, p.upi_id, p.status, p.transaction_id,
                   p.created_at, p.processed_at,
                   CASE 
                       WHEN p.user_type = 'user' THEN u.full_name
                       WHEN p.user_type = 'influencer' THEN i.full_name
                   END as user_name,
                   CASE 
                       WHEN p.user_type = 'user' THEN u.email
                       WHEN p.user_type = 'influencer' THEN i.email
                   END as email
            FROM payouts p
            LEFT JOIN users u ON p.user_type = 'user' AND p.user_id = u.id
            LEFT JOIN influencers i ON p.user_type = 'influencer' AND p.user_id = i.id
            ORDER BY p.created_at DESC
        ");
        
        foreach ($payouts as $payout) {
            fputcsv($output, [
                $payout['id'],
                $payout['user_type'],
                $payout['user_name'],
                $payout['email'],
                $payout['amount'],
                $payout['upi_id'],
                $payout['status'],
                $payout['transaction_id'],
                $payout['created_at'],
                $payout['processed_at']
            ]);
        }
        break;
        
    case 'submissions':
        // CSV headers
        fputcsv($output, [
            'ID', 'User Name', 'Campaign Title', 'Brand', 'Reward Amount',
            'Status', 'Post URL', 'Submitted At', 'Approved At'
        ]);
        
        // Get submissions data
        $submissions = $db->fetchAll("
            SELECT ut.id, u.full_name as user_name, c.title as campaign_title,
                   b.company_name as brand_name, ut.reward_amount, ut.status,
                   us.post_url, ut.submitted_at, ut.approved_at
            FROM user_tasks ut
            JOIN users u ON ut.user_id = u.id
            JOIN campaigns c ON ut.campaign_id = c.id
            LEFT JOIN brands b ON c.brand_id = b.id
            LEFT JOIN user_submissions us ON ut.id = us.task_id
            WHERE ut.status IN ('submitted', 'approved', 'rejected')
            ORDER BY ut.submitted_at DESC
        ");
        
        foreach ($submissions as $submission) {
            fputcsv($output, [
                $submission['id'],
                $submission['user_name'],
                $submission['campaign_title'],
                $submission['brand_name'] ?? 'RealEarners',
                $submission['reward_amount'],
                $submission['status'],
                $submission['post_url'],
                $submission['submitted_at'],
                $submission['approved_at']
            ]);
        }
        break;
}

fclose($output);
exit;
?>
