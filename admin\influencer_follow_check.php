<?php
require_once '../config.php';
require_once '../includes/instagram_verification.php';
require_login(['admin']);

$db = Database::getInstance();
$verification_system = new InstagramVerification();

$success = '';
$error = '';

// Handle manual verification actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];
        $influencer_id = intval($_POST['influencer_id'] ?? 0);
        
        if ($action === 'approve' && $influencer_id > 0) {
            try {
                $verification_system->approveVerification($influencer_id, 'influencer');
                $success = 'Influencer verification approved successfully!';
            } catch (Exception $e) {
                $error = 'Failed to approve verification.';
            }
        } elseif ($action === 'reject' && $influencer_id > 0) {
            try {
                $verification_system->rejectVerification($influencer_id, 'influencer');
                $success = 'Influencer verification rejected successfully!';
            } catch (Exception $e) {
                $error = 'Failed to reject verification.';
            }
        } elseif ($action === 'reset' && $influencer_id > 0) {
            try {
                // Reset verification status
                $db->delete('instagram_verifications', 'user_id = ? AND user_type = ?', [$influencer_id, 'influencer']);
                $success = 'Influencer verification status reset successfully!';
            } catch (Exception $e) {
                $error = 'Failed to reset verification status.';
            }
        }
    }
}

// Get filter parameters
$filter = $_GET['filter'] ?? 'all';
$search = trim($_GET['search'] ?? '');

// Get all influencers with their verification status
$influencers = $db->fetchAll("
    SELECT 
        i.id,
        i.full_name,
        i.email,
        i.instagram_handle,
        i.instagram_followers,
        i.status as account_status,
        i.created_at,
        iv.verification_status,
        iv.instagram_username as submitted_username,
        iv.submitted_at,
        iv.verified_at,
        iv.admin_notes
    FROM influencers i
    LEFT JOIN instagram_verifications iv ON i.id = iv.user_id AND iv.user_type = 'influencer'
    ORDER BY 
        CASE 
            WHEN iv.verification_status = 'pending' THEN 1
            WHEN iv.verification_status = 'verified' THEN 2
            WHEN iv.verification_status = 'rejected' THEN 3
            ELSE 4
        END,
        iv.submitted_at DESC,
        i.created_at DESC
");

// Filter influencers based on verification status
$filtered_influencers = [];
foreach ($influencers as $influencer) {
    $verification_status = $verification_system->getVerificationStatus($influencer['id'], 'influencer');
    $influencer['platform_verified'] = $verification_status['verified'];
    $influencer['verification_details'] = $verification_status;
    
    // Apply filters
    if ($filter === 'verified' && !$verification_status['verified']) continue;
    if ($filter === 'pending' && $verification_status['verification_status'] !== 'pending') continue;
    if ($filter === 'not_submitted' && $verification_status['verification_status'] !== 'not_submitted') continue;
    if ($filter === 'rejected' && $verification_status['verification_status'] !== 'rejected') continue;
    
    // Apply search
    if (!empty($search)) {
        $search_text = strtolower($search);
        if (strpos(strtolower($influencer['full_name']), $search_text) === false &&
            strpos(strtolower($influencer['email']), $search_text) === false &&
            strpos(strtolower($influencer['instagram_handle']), $search_text) === false) {
            continue;
        }
    }
    
    $filtered_influencers[] = $influencer;
}

// Get statistics
$stats = [
    'total' => count($influencers),
    'verified' => 0,
    'pending' => 0,
    'not_submitted' => 0,
    'rejected' => 0
];

foreach ($influencers as $influencer) {
    $verification_status = $verification_system->getVerificationStatus($influencer['id'], 'influencer');
    $stats[$verification_status['verification_status']]++;
    if ($verification_status['verified']) {
        $stats['verified']++;
    }
}

$page_title = 'Influencer Follow Status Check';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-xl-2 sidebar p-0">
            <div class="p-3">
                <h5 class="text-white mb-4">
                    <i class="fas fa-shield-alt me-2"></i>Admin Panel
                </h5>
                
                                                <nav class="nav flex-column">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a class="nav-link" href="users.php">
                        <i class="fas fa-users"></i>Users
                    </a>
                    <a class="nav-link" href="influencers.php">
                        <i class="fas fa-star"></i>Influencers
                    </a>
                    <a class="nav-link" href="brands.php">
                        <i class="fas fa-building"></i>Brands
                    </a>
                    <a class="nav-link" href="campaigns.php">
                        <i class="fas fa-bullhorn"></i>Campaigns
                    </a>
                    <a class="nav-link" href="submissions.php">
                        <i class="fas fa-clipboard-check"></i>Submissions
                    </a>
                    <a class="nav-link" href="assignments.php">
                        <i class="fas fa-user-check"></i>Assignments
                    </a>
                    <a class="nav-link" href="payouts.php">
                        <i class="fas fa-money-bill-wave"></i>Payouts
                    </a>
                    
                    <!-- Instagram & Verification Section -->
                    <hr class="my-2 text-light">
                    <small class="text-light px-3 mb-2">Instagram & Verification</small>
                    
                    <a class="nav-link" href="instagram_verifications.php">
                        <i class="fab fa-instagram"></i>Instagram Verifications
                    </a>
                    <a class="nav-link active" href="influencer_follow_check.php">
                        <i class="fas fa-user-check"></i>Follow Status Check
                    </a>
                    <a class="nav-link" href="instagram_blue_check.php">
                        <i class="fas fa-check-circle"></i>Blue Check Management
                    </a>
                    <a class="nav-link" href="user_verification_status.php">
                        <i class="fas fa-clipboard-check"></i>User Follow Status
                    </a>
                    <a class="nav-link" href="suspended_users.php">
                        <i class="fas fa-ban"></i>Suspended Users
                    </a>
                    
                    <!-- Badge Management Section -->
                    <hr class="my-2 text-light">
                    <small class="text-light px-3 mb-2">Badge Management</small>
                    
                    <a class="nav-link" href="badge_management.php">
                        <i class="fas fa-award"></i>Badge Management
                    </a>
                    <a class="nav-link" href="user_badge_assignment.php">
                        <i class="fas fa-user-tag"></i>Assign Badges
                    </a>
                    
                    <!-- System Section -->
                    <hr class="my-2 text-light">
                    <small class="text-light px-3 mb-2">System</small>
                    
                    <a class="nav-link" href="settings.php">
                        <i class="fas fa-cog"></i>Settings
                    </a>
                    <a class="nav-link" href="instagram_settings.php">
                        <i class="fab fa-instagram"></i>Instagram Settings
                    </a>
                    <a class="nav-link" href="export.php">
                        <i class="fas fa-download"></i>Export Data
                    </a>
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10 main-content" style="margin-left: auto;">
            <!-- Top Bar -->
            <div class="bg-white shadow-sm p-3 mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">Influencer Follow Status Check</h4>
                        <small class="text-muted">Monitor and manage influencer Instagram follow verification status</small>
                    </div>
                </div>
            </div>
            
            <div class="p-3">
                <!-- Success/Error Messages -->
                <?php if ($success): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-lg-2 col-md-4 mb-3">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['total']; ?></h3>
                                <p class="mb-0 small">Total</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-2 col-md-4 mb-3">
                        <div class="card stats-card success">
                            <div class="card-body text-center">
                                <i class="fas fa-check-circle fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['verified']; ?></h3>
                                <p class="mb-0 small">Verified</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-2 col-md-4 mb-3">
                        <div class="card stats-card warning">
                            <div class="card-body text-center">
                                <i class="fas fa-clock fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['pending']; ?></h3>
                                <p class="mb-0 small">Pending</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-2 col-md-4 mb-3">
                        <div class="card stats-card danger">
                            <div class="card-body text-center">
                                <i class="fas fa-times-circle fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['rejected']; ?></h3>
                                <p class="mb-0 small">Rejected</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-2 col-md-4 mb-3">
                        <div class="card stats-card info">
                            <div class="card-body text-center">
                                <i class="fas fa-question-circle fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['not_submitted']; ?></h3>
                                <p class="mb-0 small">Not Submitted</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters and Search -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <label for="filter" class="form-label">Filter by Status</label>
                                <select class="form-select" id="filter" name="filter">
                                    <option value="all" <?php echo $filter === 'all' ? 'selected' : ''; ?>>All Influencers</option>
                                    <option value="verified" <?php echo $filter === 'verified' ? 'selected' : ''; ?>>Verified (Following Accounts)</option>
                                    <option value="pending" <?php echo $filter === 'pending' ? 'selected' : ''; ?>>Pending Verification</option>
                                    <option value="rejected" <?php echo $filter === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                                    <option value="not_submitted" <?php echo $filter === 'not_submitted' ? 'selected' : ''; ?>>Not Submitted</option>
                                </select>
                            </div>

                            <div class="col-md-6">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" class="form-control" id="search" name="search"
                                       value="<?php echo htmlspecialchars($search); ?>"
                                       placeholder="Search by name, Instagram handle, or email">
                            </div>

                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-1"></i>Search
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Influencers List -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>Influencer Follow Status
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($filtered_influencers)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                                <h6 class="text-muted">No influencers found</h6>
                                <p class="text-muted">Try adjusting your search criteria.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Influencer</th>
                                            <th>Instagram Handle</th>
                                            <th>Submitted Username</th>
                                            <th>Follow Status</th>
                                            <th>Submitted Date</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($filtered_influencers as $influencer): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <h6 class="mb-1"><?php echo htmlspecialchars($influencer['full_name']); ?></h6>
                                                    <small class="text-muted"><?php echo htmlspecialchars($influencer['email']); ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <strong>@<?php echo htmlspecialchars($influencer['instagram_handle']); ?></strong>
                                            </td>
                                            <td>
                                                <?php if ($influencer['submitted_username']): ?>
                                                    <strong>@<?php echo htmlspecialchars($influencer['submitted_username']); ?></strong>
                                                    <?php if ($influencer['instagram_handle'] !== $influencer['submitted_username']): ?>
                                                        <br><small class="text-warning">
                                                            <i class="fas fa-exclamation-triangle me-1"></i>Different from profile
                                                        </small>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <span class="text-muted">Not submitted</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                $status = $influencer['verification_details']['verification_status'];
                                                $verified = $influencer['platform_verified'];
                                                ?>

                                                <?php if ($verified): ?>
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-check-circle me-1"></i>Verified (Following)
                                                    </span>
                                                <?php elseif ($status === 'pending'): ?>
                                                    <span class="badge bg-warning">
                                                        <i class="fas fa-clock me-1"></i>Pending Review
                                                    </span>
                                                <?php elseif ($status === 'rejected'): ?>
                                                    <span class="badge bg-danger">
                                                        <i class="fas fa-times-circle me-1"></i>Rejected
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">
                                                        <i class="fas fa-question-circle me-1"></i>Not Submitted
                                                    </span>
                                                <?php endif; ?>

                                                <div class="mt-1">
                                                    <small class="text-muted">
                                                        Required: @thesyedabubakkar, @real_earners.in
                                                    </small>
                                                </div>
                                            </td>
                                            <td>
                                                <?php if ($influencer['submitted_at']): ?>
                                                    <?php echo date('M j, Y g:i A', strtotime($influencer['submitted_at'])); ?>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group-vertical" role="group">
                                                    <?php if ($status === 'pending'): ?>
                                                        <form method="POST" style="display: inline;">
                                                            <input type="hidden" name="action" value="approve">
                                                            <input type="hidden" name="influencer_id" value="<?php echo $influencer['id']; ?>">
                                                            <button type="submit" class="btn btn-sm btn-success mb-1"
                                                                    onclick="return confirm('Approve this verification?')">
                                                                <i class="fas fa-check me-1"></i>Approve
                                                            </button>
                                                        </form>

                                                        <form method="POST" style="display: inline;">
                                                            <input type="hidden" name="action" value="reject">
                                                            <input type="hidden" name="influencer_id" value="<?php echo $influencer['id']; ?>">
                                                            <button type="submit" class="btn btn-sm btn-danger mb-1"
                                                                    onclick="return confirm('Reject this verification?')">
                                                                <i class="fas fa-times me-1"></i>Reject
                                                            </button>
                                                        </form>
                                                    <?php endif; ?>

                                                    <?php if ($status !== 'not_submitted'): ?>
                                                        <form method="POST" style="display: inline;">
                                                            <input type="hidden" name="action" value="reset">
                                                            <input type="hidden" name="influencer_id" value="<?php echo $influencer['id']; ?>">
                                                            <button type="submit" class="btn btn-sm btn-outline-secondary"
                                                                    onclick="return confirm('Reset verification status? This will require them to resubmit.')">
                                                                <i class="fas fa-redo me-1"></i>Reset
                                                            </button>
                                                        </form>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Information Card -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>Follow Verification Process
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="fw-bold">Required Follows</h6>
                                <ul class="small">
                                    <li><strong>@thesyedabubakkar</strong> - Main account</li>
                                    <li><strong>@real_earners.in</strong> - Platform account</li>
                                    <li>Both accounts must be followed for verification</li>
                                    <li>2-day grace period for compliance</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 class="fw-bold">Verification Status</h6>
                                <ul class="small">
                                    <li><span class="badge bg-success">Verified</span> - Following both required accounts</li>
                                    <li><span class="badge bg-warning">Pending</span> - Submitted, awaiting admin review</li>
                                    <li><span class="badge bg-danger">Rejected</span> - Not following required accounts</li>
                                    <li><span class="badge bg-secondary">Not Submitted</span> - Haven't submitted verification</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
