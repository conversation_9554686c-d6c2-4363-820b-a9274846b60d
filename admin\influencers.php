<?php
require_once '../config.php';
require_login(['admin']);

$db = Database::getInstance();

// Handle influencer actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['update_status'])) {
        $influencer_id = intval($_POST['influencer_id']);
        $status = sanitize_input($_POST['status']);

        // Validate status
        $valid_statuses = ['pending', 'approved', 'rejected', 'suspended'];
        if (!in_array($status, $valid_statuses)) {
            $_SESSION['error'] = 'Invalid status selected.';
        } else {
            try {
                // Check if influencer exists
                $influencer = $db->fetch("SELECT id, full_name FROM influencers WHERE id = ?", [$influencer_id]);

                if (!$influencer) {
                    $_SESSION['error'] = 'Influencer not found.';
                } else {
                    // Update status
                    $result = $db->update('influencers', ['status' => $status], 'id = ?', [$influencer_id]);

                    if ($result) {
                        $_SESSION['success'] = "Influencer {$influencer['full_name']} status updated to " . ucfirst($status) . " successfully!";

                        // Log the activity
                        log_activity('admin', get_user_id(), 'influencer_status_update', "Updated influencer {$influencer_id} status to {$status}");
                    } else {
                        $_SESSION['error'] = 'Failed to update influencer status. Please try again.';
                    }
                }
            } catch (Exception $e) {
                $_SESSION['error'] = 'Database error: ' . $e->getMessage();
                error_log("Influencer status update error: " . $e->getMessage());
            }
        }
    }
    
    if (isset($_POST['delete_influencer'])) {
        $influencer_id = intval($_POST['influencer_id']);

        try {
            // Delete related records first
            $db->delete('influencer_campaigns', 'influencer_id = ?', [$influencer_id]);
            $db->delete('influencer_submissions', 'influencer_id = ?', [$influencer_id]);
            $db->delete('payouts', 'user_type = ? AND user_id = ?', ['influencer', $influencer_id]);
            $db->delete('influencers', 'id = ?', [$influencer_id]);

            $_SESSION['success'] = 'Influencer deleted successfully!';
        } catch (Exception $e) {
            $_SESSION['error'] = 'Failed to delete influencer.';
        }
    }

    if (isset($_POST['bulk_process_influencers'])) {
        $influencer_ids = $_POST['influencer_ids'] ?? [];
        $bulk_action = sanitize_input($_POST['bulk_action']);

        if (empty($influencer_ids)) {
            $_SESSION['error'] = 'Please select at least one influencer.';
        } elseif (!in_array($bulk_action, ['approve_all', 'reject_all'])) {
            $_SESSION['error'] = 'Invalid bulk action selected.';
        } else {
            try {
                $processed = 0;
                $failed = 0;
                $status = ($bulk_action === 'approve_all') ? 'approved' : 'rejected';

                foreach ($influencer_ids as $influencer_id) {
                    $influencer_id = intval($influencer_id);

                    // Verify influencer exists and is pending
                    $influencer = $db->fetch("SELECT id, full_name, status FROM influencers WHERE id = ?", [$influencer_id]);

                    if ($influencer && $influencer['status'] === 'pending') {
                        $result = $db->update('influencers', ['status' => $status], 'id = ?', [$influencer_id]);
                        if ($result) {
                            $processed++;
                            // Log the activity
                            log_activity('admin', get_user_id(), 'bulk_influencer_update', "Bulk updated influencer {$influencer_id} to {$status}");
                        } else {
                            $failed++;
                        }
                    } else {
                        $failed++;
                    }
                }

                if ($processed > 0) {
                    $action_text = ($bulk_action === 'approve_all') ? 'approved' : 'rejected';
                    $_SESSION['success'] = "Successfully {$action_text} {$processed} influencer(s)!";

                    if ($failed > 0) {
                        $_SESSION['warning'] = "{$failed} influencer(s) could not be processed (may not be pending).";
                    }
                } else {
                    $_SESSION['error'] = 'No influencers were processed. Please ensure selected influencers are in pending status.';
                }
            } catch (Exception $e) {
                $_SESSION['error'] = 'Database error during bulk processing: ' . $e->getMessage();
                error_log("Bulk influencer processing error: " . $e->getMessage());
            }
        }
    }
}

// Get filter parameters
$search = $_GET['search'] ?? '';
$status = $_GET['status'] ?? '';
$category = $_GET['category'] ?? '';
$min_followers = $_GET['min_followers'] ?? '';

// Build query
$conditions = ['1=1'];
$params = [];

if ($search) {
    $conditions[] = "(username LIKE ? OR full_name LIKE ? OR email LIKE ?)";
    $search_param = "%{$search}%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

if ($status) {
    $conditions[] = "status = ?";
    $params[] = $status;
}

if ($category) {
    $conditions[] = "category = ?";
    $params[] = $category;
}

if ($min_followers) {
    $conditions[] = "instagram_followers >= ?";
    $params[] = intval($min_followers);
}

$where_clause = implode(' AND ', $conditions);

// Get influencers
$influencers = $db->fetchAll("
    SELECT i.*, 
           COUNT(ic.id) as total_campaigns,
           SUM(CASE WHEN ic.status = 'approved' THEN 1 ELSE 0 END) as completed_campaigns
    FROM influencers i
    LEFT JOIN influencer_campaigns ic ON i.id = ic.influencer_id
    WHERE {$where_clause}
    GROUP BY i.id
    ORDER BY i.created_at DESC
", $params);

// Get categories for filter
$categories = $db->fetchAll("
    SELECT DISTINCT category 
    FROM influencers 
    WHERE category IS NOT NULL AND category != ''
    ORDER BY category
");

// Get statistics
$stats = $db->fetch("
    SELECT 
        COUNT(*) as total_influencers,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_approval,
        SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_influencers,
        SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_influencers,
        SUM(total_earned) as total_paid,
        AVG(instagram_followers) as avg_followers
    FROM influencers
");

$page_title = 'Influencer Management';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-xl-2 sidebar p-0">
            <div class="p-3">
                <h5 class="text-white mb-4">
                    <i class="fas fa-crown me-2"></i>Admin Panel
                </h5>
                
                                <nav class="nav flex-column">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a class="nav-link" href="users.php">
                        <i class="fas fa-users"></i>Users
                    </a>
                    <a class="nav-link active" href="influencers.php">
                        <i class="fas fa-star"></i>Influencers
                    </a>
                    <a class="nav-link" href="brands.php">
                        <i class="fas fa-building"></i>Brands
                    </a>
                    <a class="nav-link" href="campaigns.php">
                        <i class="fas fa-bullhorn"></i>Campaigns
                    </a>
                    <a class="nav-link" href="submissions.php">
                        <i class="fas fa-clipboard-check"></i>Submissions
                    </a>
                    <a class="nav-link" href="assignments.php">
                        <i class="fas fa-user-check"></i>Assignments
                    </a>
                    <a class="nav-link" href="payouts.php">
                        <i class="fas fa-money-bill-wave"></i>Payouts
                    </a>
                    
                    <!-- Instagram & Verification Section -->
                    <hr class="my-2 text-light">
                    <small class="text-light px-3 mb-2">Instagram & Verification</small>
                    
                    <a class="nav-link" href="instagram_verifications.php">
                        <i class="fab fa-instagram"></i>Instagram Verifications
                    </a>
                    <a class="nav-link" href="influencer_follow_check.php">
                        <i class="fas fa-user-check"></i>Follow Status Check
                    </a>
                    <a class="nav-link" href="instagram_blue_check.php">
                        <i class="fas fa-check-circle"></i>Blue Check Management
                    </a>
                    <a class="nav-link" href="user_verification_status.php">
                        <i class="fas fa-clipboard-check"></i>User Follow Status
                    </a>
                    <a class="nav-link" href="suspended_users.php">
                        <i class="fas fa-ban"></i>Suspended Users
                    </a>
                    
                    <!-- Badge Management Section -->
                    <hr class="my-2 text-light">
                    <small class="text-light px-3 mb-2">Badge Management</small>
                    
                    <a class="nav-link" href="badge_management.php">
                        <i class="fas fa-award"></i>Badge Management
                    </a>
                    <a class="nav-link" href="user_badge_assignment.php">
                        <i class="fas fa-user-tag"></i>Assign Badges
                    </a>
                    
                    <!-- System Section -->
                    <hr class="my-2 text-light">
                    <small class="text-light px-3 mb-2">System</small>
                    
                    <a class="nav-link" href="settings.php">
                        <i class="fas fa-cog"></i>Settings
                    </a>
                    <a class="nav-link" href="instagram_settings.php">
                        <i class="fab fa-instagram"></i>Instagram Settings
                    </a>
                    <a class="nav-link" href="export.php">
                        <i class="fas fa-download"></i>Export Data
                    </a>
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10 main-content" style="margin-left: auto;">
            <!-- Top Bar -->
            <div class="bg-white shadow-sm p-3 mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">Influencer Management</h4>
                        <small class="text-muted">Manage influencer applications and partnerships</small>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-outline-primary d-lg-none" onclick="toggleSidebar()">
                            <i class="fas fa-bars"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="p-3">
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <i class="fas fa-star fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['total_influencers']; ?></h3>
                                <p class="mb-0">Total Influencers</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card warning">
                            <div class="card-body text-center">
                                <i class="fas fa-clock fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['pending_approval']; ?></h3>
                                <p class="mb-0">Pending Approval</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card success">
                            <div class="card-body text-center">
                                <i class="fas fa-check-circle fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['approved_influencers']; ?></h3>
                                <p class="mb-0">Approved</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card info">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo number_format($stats['avg_followers'] ?? 0); ?></h3>
                                <p class="mb-0">Avg Followers</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-filter me-2"></i>Filter Influencers
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-3">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="<?php echo htmlspecialchars($search); ?>" 
                                       placeholder="Username, name, or email...">
                            </div>
                            
                            <div class="col-md-2">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">All Status</option>
                                    <option value="pending" <?php echo $status === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                    <option value="approved" <?php echo $status === 'approved' ? 'selected' : ''; ?>>Approved</option>
                                    <option value="rejected" <?php echo $status === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                                    <option value="suspended" <?php echo $status === 'suspended' ? 'selected' : ''; ?>>Suspended</option>
                                </select>
                            </div>
                            
                            <div class="col-md-2">
                                <label for="category" class="form-label">Category</label>
                                <select class="form-select" id="category" name="category">
                                    <option value="">All Categories</option>
                                    <?php foreach ($categories as $cat): ?>
                                        <option value="<?php echo htmlspecialchars($cat['category']); ?>" 
                                                <?php echo $category === $cat['category'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars(ucfirst($cat['category'])); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-2">
                                <label for="min_followers" class="form-label">Min Followers</label>
                                <input type="number" class="form-control" id="min_followers" name="min_followers" 
                                       value="<?php echo htmlspecialchars($min_followers); ?>" 
                                       placeholder="1000">
                            </div>
                            
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                <a href="influencers.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Bulk Actions for Pending Influencers -->
                <?php
                $pending_influencers = array_filter($influencers, function($inf) { return $inf['status'] === 'pending'; });
                if (!empty($pending_influencers)):
                ?>
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-tasks me-2"></i>Bulk Actions for Pending Approvals (<?php echo count($pending_influencers); ?> pending)
                            </h6>
                        </div>
                        <div class="card-body">
                            <form method="POST" id="bulkInfluencerForm">
                                <div class="row align-items-end">
                                    <div class="col-md-3">
                                        <label class="form-label">Select Action</label>
                                        <select class="form-select" name="bulk_action" required>
                                            <option value="">Choose Action</option>
                                            <option value="approve_all">Approve Selected</option>
                                            <option value="reject_all">Reject Selected</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <button type="submit" name="bulk_process_influencers" class="btn btn-success">
                                            <i class="fas fa-check-double me-1"></i>Apply to Selected
                                        </button>
                                    </div>
                                    <div class="col-md-6 text-end">
                                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="selectAllInfluencers()">Select All Pending</button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="selectNoneInfluencers()">Select None</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Influencers Table -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>All Influencers (<?php echo count($influencers); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($influencers)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-star fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No influencers found</h5>
                                <p class="text-muted">No influencers match your current filters.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <?php if (!empty($pending_influencers)): ?>
                                                <th width="50">
                                                    <input type="checkbox" class="form-check-input" id="selectAllInfluencersCheckbox">
                                                </th>
                                            <?php endif; ?>
                                            <th>Influencer</th>
                                            <th>Contact</th>
                                            <th>Instagram</th>
                                            <th>Category</th>
                                            <th>Campaigns</th>
                                            <th>Earnings</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($influencers as $influencer): ?>
                                            <tr>
                                                <?php if (!empty($pending_influencers)): ?>
                                                    <td>
                                                        <?php if ($influencer['status'] === 'pending'): ?>
                                                            <input type="checkbox" class="form-check-input influencer-checkbox"
                                                                   name="influencer_ids[]" value="<?php echo $influencer['id']; ?>"
                                                                   form="bulkInfluencerForm">
                                                        <?php endif; ?>
                                                    </td>
                                                <?php endif; ?>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <?php if ($influencer['profile_image']): ?>
                                                            <img src="<?php echo htmlspecialchars($influencer['profile_image']); ?>" 
                                                                 alt="Profile" class="rounded-circle me-2" 
                                                                 style="width: 40px; height: 40px; object-fit: cover;">
                                                        <?php else: ?>
                                                            <div class="bg-warning rounded-circle d-flex align-items-center justify-content-center me-2" 
                                                                 style="width: 40px; height: 40px;">
                                                                <i class="fas fa-star text-white"></i>
                                                            </div>
                                                        <?php endif; ?>
                                                        <div>
                                                            <strong><?php echo htmlspecialchars($influencer['full_name']); ?></strong>
                                                            <br><small class="text-muted">@<?php echo htmlspecialchars($influencer['username']); ?></small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div>
                                                        <small class="d-block"><?php echo htmlspecialchars($influencer['email']); ?></small>
                                                        <small class="text-muted"><?php echo htmlspecialchars($influencer['phone']); ?></small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div>
                                                        <strong>@<?php echo htmlspecialchars($influencer['instagram_handle']); ?></strong>
                                                        <?php if ($influencer['instagram_verified']): ?>
                                                            <i class="fas fa-check-circle text-primary ms-1"></i>
                                                        <?php endif; ?>
                                                        <br><small class="text-muted"><?php echo number_format($influencer['instagram_followers']); ?> followers</small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <?php if ($influencer['category']): ?>
                                                        <span class="badge bg-light text-dark">
                                                            <?php echo htmlspecialchars(ucfirst($influencer['category'])); ?>
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="text-center">
                                                        <strong class="text-primary"><?php echo $influencer['total_campaigns']; ?></strong>
                                                        <small class="d-block text-muted">
                                                            <?php echo $influencer['completed_campaigns']; ?> completed
                                                        </small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="text-center">
                                                        <strong class="text-success"><?php echo format_currency($influencer['total_earned']); ?></strong>
                                                        <small class="d-block text-muted">
                                                            <?php echo format_currency($influencer['pending_payout']); ?> pending
                                                        </small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <?php
                                                    $status_class = '';
                                                    switch ($influencer['status']) {
                                                        case 'pending':
                                                            $status_class = 'bg-warning';
                                                            break;
                                                        case 'approved':
                                                            $status_class = 'bg-success';
                                                            break;
                                                        case 'rejected':
                                                            $status_class = 'bg-danger';
                                                            break;
                                                        case 'suspended':
                                                            $status_class = 'bg-secondary';
                                                            break;
                                                    }
                                                    ?>
                                                    <span class="badge <?php echo $status_class; ?>">
                                                        <?php echo ucfirst($influencer['status']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if ($influencer['status'] === 'pending'): ?>
                                                        <!-- Quick Actions for Pending -->
                                                        <div class="btn-group" role="group">
                                                            <button class="btn btn-sm btn-success"
                                                                    onclick="quickApprove(<?php echo $influencer['id']; ?>)"
                                                                    title="Quick Approve">
                                                                <i class="fas fa-check"></i>
                                                            </button>
                                                            <button class="btn btn-sm btn-danger"
                                                                    onclick="quickReject(<?php echo $influencer['id']; ?>)"
                                                                    title="Quick Reject">
                                                                <i class="fas fa-times"></i>
                                                            </button>
                                                            <button class="btn btn-sm btn-outline-primary"
                                                                    data-bs-toggle="modal"
                                                                    data-bs-target="#influencerModal<?php echo $influencer['id']; ?>"
                                                                    title="View Details">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                        </div>
                                                    <?php else: ?>
                                                        <!-- Regular Actions -->
                                                        <div class="btn-group" role="group">
                                                            <button class="btn btn-sm btn-outline-primary"
                                                                    data-bs-toggle="modal"
                                                                    data-bs-target="#influencerModal<?php echo $influencer['id']; ?>">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            <button class="btn btn-sm btn-outline-warning"
                                                                    data-bs-toggle="modal"
                                                                    data-bs-target="#statusModal<?php echo $influencer['id']; ?>">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <button class="btn btn-sm btn-outline-danger delete-btn"
                                                                    data-bs-toggle="modal"
                                                                    data-bs-target="#deleteModal<?php echo $influencer['id']; ?>">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modals for each influencer -->
<?php foreach ($influencers as $influencer): ?>
    <!-- Influencer Details Modal -->
    <div class="modal fade" id="influencerModal<?php echo $influencer['id']; ?>" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Influencer Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-4 text-center">
                            <?php if ($influencer['profile_image']): ?>
                                <img src="<?php echo htmlspecialchars($influencer['profile_image']); ?>"
                                     alt="Profile" class="rounded-circle mb-3"
                                     style="width: 120px; height: 120px; object-fit: cover;">
                            <?php else: ?>
                                <div class="bg-warning rounded-circle d-inline-flex align-items-center justify-content-center mb-3"
                                     style="width: 120px; height: 120px;">
                                    <i class="fas fa-star fa-3x text-white"></i>
                                </div>
                            <?php endif; ?>
                            <h5><?php echo htmlspecialchars($influencer['full_name']); ?></h5>
                            <p class="text-muted">@<?php echo htmlspecialchars($influencer['username']); ?></p>
                        </div>
                        <div class="col-md-8">
                            <h6 class="fw-bold">Personal Information</h6>
                            <p><strong>Email:</strong> <?php echo htmlspecialchars($influencer['email']); ?></p>
                            <p><strong>Phone:</strong> <?php echo htmlspecialchars($influencer['phone']); ?></p>
                            <p><strong>Category:</strong> <?php echo htmlspecialchars(ucfirst($influencer['category'])); ?></p>

                            <h6 class="fw-bold mt-3">Instagram Details</h6>
                            <p><strong>Handle:</strong> @<?php echo htmlspecialchars($influencer['instagram_handle']); ?>
                                <?php if ($influencer['instagram_verified']): ?>
                                    <i class="fas fa-check-circle text-primary ms-1"></i>
                                <?php endif; ?>
                            </p>
                            <p><strong>Followers:</strong> <?php echo number_format($influencer['instagram_followers']); ?></p>
                            <p><strong>Engagement Rate:</strong> <?php echo number_format($influencer['avg_engagement'] ?? 0, 1); ?>%</p>

                            <h6 class="fw-bold mt-3">Performance</h6>
                            <p><strong>Total Campaigns:</strong> <?php echo $influencer['total_campaigns']; ?></p>
                            <p><strong>Completed:</strong> <?php echo $influencer['completed_campaigns']; ?></p>
                            <p><strong>Total Earned:</strong> <?php echo format_currency($influencer['total_earned']); ?></p>
                            <p><strong>Pending Payout:</strong> <?php echo format_currency($influencer['pending_payout']); ?></p>

                            <h6 class="fw-bold mt-3">Account Status</h6>
                            <p><strong>Status:</strong>
                                <span class="badge <?php
                                    switch ($influencer['status']) {
                                        case 'pending': echo 'bg-warning'; break;
                                        case 'approved': echo 'bg-success'; break;
                                        case 'rejected': echo 'bg-danger'; break;
                                        case 'suspended': echo 'bg-secondary'; break;
                                    }
                                ?>">
                                    <?php echo ucfirst($influencer['status']); ?>
                                </span>
                            </p>
                            <p><strong>Joined:</strong> <?php echo date('F j, Y', strtotime($influencer['created_at'])); ?></p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <?php if ($influencer['status'] === 'pending'): ?>
                        <button type="button" class="btn btn-success" data-bs-dismiss="modal"
                                data-bs-toggle="modal" data-bs-target="#statusModal<?php echo $influencer['id']; ?>">
                            <i class="fas fa-check me-1"></i>Approve/Reject
                        </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Update Modal -->
    <div class="modal fade" id="statusModal<?php echo $influencer['id']; ?>" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Update Influencer Status</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <div class="text-center mb-3">
                            <h6><?php echo htmlspecialchars($influencer['full_name']); ?></h6>
                            <p class="text-muted">@<?php echo htmlspecialchars($influencer['instagram_handle']); ?> • <?php echo number_format($influencer['instagram_followers']); ?> followers</p>
                        </div>

                        <input type="hidden" name="influencer_id" value="<?php echo $influencer['id']; ?>">
                        <div class="mb-3">
                            <label for="status<?php echo $influencer['id']; ?>" class="form-label">Status</label>
                            <select class="form-select" id="status<?php echo $influencer['id']; ?>" name="status" required>
                                <option value="pending" <?php echo $influencer['status'] === 'pending' ? 'selected' : ''; ?>>Pending Review</option>
                                <option value="approved" <?php echo $influencer['status'] === 'approved' ? 'selected' : ''; ?>>Approved</option>
                                <option value="rejected" <?php echo $influencer['status'] === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                                <option value="suspended" <?php echo $influencer['status'] === 'suspended' ? 'selected' : ''; ?>>Suspended</option>
                            </select>
                        </div>

                        <?php if ($influencer['status'] === 'pending'): ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Review Guidelines:</strong>
                                <ul class="mb-0 mt-2">
                                    <li>Check Instagram profile authenticity</li>
                                    <li>Verify follower count and engagement</li>
                                    <li>Ensure content quality and brand alignment</li>
                                    <li>Confirm contact information accuracy</li>
                                </ul>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="update_status" class="btn btn-primary">Update Status</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Modal -->
    <div class="modal fade" id="deleteModal<?php echo $influencer['id']; ?>" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Delete Influencer</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete influencer <strong><?php echo htmlspecialchars($influencer['full_name']); ?></strong>?</p>
                    <p class="text-danger"><strong>Warning:</strong> This will also delete all their campaigns, submissions, and payout records. This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form method="POST" class="d-inline">
                        <input type="hidden" name="influencer_id" value="<?php echo $influencer['id']; ?>">
                        <button type="submit" name="delete_influencer" class="btn btn-danger">Delete Influencer</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php endforeach; ?>

<script>
function selectAllInfluencers() {
    document.querySelectorAll('.influencer-checkbox').forEach(cb => cb.checked = true);
    document.getElementById('selectAllInfluencersCheckbox').checked = true;
}

function selectNoneInfluencers() {
    document.querySelectorAll('.influencer-checkbox').forEach(cb => cb.checked = false);
    document.getElementById('selectAllInfluencersCheckbox').checked = false;
}

// Handle select all checkbox
document.getElementById('selectAllInfluencersCheckbox')?.addEventListener('change', function() {
    document.querySelectorAll('.influencer-checkbox').forEach(cb => cb.checked = this.checked);
});

// Quick approve/reject buttons
function quickApprove(influencerId) {
    if (confirm('Are you sure you want to approve this influencer?')) {
        submitQuickAction(influencerId, 'approved');
    }
}

function quickReject(influencerId) {
    if (confirm('Are you sure you want to reject this influencer?')) {
        submitQuickAction(influencerId, 'rejected');
    }
}

function submitQuickAction(influencerId, status) {
    // Create and submit form
    const form = document.createElement('form');
    form.method = 'POST';
    form.style.display = 'none';

    // Add form fields
    const influencerIdInput = document.createElement('input');
    influencerIdInput.type = 'hidden';
    influencerIdInput.name = 'influencer_id';
    influencerIdInput.value = influencerId;

    const statusInput = document.createElement('input');
    statusInput.type = 'hidden';
    statusInput.name = 'status';
    statusInput.value = status;

    const actionInput = document.createElement('input');
    actionInput.type = 'hidden';
    actionInput.name = 'update_status';
    actionInput.value = '1';

    form.appendChild(influencerIdInput);
    form.appendChild(statusInput);
    form.appendChild(actionInput);

    document.body.appendChild(form);
    form.submit();
}
</script>

<?php include '../includes/footer.php'; ?>
