<?php
require_once '../config.php';
require_login(['admin']);

$db = Database::getInstance();

$success = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];
        $influencer_id = intval($_POST['influencer_id'] ?? 0);
        
        if ($action === 'verify' && $influencer_id > 0) {
            try {
                $db->update('influencers', [
                    'instagram_verified' => 1,
                    'updated_at' => date('Y-m-d H:i:s')
                ], 'id = ?', [$influencer_id]);
                
                $success = 'Influencer marked as Instagram Blue Check verified successfully!';
            } catch (Exception $e) {
                $error = 'Failed to update verification status.';
            }
        } elseif ($action === 'unverify' && $influencer_id > 0) {
            try {
                $db->update('influencers', [
                    'instagram_verified' => 0,
                    'updated_at' => date('Y-m-d H:i:s')
                ], 'id = ?', [$influencer_id]);
                
                $success = 'Influencer Blue Check verification removed successfully!';
            } catch (Exception $e) {
                $error = 'Failed to update verification status.';
            }
        }
    }
}

// Get filter parameters
$filter = $_GET['filter'] ?? 'all';
$search = trim($_GET['search'] ?? '');

// Build query based on filters
$where_conditions = [];
$params = [];

if ($filter === 'verified') {
    $where_conditions[] = 'instagram_verified = 1';
} elseif ($filter === 'unverified') {
    $where_conditions[] = 'instagram_verified = 0 OR instagram_verified IS NULL';
}

if (!empty($search)) {
    $where_conditions[] = '(full_name LIKE ? OR instagram_handle LIKE ? OR email LIKE ?)';
    $search_param = '%' . $search . '%';
    $params = array_merge($params, [$search_param, $search_param, $search_param]);
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get influencers
$influencers = $db->fetchAll("
    SELECT 
        id,
        full_name,
        email,
        instagram_handle,
        instagram_followers,
        instagram_verified,
        category,
        status,
        created_at
    FROM influencers 
    $where_clause
    ORDER BY instagram_verified DESC, instagram_followers DESC
", $params);

// Get statistics
$stats = [
    'total' => $db->fetch("SELECT COUNT(*) as count FROM influencers")['count'],
    'verified' => $db->fetch("SELECT COUNT(*) as count FROM influencers WHERE instagram_verified = 1")['count'],
    'unverified' => $db->fetch("SELECT COUNT(*) as count FROM influencers WHERE instagram_verified = 0 OR instagram_verified IS NULL")['count']
];

$page_title = 'Instagram Blue Check Management';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-xl-2 sidebar p-0">
            <div class="p-3">
                <h5 class="text-white mb-4">
                    <i class="fas fa-shield-alt me-2"></i>Admin Panel
                </h5>
                
                                                <nav class="nav flex-column">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a class="nav-link" href="users.php">
                        <i class="fas fa-users"></i>Users
                    </a>
                    <a class="nav-link" href="influencers.php">
                        <i class="fas fa-star"></i>Influencers
                    </a>
                    <a class="nav-link" href="brands.php">
                        <i class="fas fa-building"></i>Brands
                    </a>
                    <a class="nav-link" href="campaigns.php">
                        <i class="fas fa-bullhorn"></i>Campaigns
                    </a>
                    <a class="nav-link" href="submissions.php">
                        <i class="fas fa-clipboard-check"></i>Submissions
                    </a>
                    <a class="nav-link" href="assignments.php">
                        <i class="fas fa-user-check"></i>Assignments
                    </a>
                    <a class="nav-link" href="payouts.php">
                        <i class="fas fa-money-bill-wave"></i>Payouts
                    </a>
                    
                    <!-- Instagram & Verification Section -->
                    <hr class="my-2 text-light">
                    <small class="text-light px-3 mb-2">Instagram & Verification</small>
                    
                    <a class="nav-link" href="instagram_verifications.php">
                        <i class="fab fa-instagram"></i>Instagram Verifications
                    </a>
                    <a class="nav-link" href="influencer_follow_check.php">
                        <i class="fas fa-user-check"></i>Follow Status Check
                    </a>
                    <a class="nav-link active" href="instagram_blue_check.php">
                        <i class="fas fa-check-circle"></i>Blue Check Management
                    </a>
                    <a class="nav-link" href="user_verification_status.php">
                        <i class="fas fa-clipboard-check"></i>User Follow Status
                    </a>
                    <a class="nav-link" href="suspended_users.php">
                        <i class="fas fa-ban"></i>Suspended Users
                    </a>
                    
                    <!-- Badge Management Section -->
                    <hr class="my-2 text-light">
                    <small class="text-light px-3 mb-2">Badge Management</small>
                    
                    <a class="nav-link" href="badge_management.php">
                        <i class="fas fa-award"></i>Badge Management
                    </a>
                    <a class="nav-link" href="user_badge_assignment.php">
                        <i class="fas fa-user-tag"></i>Assign Badges
                    </a>
                    
                    <!-- System Section -->
                    <hr class="my-2 text-light">
                    <small class="text-light px-3 mb-2">System</small>
                    
                    <a class="nav-link" href="settings.php">
                        <i class="fas fa-cog"></i>Settings
                    </a>
                    <a class="nav-link" href="instagram_settings.php">
                        <i class="fab fa-instagram"></i>Instagram Settings
                    </a>
                    <a class="nav-link" href="export.php">
                        <i class="fas fa-download"></i>Export Data
                    </a>
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10 main-content" style="margin-left: auto;">
            <!-- Top Bar -->
            <div class="bg-white shadow-sm p-3 mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">Instagram Blue Check Management</h4>
                        <small class="text-muted">Manage Instagram blue check verification status for influencers</small>
                    </div>
                </div>
            </div>
            
            <div class="p-3">
                <!-- Success/Error Messages -->
                <?php if ($success): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-4 mb-3">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['total']; ?></h3>
                                <p class="mb-0">Total Influencers</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <div class="card stats-card success">
                            <div class="card-body text-center">
                                <i class="fas fa-check-circle fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['verified']; ?></h3>
                                <p class="mb-0">Blue Check Verified</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <div class="card stats-card warning">
                            <div class="card-body text-center">
                                <i class="fas fa-times-circle fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['unverified']; ?></h3>
                                <p class="mb-0">Not Blue Check Verified</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters and Search -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <label for="filter" class="form-label">Filter by Status</label>
                                <select class="form-select" id="filter" name="filter">
                                    <option value="all" <?php echo $filter === 'all' ? 'selected' : ''; ?>>All Influencers</option>
                                    <option value="verified" <?php echo $filter === 'verified' ? 'selected' : ''; ?>>Blue Check Verified</option>
                                    <option value="unverified" <?php echo $filter === 'unverified' ? 'selected' : ''; ?>>Not Blue Check Verified</option>
                                </select>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="<?php echo htmlspecialchars($search); ?>" 
                                       placeholder="Search by name, Instagram handle, or email">
                            </div>
                            
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-1"></i>Search
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Influencers List -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>Influencers Blue Check Status
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($influencers)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                                <h6 class="text-muted">No influencers found</h6>
                                <p class="text-muted">Try adjusting your search criteria.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Influencer</th>
                                            <th>Instagram</th>
                                            <th>Followers</th>
                                            <th>Category</th>
                                            <th>Blue Check Status</th>
                                            <th>Account Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($influencers as $influencer): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <h6 class="mb-1"><?php echo htmlspecialchars($influencer['full_name']); ?></h6>
                                                    <small class="text-muted"><?php echo htmlspecialchars($influencer['email']); ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong>@<?php echo htmlspecialchars($influencer['instagram_handle']); ?></strong>
                                                    <?php if ($influencer['instagram_verified']): ?>
                                                        <i class="fas fa-check-circle text-primary ms-1" title="Blue Check Verified"></i>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?php echo number_format($influencer['instagram_followers']); ?></span>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary"><?php echo ucfirst($influencer['category'] ?? 'Not set'); ?></span>
                                            </td>
                                            <td>
                                                <?php if ($influencer['instagram_verified']): ?>
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-check-circle me-1"></i>Blue Check Verified
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge bg-warning">
                                                        <i class="fas fa-times-circle me-1"></i>Not Blue Check Verified
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                $status_class = '';
                                                switch ($influencer['status']) {
                                                    case 'approved':
                                                        $status_class = 'bg-success';
                                                        break;
                                                    case 'pending':
                                                        $status_class = 'bg-warning';
                                                        break;
                                                    case 'rejected':
                                                        $status_class = 'bg-danger';
                                                        break;
                                                    case 'suspended':
                                                        $status_class = 'bg-dark';
                                                        break;
                                                    default:
                                                        $status_class = 'bg-secondary';
                                                }
                                                ?>
                                                <span class="badge <?php echo $status_class; ?>">
                                                    <?php echo ucfirst($influencer['status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <?php if ($influencer['instagram_verified']): ?>
                                                        <form method="POST" style="display: inline;">
                                                            <input type="hidden" name="action" value="unverify">
                                                            <input type="hidden" name="influencer_id" value="<?php echo $influencer['id']; ?>">
                                                            <button type="submit" class="btn btn-sm btn-outline-warning"
                                                                    onclick="return confirm('Remove Blue Check verification for this influencer?')">
                                                                <i class="fas fa-times me-1"></i>Remove Blue Check
                                                            </button>
                                                        </form>
                                                    <?php else: ?>
                                                        <form method="POST" style="display: inline;">
                                                            <input type="hidden" name="action" value="verify">
                                                            <input type="hidden" name="influencer_id" value="<?php echo $influencer['id']; ?>">
                                                            <button type="submit" class="btn btn-sm btn-success"
                                                                    onclick="return confirm('Mark this influencer as Blue Check verified?')">
                                                                <i class="fas fa-check me-1"></i>Mark Blue Check Verified
                                                            </button>
                                                        </form>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Information Card -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>About Blue Check Verification
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="fw-bold">What is Blue Check Verification?</h6>
                                <ul class="small">
                                    <li>Official Instagram verification badge (blue checkmark)</li>
                                    <li>Indicates authentic, notable public figures or brands</li>
                                    <li>Separate from platform verification (following our accounts)</li>
                                    <li>Enhances influencer credibility and reach</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 class="fw-bold">Management Guidelines</h6>
                                <ul class="small">
                                    <li>Only mark as verified if influencer has actual blue check</li>
                                    <li>Verify by checking their Instagram profile directly</li>
                                    <li>This affects how they appear in search and recommendations</li>
                                    <li>Can be updated anytime based on current status</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
