<?php
require_once '../config.php';
require_login(['admin']);

$db = Database::getInstance();

// Handle settings update
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['update_instagram_settings'])) {
        $founder_token = sanitize_input($_POST['founder_access_token']);
        $brand_token = sanitize_input($_POST['brand_access_token']);
        $cache_duration = intval($_POST['cache_duration']);
        
        try {
            // Update or insert settings
            $settings = [
                'instagram_founder_token' => $founder_token,
                'instagram_brand_token' => $brand_token,
                'instagram_cache_duration' => $cache_duration
            ];
            
            foreach ($settings as $key => $value) {
                $existing = $db->fetch("SELECT id FROM settings WHERE setting_key = ?", [$key]);
                
                if ($existing) {
                    $db->update('settings', 
                        ['setting_value' => $value, 'updated_by' => get_user_id()], 
                        'setting_key = ?', 
                        [$key]
                    );
                } else {
                    $db->insert('settings', [
                        'setting_key' => $key,
                        'setting_value' => $value,
                        'description' => ucfirst(str_replace('_', ' ', $key)),
                        'updated_by' => get_user_id()
                    ]);
                }
            }
            
            $_SESSION['success'] = 'Instagram settings updated successfully!';
        } catch (Exception $e) {
            $_SESSION['error'] = 'Failed to update Instagram settings.';
        }
    }
    
    if (isset($_POST['clear_cache'])) {
        try {
            // Clear Instagram cache files
            $cache_dir = sys_get_temp_dir();
            $cache_files = glob($cache_dir . '/instagram_cache_*.json');
            
            foreach ($cache_files as $file) {
                unlink($file);
            }
            
            $_SESSION['success'] = 'Instagram cache cleared successfully!';
        } catch (Exception $e) {
            $_SESSION['error'] = 'Failed to clear Instagram cache.';
        }
    }
}

// Get current settings
$founder_token = $db->fetch("SELECT setting_value FROM settings WHERE setting_key = 'instagram_founder_token'")['setting_value'] ?? '';
$brand_token = $db->fetch("SELECT setting_value FROM settings WHERE setting_key = 'instagram_brand_token'")['setting_value'] ?? '';
$cache_duration = $db->fetch("SELECT setting_value FROM settings WHERE setting_key = 'instagram_cache_duration'")['setting_value'] ?? '3600';

$page_title = 'Instagram Settings';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-xl-2 sidebar p-0">
            <div class="p-3">
                <h5 class="text-white mb-4">
                    <i class="fas fa-crown me-2"></i>Admin Panel
                </h5>
                
                                <nav class="nav flex-column">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a class="nav-link" href="users.php">
                        <i class="fas fa-users"></i>Users
                    </a>
                    <a class="nav-link" href="influencers.php">
                        <i class="fas fa-star"></i>Influencers
                    </a>
                    <a class="nav-link" href="brands.php">
                        <i class="fas fa-building"></i>Brands
                    </a>
                    <a class="nav-link" href="campaigns.php">
                        <i class="fas fa-bullhorn"></i>Campaigns
                    </a>
                    <a class="nav-link" href="submissions.php">
                        <i class="fas fa-clipboard-check"></i>Submissions
                    </a>
                    <a class="nav-link" href="assignments.php">
                        <i class="fas fa-user-check"></i>Assignments
                    </a>
                    <a class="nav-link" href="payouts.php">
                        <i class="fas fa-money-bill-wave"></i>Payouts
                    </a>
                    
                    <!-- Instagram & Verification Section -->
                    <hr class="my-2 text-light">
                    <small class="text-light px-3 mb-2">Instagram & Verification</small>
                    
                    <a class="nav-link" href="instagram_verifications.php">
                        <i class="fab fa-instagram"></i>Instagram Verifications
                    </a>
                    <a class="nav-link" href="influencer_follow_check.php">
                        <i class="fas fa-user-check"></i>Follow Status Check
                    </a>
                    <a class="nav-link" href="instagram_blue_check.php">
                        <i class="fas fa-check-circle"></i>Blue Check Management
                    </a>
                    <a class="nav-link" href="user_verification_status.php">
                        <i class="fas fa-clipboard-check"></i>User Follow Status
                    </a>
                    <a class="nav-link" href="suspended_users.php">
                        <i class="fas fa-ban"></i>Suspended Users
                    </a>
                    
                    <!-- Badge Management Section -->
                    <hr class="my-2 text-light">
                    <small class="text-light px-3 mb-2">Badge Management</small>
                    
                    <a class="nav-link" href="badge_management.php">
                        <i class="fas fa-award"></i>Badge Management
                    </a>
                    <a class="nav-link" href="user_badge_assignment.php">
                        <i class="fas fa-user-tag"></i>Assign Badges
                    </a>
                    
                    <!-- System Section -->
                    <hr class="my-2 text-light">
                    <small class="text-light px-3 mb-2">System</small>
                    
                    <a class="nav-link" href="settings.php">
                        <i class="fas fa-cog"></i>Settings
                    </a>
                    <a class="nav-link active" href="instagram_settings.php">
                        <i class="fab fa-instagram"></i>Instagram Settings
                    </a>
                    <a class="nav-link" href="export.php">
                        <i class="fas fa-download"></i>Export Data
                    </a>
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10 main-content" style="margin-left: auto;">
            <!-- Top Bar -->
            <div class="bg-white shadow-sm p-3 mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">Instagram Integration</h4>
                        <small class="text-muted">Manage Instagram API settings and live data</small>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-outline-primary d-lg-none ms-2" onclick="toggleSidebar()">
                            <i class="fas fa-bars"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="p-3">
                <!-- Instagram Status Cards -->
                <div class="row mb-4">
                    <div class="col-lg-6 mb-3">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-3" 
                                         style="width: 50px; height: 50px;">
                                        <i class="fab fa-instagram fa-lg text-primary"></i>
                                    </div>
                                    <div>
                                        <h6 class="fw-bold mb-1">@thesyedabubakkar</h6>
                                        <p class="text-muted mb-0">
                                            <?php echo !empty($founder_token) ? 'Connected' : 'Not Connected'; ?>
                                        </p>
                                    </div>
                                    <div class="ms-auto">
                                        <span class="badge bg-<?php echo !empty($founder_token) ? 'success' : 'warning'; ?>">
                                            <?php echo !empty($founder_token) ? 'Active' : 'Inactive'; ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-6 mb-3">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="bg-danger bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-3" 
                                         style="width: 50px; height: 50px;">
                                        <i class="fab fa-instagram fa-lg text-danger"></i>
                                    </div>
                                    <div>
                                        <h6 class="fw-bold mb-1">@real_earners.in</h6>
                                        <p class="text-muted mb-0">
                                            <?php echo !empty($brand_token) ? 'Connected' : 'Not Connected'; ?>
                                        </p>
                                    </div>
                                    <div class="ms-auto">
                                        <span class="badge bg-<?php echo !empty($brand_token) ? 'success' : 'warning'; ?>">
                                            <?php echo !empty($brand_token) ? 'Active' : 'Inactive'; ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Settings Form -->
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">
                            <i class="fab fa-instagram me-2"></i>Instagram API Configuration
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Setup Required:</strong> Follow the <a href="../INSTAGRAM_API_SETUP.md" target="_blank">Instagram API Setup Guide</a> to get your access tokens.
                        </div>
                        
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="founder_access_token" class="form-label">
                                        Founder Access Token (@thesyedabubakkar)
                                    </label>
                                    <input type="password" class="form-control" id="founder_access_token" 
                                           name="founder_access_token" value="<?php echo htmlspecialchars($founder_token); ?>"
                                           placeholder="Enter Instagram access token">
                                    <small class="text-muted">Long-lived access token for @thesyedabubakkar</small>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="brand_access_token" class="form-label">
                                        Brand Access Token (@real_earners.in)
                                    </label>
                                    <input type="password" class="form-control" id="brand_access_token" 
                                           name="brand_access_token" value="<?php echo htmlspecialchars($brand_token); ?>"
                                           placeholder="Enter Instagram access token">
                                    <small class="text-muted">Long-lived access token for @real_earners.in</small>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="cache_duration" class="form-label">Cache Duration (seconds)</label>
                                    <select class="form-select" id="cache_duration" name="cache_duration">
                                        <option value="1800" <?php echo $cache_duration == '1800' ? 'selected' : ''; ?>>30 minutes</option>
                                        <option value="3600" <?php echo $cache_duration == '3600' ? 'selected' : ''; ?>>1 hour</option>
                                        <option value="7200" <?php echo $cache_duration == '7200' ? 'selected' : ''; ?>>2 hours</option>
                                        <option value="21600" <?php echo $cache_duration == '21600' ? 'selected' : ''; ?>>6 hours</option>
                                    </select>
                                    <small class="text-muted">How long to cache Instagram data</small>
                                </div>
                            </div>
                            
                            <div class="d-flex gap-3">
                                <button type="submit" name="update_instagram_settings" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Save Settings
                                </button>
                                
                                <button type="submit" name="clear_cache" class="btn btn-outline-warning">
                                    <i class="fas fa-trash me-2"></i>Clear Cache
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Setup Instructions -->
                <div class="card border-0 shadow-sm mt-4">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">
                            <i class="fas fa-book me-2"></i>Quick Setup Guide
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="fw-bold">1. Create Facebook App</h6>
                                <p class="text-muted">Visit Facebook Developers and create a new business app.</p>
                                
                                <h6 class="fw-bold">2. Add Instagram Basic Display</h6>
                                <p class="text-muted">Add Instagram Basic Display product to your app.</p>
                            </div>
                            <div class="col-md-6">
                                <h6 class="fw-bold">3. Generate Access Tokens</h6>
                                <p class="text-muted">Generate long-lived access tokens for both accounts.</p>
                                
                                <h6 class="fw-bold">4. Update Settings</h6>
                                <p class="text-muted">Paste the tokens above and save settings.</p>
                            </div>
                        </div>
                        
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Important:</strong> Access tokens expire every 60 days. Set up automatic refresh or manually update them.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
