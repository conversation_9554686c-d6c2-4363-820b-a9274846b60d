<?php
require_once '../config.php';
require_once '../includes/instagram_verification.php';
require_login(['admin']);

$db = Database::getInstance();
$verification_system = new InstagramVerification();

// Handle verification actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['verify_user'])) {
        $verification_id = intval($_POST['verification_id']);
        $verified_accounts = $_POST['verified_accounts'] ?? [];
        
        try {
            $verification_system->verifyFollows($verification_id, $verified_accounts);
            $_SESSION['success'] = 'User verification updated successfully!';
        } catch (Exception $e) {
            $_SESSION['error'] = 'Failed to update verification.';
        }
    }
}

// Get all pending verifications
$verifications = $db->fetchAll("
    SELECT iv.*, 
           CASE 
               WHEN iv.user_type = 'user' THEN u.full_name
               WHEN iv.user_type = 'influencer' THEN i.full_name
               WHEN iv.user_type = 'brand' THEN b.company_name
           END as user_name,
           CASE 
               WHEN iv.user_type = 'user' THEN u.email
               WHEN iv.user_type = 'influencer' THEN i.email
               WHEN iv.user_type = 'brand' THEN b.email
           END as user_email
    FROM instagram_verifications iv
    LEFT JOIN users u ON iv.user_type = 'user' AND iv.user_id = u.id
    LEFT JOIN influencers i ON iv.user_type = 'influencer' AND iv.user_id = i.id
    LEFT JOIN brands b ON iv.user_type = 'brand' AND iv.user_id = b.id
    ORDER BY iv.submitted_at DESC
");

$page_title = 'Instagram Verifications';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-xl-2 sidebar p-0">
            <div class="p-3">
                <h5 class="text-white mb-4">
                    <i class="fas fa-crown me-2"></i>Admin Panel
                </h5>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a class="nav-link" href="users.php">
                        <i class="fas fa-users"></i>Users
                    </a>
                    <a class="nav-link" href="influencers.php">
                        <i class="fas fa-star"></i>Influencers
                    </a>
                    <a class="nav-link" href="campaigns.php">
                        <i class="fas fa-bullhorn"></i>Campaigns
                    </a>
                    <a class="nav-link" href="submissions.php">
                        <i class="fas fa-clipboard-check"></i>Submissions
                    </a>
                    <a class="nav-link" href="payouts.php">
                        <i class="fas fa-money-bill-wave"></i>Payouts
                    </a>
                    <a class="nav-link" href="brands.php">
                        <i class="fas fa-building"></i>Brands
                    </a>
                    <a class="nav-link active" href="instagram_verifications.php">
                        <i class="fab fa-instagram"></i>Instagram Verifications
                    </a>
                    <a class="nav-link" href="settings.php">
                        <i class="fas fa-cog"></i>Settings
                    </a>
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10 main-content" style="margin-left: auto;">
            <!-- Top Bar -->
            <div class="bg-white shadow-sm p-3 mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">Instagram Verifications</h4>
                        <small class="text-muted">Manage user Instagram follow verifications</small>
                    </div>
                </div>
            </div>
            
            <div class="p-3">
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <i class="fas fa-clock fa-2x mb-2"></i>
                                <h3 class="fw-bold">
                                    <?php echo count(array_filter($verifications, function($v) { return $v['verification_status'] === 'pending'; })); ?>
                                </h3>
                                <p class="mb-0">Pending</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card success">
                            <div class="card-body text-center">
                                <i class="fas fa-check-circle fa-2x mb-2"></i>
                                <h3 class="fw-bold">
                                    <?php echo count(array_filter($verifications, function($v) { return $v['verified'] == 1; })); ?>
                                </h3>
                                <p class="mb-0">Verified</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card warning">
                            <div class="card-body text-center">
                                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                                <h3 class="fw-bold">
                                    <?php echo count(array_filter($verifications, function($v) { return $v['verification_status'] === 'partial'; })); ?>
                                </h3>
                                <p class="mb-0">Partial</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card info">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo count($verifications); ?></h3>
                                <p class="mb-0">Total Requests</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Verifications Table -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fab fa-instagram me-2"></i>Verification Requests
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($verifications)): ?>
                            <div class="text-center py-5">
                                <i class="fab fa-instagram fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No verification requests</h5>
                                <p class="text-muted">Users will appear here when they submit Instagram verification requests.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>User</th>
                                            <th>Type</th>
                                            <th>Instagram</th>
                                            <th>Status</th>
                                            <th>Submitted</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($verifications as $verification): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($verification['user_name']); ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($verification['user_email']); ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo $verification['user_type'] === 'user' ? 'primary' : 
                                                        ($verification['user_type'] === 'influencer' ? 'warning' : 'info'); 
                                                ?>">
                                                    <?php echo ucfirst($verification['user_type']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <a href="https://instagram.com/<?php echo htmlspecialchars($verification['instagram_username']); ?>" 
                                                   target="_blank" class="text-decoration-none">
                                                    <i class="fab fa-instagram me-1"></i>@<?php echo htmlspecialchars($verification['instagram_username']); ?>
                                                </a>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo $verification['verified'] ? 'success' : 
                                                        ($verification['verification_status'] === 'pending' ? 'warning' : 'secondary'); 
                                                ?>">
                                                    <?php echo $verification['verified'] ? 'Verified' : ucfirst($verification['verification_status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <small><?php echo date('M j, Y g:i A', strtotime($verification['submitted_at'])); ?></small>
                                            </td>
                                            <td>
                                                <?php if (!$verification['verified']): ?>
                                                <button class="btn btn-sm btn-primary" 
                                                        data-bs-toggle="modal" 
                                                        data-bs-target="#verifyModal<?php echo $verification['id']; ?>">
                                                    <i class="fas fa-check me-1"></i>Verify
                                                </button>
                                                <?php else: ?>
                                                <span class="text-success">
                                                    <i class="fas fa-check-circle me-1"></i>Complete
                                                </span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Verification Modals -->
<?php foreach ($verifications as $verification): ?>
<div class="modal fade" id="verifyModal<?php echo $verification['id']; ?>" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Verify Instagram Follows</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <p><strong>User:</strong> <?php echo htmlspecialchars($verification['user_name']); ?></p>
                    <p><strong>Instagram:</strong> @<?php echo htmlspecialchars($verification['instagram_username']); ?></p>
                    
                    <div class="mb-3">
                        <label class="form-label">Check which accounts they follow:</label>
                        <?php 
                        $required_accounts = $verification_system->getRequiredAccounts();
                        $verified_accounts = json_decode($verification['accounts_verified'], true) ?: [];
                        ?>
                        
                        <?php foreach ($required_accounts as $account): ?>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" 
                                   name="verified_accounts[]" 
                                   value="<?php echo $account; ?>"
                                   id="account_<?php echo $verification['id']; ?>_<?php echo $account; ?>"
                                   <?php echo in_array($account, $verified_accounts) ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="account_<?php echo $verification['id']; ?>_<?php echo $account; ?>">
                                @<?php echo $account; ?>
                                <small class="text-muted">
                                    - <a href="https://instagram.com/<?php echo $account; ?>" target="_blank">Check</a>
                                </small>
                            </label>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <input type="hidden" name="verification_id" value="<?php echo $verification['id']; ?>">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="verify_user" class="btn btn-primary">Update Verification</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endforeach; ?>

<?php include '../includes/footer.php'; ?>
