<?php
require_once '../config.php';
require_once '../includes/instagram_verification.php';
require_login(['admin']);

$db = Database::getInstance();
$verification_system = new InstagramVerification();

// Handle verification actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['verify_user'])) {
        $verification_id = intval($_POST['verification_id']);
        $verified_accounts = $_POST['verified_accounts'] ?? [];
        $follow_proof = $_POST['follow_proof'] ?? '';
        $notes = sanitize_input($_POST['notes']);

        try {
            // Update verification with proof
            $all_verified = count($verified_accounts) >= count($verification_system->getRequiredAccounts());

            $db->update(
                'instagram_verifications',
                [
                    'accounts_verified' => json_encode($verified_accounts),
                    'verified' => $all_verified ? 1 : 0,
                    'verification_status' => $all_verified ? 'verified' : 'partial',
                    'verified_at' => $all_verified ? date('Y-m-d H:i:s') : null,
                    'verified_by' => get_user_id(),
                    'follow_proof_screenshots' => json_encode($follow_proof ? [$follow_proof] : []),
                    'notes' => $notes
                ],
                'id = ?',
                [$verification_id]
            );

            // Award verified follower badge if fully verified
            if ($all_verified) {
                $verification = $db->fetch("SELECT user_id, user_type FROM instagram_verifications WHERE id = ?", [$verification_id]);
                if ($verification) {
                    $badge_exists = $db->fetch(
                        "SELECT id FROM user_badges WHERE user_id = ? AND user_type = ? AND badge_type = 'verified_follower'",
                        [$verification['user_id'], $verification['user_type']]
                    );

                    if (!$badge_exists) {
                        $db->insert('user_badges', [
                            'user_id' => $verification['user_id'],
                            'user_type' => $verification['user_type'],
                            'badge_type' => 'verified_follower',
                            'badge_name' => 'Verified Follower',
                            'badge_description' => 'Successfully followed both required Instagram accounts',
                            'badge_icon' => 'fab fa-instagram',
                            'badge_color' => '#E4405F',
                            'awarded_by' => get_user_id()
                        ]);
                    }
                }
            }

            $_SESSION['success'] = 'User verification updated successfully!';
        } catch (Exception $e) {
            $_SESSION['error'] = 'Failed to update verification: ' . $e->getMessage();
        }
    }

    if (isset($_POST['suspend_user'])) {
        $verification_id = intval($_POST['verification_id']);
        $suspension_reason = sanitize_input($_POST['suspension_reason']);

        try {
            $db->update(
                'instagram_verifications',
                [
                    'verification_status' => 'suspended',
                    'suspension_date' => date('Y-m-d H:i:s'),
                    'suspension_reason' => $suspension_reason,
                    'verified_by' => get_user_id()
                ],
                'id = ?',
                [$verification_id]
            );

            $_SESSION['success'] = 'User suspended successfully!';
        } catch (Exception $e) {
            $_SESSION['error'] = 'Failed to suspend user.';
        }
    }

    if (isset($_POST['auto_suspend_overdue'])) {
        try {
            // Auto-suspend users who haven't followed after 2 days
            $overdue_date = date('Y-m-d H:i:s', strtotime('-2 days'));

            $db->update(
                'instagram_verifications',
                [
                    'verification_status' => 'suspended',
                    'suspension_date' => date('Y-m-d H:i:s'),
                    'suspension_reason' => 'Auto-suspended: Failed to follow required Instagram accounts within 2 days',
                    'verified_by' => get_user_id()
                ],
                'verification_status = ? AND submitted_at < ? AND verified = 0',
                ['pending', $overdue_date]
            );

            $_SESSION['success'] = 'Overdue users suspended automatically!';
        } catch (Exception $e) {
            $_SESSION['error'] = 'Failed to auto-suspend overdue users.';
        }
    }

    if (isset($_POST['unsuspend_user'])) {
        $verification_id = intval($_POST['verification_id']);
        $notes = sanitize_input($_POST['unsuspend_notes']);

        try {
            if ($verification_system->unsuspendUser($verification_id, get_user_id(), $notes)) {
                // Get user details to set session for them
                $verification_details = $db->fetch("SELECT user_id, user_type FROM instagram_verifications WHERE id = ?", [$verification_id]);

                $_SESSION['success'] = 'User unsuspended successfully! They have been notified and can now access the platform.';
            } else {
                $_SESSION['error'] = 'Failed to unsuspend user.';
            }
        } catch (Exception $e) {
            $_SESSION['error'] = 'Failed to unsuspend user: ' . $e->getMessage();
        }
    }
}

// Get all verifications with additional details
$verifications = $db->fetchAll("
    SELECT iv.*,
           CASE
               WHEN iv.user_type = 'user' THEN u.full_name
               WHEN iv.user_type = 'influencer' THEN i.full_name
               WHEN iv.user_type = 'brand' THEN b.company_name
           END as user_name,
           CASE
               WHEN iv.user_type = 'user' THEN u.email
               WHEN iv.user_type = 'influencer' THEN i.email
               WHEN iv.user_type = 'brand' THEN b.email
           END as user_email,
           CASE
               WHEN iv.user_type = 'user' THEN u.status
               WHEN iv.user_type = 'influencer' THEN i.status
               WHEN iv.user_type = 'brand' THEN b.status
           END as user_status,
           DATEDIFF(NOW(), iv.submitted_at) as days_since_submission,
           admin.full_name as verified_by_name
    FROM instagram_verifications iv
    LEFT JOIN users u ON iv.user_type = 'user' AND iv.user_id = u.id
    LEFT JOIN influencers i ON iv.user_type = 'influencer' AND iv.user_id = i.id
    LEFT JOIN brands b ON iv.user_type = 'brand' AND iv.user_id = b.id
    LEFT JOIN admin ON iv.verified_by = admin.id
    ORDER BY
        CASE iv.verification_status
            WHEN 'pending' THEN 1
            WHEN 'partial' THEN 2
            WHEN 'verified' THEN 3
            WHEN 'suspended' THEN 4
            ELSE 5
        END,
        iv.submitted_at DESC
");

// Get statistics
$stats = [
    'pending' => count(array_filter($verifications, function($v) { return $v['verification_status'] === 'pending'; })),
    'verified' => count(array_filter($verifications, function($v) { return $v['verified'] == 1; })),
    'partial' => count(array_filter($verifications, function($v) { return $v['verification_status'] === 'partial'; })),
    'suspended' => count(array_filter($verifications, function($v) { return $v['verification_status'] === 'suspended'; })),
    'overdue' => count(array_filter($verifications, function($v) {
        return $v['verification_status'] === 'pending' && $v['days_since_submission'] >= 2;
    }))
];

$page_title = 'Instagram Verifications';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-xl-2 sidebar p-0">
            <div class="p-3">
                <h5 class="text-white mb-4">
                    <i class="fas fa-crown me-2"></i>Admin Panel
                </h5>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a class="nav-link" href="users.php">
                        <i class="fas fa-users"></i>Users
                    </a>
                    <a class="nav-link" href="influencers.php">
                        <i class="fas fa-star"></i>Influencers
                    </a>
                    <a class="nav-link" href="campaigns.php">
                        <i class="fas fa-bullhorn"></i>Campaigns
                    </a>
                    <a class="nav-link" href="submissions.php">
                        <i class="fas fa-clipboard-check"></i>Submissions
                    </a>
                    <a class="nav-link" href="payouts.php">
                        <i class="fas fa-money-bill-wave"></i>Payouts
                    </a>
                    <a class="nav-link" href="brands.php">
                        <i class="fas fa-building"></i>Brands
                    </a>
                    <a class="nav-link active" href="instagram_verifications.php">
                        <i class="fab fa-instagram"></i>Instagram Verifications
                    </a>
                    <a class="nav-link" href="settings.php">
                        <i class="fas fa-cog"></i>Settings
                    </a>
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10 main-content" style="margin-left: auto;">
            <!-- Top Bar -->
            <div class="bg-white shadow-sm p-3 mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">Instagram Verifications</h4>
                        <small class="text-muted">Manage user Instagram follow verifications</small>
                    </div>
                </div>
            </div>
            
            <div class="p-3">
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-lg-2 col-md-4 mb-3">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <i class="fas fa-clock fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['pending']; ?></h3>
                                <p class="mb-0">Pending</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-4 mb-3">
                        <div class="card stats-card success">
                            <div class="card-body text-center">
                                <i class="fas fa-check-circle fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['verified']; ?></h3>
                                <p class="mb-0">Verified</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-4 mb-3">
                        <div class="card stats-card warning">
                            <div class="card-body text-center">
                                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['partial']; ?></h3>
                                <p class="mb-0">Partial</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-4 mb-3">
                        <div class="card stats-card danger">
                            <div class="card-body text-center">
                                <i class="fas fa-ban fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['suspended']; ?></h3>
                                <p class="mb-0">Suspended</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-4 mb-3">
                        <div class="card stats-card" style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);">
                            <div class="card-body text-center text-white">
                                <i class="fas fa-hourglass-end fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['overdue']; ?></h3>
                                <p class="mb-0">Overdue (2+ days)</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-4 mb-3">
                        <div class="card stats-card info">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo count($verifications); ?></h3>
                                <p class="mb-0">Total</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card border-warning">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="fw-bold text-warning mb-1">
                                            <i class="fas fa-exclamation-triangle me-2"></i>Auto-Suspension
                                        </h6>
                                        <p class="text-muted mb-0">
                                            Suspend users who haven't followed required accounts after 2 days
                                        </p>
                                    </div>
                                    <form method="POST" class="d-inline">
                                        <button type="submit" name="auto_suspend_overdue"
                                                class="btn btn-warning"
                                                onclick="return confirm('This will suspend all users who submitted verification requests more than 3 days ago but haven\'t followed the accounts. Continue?')">
                                            <i class="fas fa-ban me-2"></i>Auto-Suspend Overdue (<?php echo $stats['overdue']; ?>)
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Verifications Table -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fab fa-instagram me-2"></i>Verification Requests
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($verifications)): ?>
                            <div class="text-center py-5">
                                <i class="fab fa-instagram fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No verification requests</h5>
                                <p class="text-muted">Users will appear here when they submit Instagram verification requests.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>User</th>
                                            <th>Type</th>
                                            <th>Instagram</th>
                                            <th>Follow Status</th>
                                            <th>Status</th>
                                            <th>Days</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($verifications as $verification): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($verification['user_name']); ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($verification['user_email']); ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo $verification['user_type'] === 'user' ? 'primary' : 
                                                        ($verification['user_type'] === 'influencer' ? 'warning' : 'info'); 
                                                ?>">
                                                    <?php echo ucfirst($verification['user_type']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <a href="https://instagram.com/<?php echo htmlspecialchars($verification['instagram_username']); ?>"
                                                   target="_blank" class="text-decoration-none">
                                                    <i class="fab fa-instagram me-1"></i>@<?php echo htmlspecialchars($verification['instagram_username']); ?>
                                                </a>
                                            </td>
                                            <td>
                                                <?php
                                                $verified_accounts = json_decode($verification['accounts_verified'], true) ?: [];
                                                $required_accounts = $verification_system->getRequiredAccounts();
                                                ?>
                                                <div class="d-flex flex-column gap-1">
                                                    <?php foreach ($required_accounts as $account): ?>
                                                        <div class="d-flex align-items-center">
                                                            <?php if (in_array($account, $verified_accounts)): ?>
                                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                                <span class="text-success small">@<?php echo $account; ?></span>
                                                            <?php else: ?>
                                                                <i class="fas fa-times-circle text-danger me-2"></i>
                                                                <span class="text-danger small">@<?php echo $account; ?></span>
                                                            <?php endif; ?>
                                                        </div>
                                                    <?php endforeach; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php
                                                    echo $verification['verified'] ? 'success' :
                                                        ($verification['verification_status'] === 'pending' ? 'warning' :
                                                        ($verification['verification_status'] === 'suspended' ? 'danger' : 'secondary'));
                                                ?>">
                                                    <?php echo $verification['verified'] ? 'Verified' : ucfirst($verification['verification_status']); ?>
                                                </span>
                                                <?php if ($verification['verification_status'] === 'suspended'): ?>
                                                    <br><small class="text-muted">
                                                        <?php echo htmlspecialchars($verification['suspension_reason']); ?>
                                                    </small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="fw-bold <?php echo $verification['days_since_submission'] >= 2 ? 'text-danger' : 'text-muted'; ?>">
                                                    <?php echo $verification['days_since_submission']; ?>
                                                </span>
                                                <br>
                                                <small class="text-muted">
                                                    <?php echo date('M j', strtotime($verification['submitted_at'])); ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <?php if ($verification['verification_status'] === 'suspended'): ?>
                                                        <button class="btn btn-sm btn-success"
                                                                data-bs-toggle="modal"
                                                                data-bs-target="#unsuspendModal<?php echo $verification['id']; ?>">
                                                            <i class="fas fa-undo me-1"></i>Unsuspend
                                                        </button>
                                                    <?php elseif ($verification['verified']): ?>
                                                        <span class="badge bg-success">
                                                            <i class="fas fa-check-circle me-1"></i>Verified
                                                        </span>
                                                    <?php else: ?>
                                                        <button class="btn btn-sm btn-primary"
                                                                data-bs-toggle="modal"
                                                                data-bs-target="#verifyModal<?php echo $verification['id']; ?>">
                                                            <i class="fas fa-check me-1"></i>Verify
                                                        </button>

                                                        <?php if ($verification['days_since_submission'] >= 1): ?>
                                                            <button class="btn btn-sm btn-warning"
                                                                    data-bs-toggle="modal"
                                                                    data-bs-target="#suspendModal<?php echo $verification['id']; ?>">
                                                                <i class="fas fa-ban me-1"></i>Suspend
                                                            </button>
                                                        <?php endif; ?>
                                                    <?php endif; ?>
                                                </div>

                                                <div class="mt-1">
                                                    <a href="https://instagram.com/<?php echo htmlspecialchars($verification['instagram_username']); ?>/followers/"
                                                       target="_blank"
                                                       class="btn btn-sm btn-outline-info">
                                                        <i class="fas fa-external-link-alt me-1"></i>Check Follows
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Verification Modals -->
<?php foreach ($verifications as $verification): ?>
<div class="modal fade" id="verifyModal<?php echo $verification['id']; ?>" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Verify Instagram Follows</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p><strong>User:</strong> <?php echo htmlspecialchars($verification['user_name']); ?></p>
                            <p><strong>Type:</strong> <?php echo ucfirst($verification['user_type']); ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Instagram:</strong>
                                <a href="https://instagram.com/<?php echo htmlspecialchars($verification['instagram_username']); ?>" target="_blank">
                                    @<?php echo htmlspecialchars($verification['instagram_username']); ?>
                                </a>
                            </p>
                            <p><strong>Submitted:</strong> <?php echo date('M j, Y g:i A', strtotime($verification['submitted_at'])); ?></p>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Verification Instructions:</strong>
                        <ol class="mb-0 mt-2">
                            <li>Click the Instagram link above to visit their profile</li>
                            <li>Check if they follow both required accounts</li>
                            <li>Mark the accounts they actually follow below</li>
                            <li>Add verification notes if needed</li>
                        </ol>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">Follow Verification Status:</label>
                        <?php
                        $required_accounts = $verification_system->getRequiredAccounts();
                        $verified_accounts = json_decode($verification['accounts_verified'], true) ?: [];
                        ?>

                        <div class="row">
                            <?php foreach ($required_accounts as $account): ?>
                            <div class="col-md-6 mb-2">
                                <div class="card border">
                                    <div class="card-body p-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox"
                                                   name="verified_accounts[]"
                                                   value="<?php echo $account; ?>"
                                                   id="account_<?php echo $verification['id']; ?>_<?php echo $account; ?>"
                                                   <?php echo in_array($account, $verified_accounts) ? 'checked' : ''; ?>>
                                            <label class="form-check-label fw-bold" for="account_<?php echo $verification['id']; ?>_<?php echo $account; ?>">
                                                @<?php echo $account; ?>
                                            </label>
                                        </div>
                                        <div class="mt-2">
                                            <a href="https://instagram.com/<?php echo $account; ?>/followers/"
                                               target="_blank"
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-external-link-alt me-1"></i>Check Followers
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="follow_proof_<?php echo $verification['id']; ?>" class="form-label">Proof/Screenshot URL (Optional):</label>
                        <input type="url" class="form-control"
                               id="follow_proof_<?php echo $verification['id']; ?>"
                               name="follow_proof"
                               placeholder="https://example.com/screenshot.png">
                        <small class="text-muted">Upload screenshot to image hosting service and paste URL here</small>
                    </div>

                    <div class="mb-3">
                        <label for="notes_<?php echo $verification['id']; ?>" class="form-label">Verification Notes:</label>
                        <textarea class="form-control"
                                  id="notes_<?php echo $verification['id']; ?>"
                                  name="notes"
                                  rows="3"
                                  placeholder="Add any notes about this verification..."><?php echo htmlspecialchars($verification['notes'] ?? ''); ?></textarea>
                    </div>

                    <input type="hidden" name="verification_id" value="<?php echo $verification['id']; ?>">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="verify_user" class="btn btn-primary">Update Verification</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Suspension Modal -->
<div class="modal fade" id="suspendModal<?php echo $verification['id']; ?>" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning">
                <h5 class="modal-title">
                    <i class="fas fa-ban me-2"></i>Suspend User Account
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning!</strong> This will suspend the user's account and prevent access to all panels.
                    </div>

                    <p><strong>User:</strong> <?php echo htmlspecialchars($verification['user_name']); ?></p>
                    <p><strong>Instagram:</strong> @<?php echo htmlspecialchars($verification['instagram_username']); ?></p>
                    <p><strong>Days since submission:</strong> <?php echo $verification['days_since_submission']; ?> days</p>

                    <div class="mb-3">
                        <label for="suspension_reason_<?php echo $verification['id']; ?>" class="form-label">Suspension Reason:</label>
                        <select class="form-select"
                                id="suspension_reason_<?php echo $verification['id']; ?>"
                                name="suspension_reason"
                                required>
                            <option value="">Select reason...</option>
                            <option value="Failed to follow required Instagram accounts within 2 days">Failed to follow required accounts (2+ days)</option>
                            <option value="Provided fake or invalid Instagram username">Invalid Instagram username</option>
                            <option value="Violated platform terms and conditions">Terms violation</option>
                            <option value="Suspicious or fraudulent activity">Suspicious activity</option>
                            <option value="Other">Other (specify in notes)</option>
                        </select>
                    </div>

                    <input type="hidden" name="verification_id" value="<?php echo $verification['id']; ?>">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="suspend_user" class="btn btn-warning">
                        <i class="fas fa-ban me-2"></i>Suspend Account
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Unsuspend Modal -->
<div class="modal fade" id="unsuspendModal<?php echo $verification['id']; ?>" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success">
                <h5 class="modal-title text-white">
                    <i class="fas fa-undo me-2"></i>Unsuspend User Account
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <div class="alert alert-success">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Unsuspend Account:</strong> This will restore the user's access to all panels.
                    </div>

                    <p><strong>User:</strong> <?php echo htmlspecialchars($verification['user_name']); ?></p>
                    <p><strong>Instagram:</strong> @<?php echo htmlspecialchars($verification['instagram_username']); ?></p>

                    <?php if ($verification['suspension_reason']): ?>
                        <p><strong>Suspension Reason:</strong> <?php echo htmlspecialchars($verification['suspension_reason']); ?></p>
                    <?php endif; ?>

                    <div class="alert alert-info">
                        <i class="fas fa-lightbulb me-2"></i>
                        <strong>Before unsuspending:</strong>
                        <ul class="mb-0 mt-2">
                            <li>Verify that the user has now followed both required Instagram accounts</li>
                            <li>Check their Instagram profile to confirm follows</li>
                            <li>Add notes explaining why you're unsuspending them</li>
                        </ul>
                    </div>

                    <div class="mb-3">
                        <label for="unsuspend_notes_<?php echo $verification['id']; ?>" class="form-label">Unsuspension Notes:</label>
                        <textarea class="form-control"
                                  id="unsuspend_notes_<?php echo $verification['id']; ?>"
                                  name="unsuspend_notes"
                                  rows="3"
                                  placeholder="Explain why you're unsuspending this user..."
                                  required></textarea>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox"
                                   id="confirm_follows_<?php echo $verification['id']; ?>"
                                   required>
                            <label class="form-check-label" for="confirm_follows_<?php echo $verification['id']; ?>">
                                I have verified that this user now follows both required Instagram accounts
                            </label>
                        </div>
                    </div>

                    <input type="hidden" name="verification_id" value="<?php echo $verification['id']; ?>">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="unsuspend_user" class="btn btn-success">
                        <i class="fas fa-undo me-2"></i>Unsuspend Account
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endforeach; ?>

<?php include '../includes/footer.php'; ?>
