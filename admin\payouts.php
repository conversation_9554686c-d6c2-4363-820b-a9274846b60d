<?php
require_once '../config.php';
require_login(['admin']);

$db = Database::getInstance();

// Handle payout actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['process_payout'])) {
        $payout_id = intval($_POST['payout_id']);
        $action = sanitize_input($_POST['action']);
        $transaction_id = sanitize_input($_POST['transaction_id']);
        $notes = sanitize_input($_POST['notes']);
        
        try {
            if ($action === 'complete') {
                $db->update('payouts', [
                    'status' => 'completed',
                    'transaction_id' => $transaction_id,
                    'processed_by' => get_user_id(),
                    'processed_at' => date('Y-m-d H:i:s'),
                    'notes' => $notes
                ], 'id = ?', [$payout_id]);
                
                $_SESSION['success'] = 'Payout marked as completed!';
            } elseif ($action === 'fail') {
                // Get payout details to refund user
                $payout = $db->fetch("SELECT * FROM payouts WHERE id = ?", [$payout_id]);
                
                if ($payout) {
                    // Refund amount to user
                    if ($payout['user_type'] === 'user') {
                        $user = $db->fetch("SELECT pending_payout FROM users WHERE id = ?", [$payout['user_id']]);
                        $new_pending = $user['pending_payout'] + $payout['amount'];
                        $db->update('users', ['pending_payout' => $new_pending], 'id = ?', [$payout['user_id']]);
                    } elseif ($payout['user_type'] === 'influencer') {
                        $influencer = $db->fetch("SELECT pending_payout FROM influencers WHERE id = ?", [$payout['user_id']]);
                        $new_pending = $influencer['pending_payout'] + $payout['amount'];
                        $db->update('influencers', ['pending_payout' => $new_pending], 'id = ?', [$payout['user_id']]);
                    }
                    
                    // Update payout status
                    $db->update('payouts', [
                        'status' => 'failed',
                        'processed_by' => get_user_id(),
                        'processed_at' => date('Y-m-d H:i:s'),
                        'notes' => $notes
                    ], 'id = ?', [$payout_id]);
                    
                    $_SESSION['success'] = 'Payout marked as failed and amount refunded to user!';
                }
            }
        } catch (Exception $e) {
            $_SESSION['error'] = 'Failed to process payout.';
        }
    }
    
    if (isset($_POST['bulk_process'])) {
        $payout_ids = $_POST['payout_ids'] ?? [];
        $bulk_action = sanitize_input($_POST['bulk_action']);
        
        if (!empty($payout_ids) && $bulk_action) {
            try {
                foreach ($payout_ids as $payout_id) {
                    $payout_id = intval($payout_id);
                    
                    if ($bulk_action === 'mark_processing') {
                        $db->update('payouts', [
                            'status' => 'processing',
                            'processed_by' => get_user_id(),
                            'processed_at' => date('Y-m-d H:i:s')
                        ], 'id = ?', [$payout_id]);
                    }
                }
                
                $_SESSION['success'] = 'Bulk action completed successfully!';
            } catch (Exception $e) {
                $_SESSION['error'] = 'Failed to process bulk action.';
            }
        }
    }
}

// Get filter parameters
$status = $_GET['status'] ?? 'pending';
$user_type = $_GET['user_type'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';

// Build query
$conditions = ['1=1'];
$params = [];

if ($status) {
    $conditions[] = "p.status = ?";
    $params[] = $status;
}

if ($user_type) {
    $conditions[] = "p.user_type = ?";
    $params[] = $user_type;
}

if ($date_from) {
    $conditions[] = "DATE(p.created_at) >= ?";
    $params[] = $date_from;
}

if ($date_to) {
    $conditions[] = "DATE(p.created_at) <= ?";
    $params[] = $date_to;
}

$where_clause = implode(' AND ', $conditions);

// Get payouts
$payouts = $db->fetchAll("
    SELECT p.*, 
           CASE 
               WHEN p.user_type = 'user' THEN u.full_name
               WHEN p.user_type = 'influencer' THEN i.full_name
           END as user_name,
           CASE 
               WHEN p.user_type = 'user' THEN u.username
               WHEN p.user_type = 'influencer' THEN i.username
           END as username,
           CASE 
               WHEN p.user_type = 'user' THEN u.email
               WHEN p.user_type = 'influencer' THEN i.email
           END as email,
           a.full_name as processed_by_name
    FROM payouts p
    LEFT JOIN users u ON p.user_type = 'user' AND p.user_id = u.id
    LEFT JOIN influencers i ON p.user_type = 'influencer' AND p.user_id = i.id
    LEFT JOIN admin a ON p.processed_by = a.id
    WHERE {$where_clause}
    ORDER BY p.created_at DESC
", $params);

// Get statistics
$stats = $db->fetch("
    SELECT 
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
        COUNT(CASE WHEN status = 'processing' THEN 1 END) as processing_count,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count,
        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count,
        SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END) as pending_amount,
        SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as completed_amount,
        SUM(amount) as total_amount
    FROM payouts
");

$page_title = 'Payout Management';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-xl-2 sidebar p-0">
            <div class="p-3">
                <h5 class="text-white mb-4">
                    <i class="fas fa-crown me-2"></i>Admin Panel
                </h5>
                
                                                <nav class="nav flex-column">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a class="nav-link" href="users.php">
                        <i class="fas fa-users"></i>Users
                    </a>
                    <a class="nav-link" href="influencers.php">
                        <i class="fas fa-star"></i>Influencers
                    </a>
                    <a class="nav-link" href="brands.php">
                        <i class="fas fa-building"></i>Brands
                    </a>
                    <a class="nav-link" href="campaigns.php">
                        <i class="fas fa-bullhorn"></i>Campaigns
                    </a>
                    <a class="nav-link" href="submissions.php">
                        <i class="fas fa-clipboard-check"></i>Submissions
                    </a>
                    <a class="nav-link" href="assignments.php">
                        <i class="fas fa-user-check"></i>Assignments
                    </a>
                    <a class="nav-link active" href="payouts.php">
                        <i class="fas fa-money-bill-wave"></i>Payouts
                    </a>
                    
                    <!-- Instagram & Verification Section -->
                    <hr class="my-2 text-light">
                    <small class="text-light px-3 mb-2">Instagram & Verification</small>
                    
                    <a class="nav-link" href="instagram_verifications.php">
                        <i class="fab fa-instagram"></i>Instagram Verifications
                    </a>
                    <a class="nav-link" href="influencer_follow_check.php">
                        <i class="fas fa-user-check"></i>Follow Status Check
                    </a>
                    <a class="nav-link" href="instagram_blue_check.php">
                        <i class="fas fa-check-circle"></i>Blue Check Management
                    </a>
                    <a class="nav-link" href="user_verification_status.php">
                        <i class="fas fa-clipboard-check"></i>User Follow Status
                    </a>
                    <a class="nav-link" href="suspended_users.php">
                        <i class="fas fa-ban"></i>Suspended Users
                    </a>
                    
                    <!-- Badge Management Section -->
                    <hr class="my-2 text-light">
                    <small class="text-light px-3 mb-2">Badge Management</small>
                    
                    <a class="nav-link" href="badge_management.php">
                        <i class="fas fa-award"></i>Badge Management
                    </a>
                    <a class="nav-link" href="user_badge_assignment.php">
                        <i class="fas fa-user-tag"></i>Assign Badges
                    </a>
                    
                    <!-- System Section -->
                    <hr class="my-2 text-light">
                    <small class="text-light px-3 mb-2">System</small>
                    
                    <a class="nav-link" href="settings.php">
                        <i class="fas fa-cog"></i>Settings
                    </a>
                    <a class="nav-link" href="instagram_settings.php">
                        <i class="fab fa-instagram"></i>Instagram Settings
                    </a>
                    <a class="nav-link" href="export.php">
                        <i class="fas fa-download"></i>Export Data
                    </a>
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10 main-content" style="margin-left: auto;">
            <!-- Top Bar -->
            <div class="bg-white shadow-sm p-3 mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">Payout Management</h4>
                        <small class="text-muted">Process user and influencer payouts</small>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-outline-primary d-lg-none" onclick="toggleSidebar()">
                            <i class="fas fa-bars"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="p-3">
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card warning">
                            <div class="card-body text-center">
                                <i class="fas fa-clock fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['pending_count']; ?></h3>
                                <p class="mb-0">Pending</p>
                                <small class="text-light"><?php echo format_currency($stats['pending_amount'] ?? 0); ?></small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card info">
                            <div class="card-body text-center">
                                <i class="fas fa-spinner fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['processing_count']; ?></h3>
                                <p class="mb-0">Processing</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card success">
                            <div class="card-body text-center">
                                <i class="fas fa-check-circle fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['completed_count']; ?></h3>
                                <p class="mb-0">Completed</p>
                                <small class="text-light"><?php echo format_currency($stats['completed_amount'] ?? 0); ?></small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <i class="fas fa-times-circle fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['failed_count']; ?></h3>
                                <p class="mb-0">Failed</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-filter me-2"></i>Filter Payouts
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-2">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="pending" <?php echo $status === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                    <option value="processing" <?php echo $status === 'processing' ? 'selected' : ''; ?>>Processing</option>
                                    <option value="completed" <?php echo $status === 'completed' ? 'selected' : ''; ?>>Completed</option>
                                    <option value="failed" <?php echo $status === 'failed' ? 'selected' : ''; ?>>Failed</option>
                                    <option value="" <?php echo $status === '' ? 'selected' : ''; ?>>All Status</option>
                                </select>
                            </div>
                            
                            <div class="col-md-2">
                                <label for="user_type" class="form-label">User Type</label>
                                <select class="form-select" id="user_type" name="user_type">
                                    <option value="">All Types</option>
                                    <option value="user" <?php echo $user_type === 'user' ? 'selected' : ''; ?>>Users</option>
                                    <option value="influencer" <?php echo $user_type === 'influencer' ? 'selected' : ''; ?>>Influencers</option>
                                </select>
                            </div>
                            
                            <div class="col-md-2">
                                <label for="date_from" class="form-label">From Date</label>
                                <input type="date" class="form-control" id="date_from" name="date_from" 
                                       value="<?php echo htmlspecialchars($date_from); ?>">
                            </div>
                            
                            <div class="col-md-2">
                                <label for="date_to" class="form-label">To Date</label>
                                <input type="date" class="form-control" id="date_to" name="date_to" 
                                       value="<?php echo htmlspecialchars($date_to); ?>">
                            </div>
                            
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                <a class="nav-link active" href="payouts.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                            
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="button" class="btn btn-success w-100" onclick="exportPayouts()">
                                    <i class="fas fa-download me-1"></i>Export
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Bulk Actions -->
                <?php if ($status === 'pending' && !empty($payouts)): ?>
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-tasks me-2"></i>Bulk Actions
                            </h6>
                        </div>
                        <div class="card-body">
                            <form method="POST" id="bulkForm">
                                <div class="row align-items-end">
                                    <div class="col-md-3">
                                        <label class="form-label">Select Action</label>
                                        <select class="form-select" name="bulk_action" required>
                                            <option value="">Choose Action</option>
                                            <option value="mark_processing">Mark as Processing</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <button type="submit" name="bulk_process" class="btn btn-warning">
                                            <i class="fas fa-cogs me-1"></i>Apply to Selected
                                        </button>
                                    </div>
                                    <div class="col-md-6 text-end">
                                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="selectAll()">Select All</button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="selectNone()">Select None</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                <?php endif; ?>
                
                <!-- Payouts Table -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>Payouts (<?php echo count($payouts); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($payouts)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No payouts found</h5>
                                <p class="text-muted">No payouts match your current filters.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <?php if ($status === 'pending'): ?>
                                                <th width="50">
                                                    <input type="checkbox" class="form-check-input" id="selectAllCheckbox">
                                                </th>
                                            <?php endif; ?>
                                            <th>User</th>
                                            <th>Type</th>
                                            <th>Amount</th>
                                            <th>UPI ID</th>
                                            <th>Status</th>
                                            <th>Requested</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($payouts as $payout): ?>
                                            <tr>
                                                <?php if ($status === 'pending'): ?>
                                                    <td>
                                                        <input type="checkbox" class="form-check-input payout-checkbox" 
                                                               name="payout_ids[]" value="<?php echo $payout['id']; ?>" 
                                                               form="bulkForm">
                                                    </td>
                                                <?php endif; ?>
                                                <td>
                                                    <div>
                                                        <strong><?php echo htmlspecialchars($payout['user_name']); ?></strong>
                                                        <br><small class="text-muted">@<?php echo htmlspecialchars($payout['username']); ?></small>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($payout['email']); ?></small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge bg-<?php echo $payout['user_type'] === 'user' ? 'primary' : 'warning'; ?>">
                                                        <?php echo ucfirst($payout['user_type']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <strong class="text-success"><?php echo format_currency($payout['amount']); ?></strong>
                                                </td>
                                                <td>
                                                    <code><?php echo htmlspecialchars($payout['upi_id']); ?></code>
                                                </td>
                                                <td>
                                                    <?php
                                                    $status_class = '';
                                                    switch ($payout['status']) {
                                                        case 'pending':
                                                            $status_class = 'bg-warning';
                                                            break;
                                                        case 'processing':
                                                            $status_class = 'bg-info';
                                                            break;
                                                        case 'completed':
                                                            $status_class = 'bg-success';
                                                            break;
                                                        case 'failed':
                                                            $status_class = 'bg-danger';
                                                            break;
                                                    }
                                                    ?>
                                                    <span class="badge <?php echo $status_class; ?>">
                                                        <?php echo ucfirst($payout['status']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <small><?php echo date('M j, Y', strtotime($payout['created_at'])); ?></small>
                                                    <br><small class="text-muted"><?php echo time_ago($payout['created_at']); ?></small>
                                                </td>
                                                <td>
                                                    <?php if ($payout['status'] === 'pending' || $payout['status'] === 'processing'): ?>
                                                        <button class="btn btn-sm btn-outline-primary" 
                                                                data-bs-toggle="modal" 
                                                                data-bs-target="#processModal<?php echo $payout['id']; ?>">
                                                            <i class="fas fa-cog"></i> Process
                                                        </button>
                                                    <?php else: ?>
                                                        <button class="btn btn-sm btn-outline-info" 
                                                                data-bs-toggle="modal" 
                                                                data-bs-target="#viewModal<?php echo $payout['id']; ?>">
                                                            <i class="fas fa-eye"></i> View
                                                        </button>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function selectAll() {
    document.querySelectorAll('.payout-checkbox').forEach(cb => cb.checked = true);
    document.getElementById('selectAllCheckbox').checked = true;
}

function selectNone() {
    document.querySelectorAll('.payout-checkbox').forEach(cb => cb.checked = false);
    document.getElementById('selectAllCheckbox').checked = false;
}

document.getElementById('selectAllCheckbox')?.addEventListener('change', function() {
    document.querySelectorAll('.payout-checkbox').forEach(cb => cb.checked = this.checked);
});

function exportPayouts() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', '1');
    window.location.href = 'export.php?type=payouts&' + params.toString();
}
</script>

<?php include '../includes/footer.php'; ?>
