<?php
require_once '../config.php';
require_login(['admin']);

$db = Database::getInstance();

// Handle settings update
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['update_settings'])) {
        $settings = $_POST['settings'] ?? [];
        
        try {
            foreach ($settings as $key => $value) {
                $db->update('settings', 
                    ['setting_value' => $value, 'updated_by' => get_user_id()], 
                    'setting_key = ?', [$key]
                );
            }
            $_SESSION['success'] = 'Settings updated successfully!';
            redirect('settings.php');
        } catch (Exception $e) {
            $_SESSION['error'] = 'Failed to update settings.';
        }
    }
    
    if (isset($_POST['add_setting'])) {
        $key = sanitize_input($_POST['setting_key']);
        $value = sanitize_input($_POST['setting_value']);
        $description = sanitize_input($_POST['description']);
        
        if (!empty($key)) {
            try {
                $db->insert('settings', [
                    'setting_key' => $key,
                    'setting_value' => $value,
                    'description' => $description,
                    'updated_by' => get_user_id()
                ]);
                $_SESSION['success'] = 'Setting added successfully!';
            } catch (Exception $e) {
                $_SESSION['error'] = 'Failed to add setting. Key might already exist.';
            }
        }
    }
}

// Get all settings
$settings = $db->fetchAll("
    SELECT s.*, a.full_name as updated_by_name 
    FROM settings s 
    LEFT JOIN admin a ON s.updated_by = a.id 
    ORDER BY s.setting_key
");

// Group settings by category
$grouped_settings = [
    'site' => [],
    'payment' => [],
    'email' => [],
    'notification' => [],
    'other' => []
];

foreach ($settings as $setting) {
    $key = $setting['setting_key'];
    if (strpos($key, 'site_') === 0) {
        $grouped_settings['site'][] = $setting;
    } elseif (strpos($key, 'razorpay_') === 0 || strpos($key, 'min_payout') === 0 || strpos($key, 'platform_commission') === 0) {
        $grouped_settings['payment'][] = $setting;
    } elseif (strpos($key, 'email_') === 0) {
        $grouped_settings['email'][] = $setting;
    } elseif (strpos($key, 'whatsapp_') === 0) {
        $grouped_settings['notification'][] = $setting;
    } else {
        $grouped_settings['other'][] = $setting;
    }
}

$page_title = 'Platform Settings';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-xl-2 sidebar p-0">
            <div class="p-3">
                <h5 class="text-white mb-4">
                    <i class="fas fa-crown me-2"></i>Admin Panel
                </h5>
                
                                                <nav class="nav flex-column">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a class="nav-link" href="users.php">
                        <i class="fas fa-users"></i>Users
                    </a>
                    <a class="nav-link" href="influencers.php">
                        <i class="fas fa-star"></i>Influencers
                    </a>
                    <a class="nav-link" href="brands.php">
                        <i class="fas fa-building"></i>Brands
                    </a>
                    <a class="nav-link" href="campaigns.php">
                        <i class="fas fa-bullhorn"></i>Campaigns
                    </a>
                    <a class="nav-link" href="submissions.php">
                        <i class="fas fa-clipboard-check"></i>Submissions
                    </a>
                    <a class="nav-link" href="assignments.php">
                        <i class="fas fa-user-check"></i>Assignments
                    </a>
                    <a class="nav-link" href="payouts.php">
                        <i class="fas fa-money-bill-wave"></i>Payouts
                    </a>
                    
                    <!-- Instagram & Verification Section -->
                    <hr class="my-2 text-light">
                    <small class="text-light px-3 mb-2">Instagram & Verification</small>
                    
                    <a class="nav-link" href="instagram_verifications.php">
                        <i class="fab fa-instagram"></i>Instagram Verifications
                    </a>
                    <a class="nav-link" href="influencer_follow_check.php">
                        <i class="fas fa-user-check"></i>Follow Status Check
                    </a>
                    <a class="nav-link" href="instagram_blue_check.php">
                        <i class="fas fa-check-circle"></i>Blue Check Management
                    </a>
                    <a class="nav-link" href="user_verification_status.php">
                        <i class="fas fa-clipboard-check"></i>User Follow Status
                    </a>
                    <a class="nav-link" href="suspended_users.php">
                        <i class="fas fa-ban"></i>Suspended Users
                    </a>
                    
                    <!-- Badge Management Section -->
                    <hr class="my-2 text-light">
                    <small class="text-light px-3 mb-2">Badge Management</small>
                    
                    <a class="nav-link" href="badge_management.php">
                        <i class="fas fa-award"></i>Badge Management
                    </a>
                    <a class="nav-link" href="user_badge_assignment.php">
                        <i class="fas fa-user-tag"></i>Assign Badges
                    </a>
                    
                    <!-- System Section -->
                    <hr class="my-2 text-light">
                    <small class="text-light px-3 mb-2">System</small>
                    
                    <a class="nav-link active" href="settings.php">
                        <i class="fas fa-cog"></i>Settings
                    </a>
                    <a class="nav-link" href="instagram_settings.php">
                        <i class="fab fa-instagram"></i>Instagram Settings
                    </a>
                    <a class="nav-link" href="export.php">
                        <i class="fas fa-download"></i>Export Data
                    </a>
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10 main-content" style="margin-left: auto;">
            <!-- Top Bar -->
            <div class="bg-white shadow-sm p-3 mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">Platform Settings</h4>
                        <small class="text-muted">Configure platform settings and integrations</small>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSettingModal">
                            <i class="fas fa-plus me-2"></i>Add Setting
                        </button>
                        <button class="btn btn-outline-primary d-lg-none ms-2" onclick="toggleSidebar()">
                            <i class="fas fa-bars"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="p-3">
                <form method="POST">
                    <!-- Site Settings -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-globe me-2"></i>Site Settings
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <?php foreach ($grouped_settings['site'] as $setting): ?>
                                    <div class="col-md-6 mb-3">
                                        <label for="<?php echo $setting['setting_key']; ?>" class="form-label">
                                            <?php echo ucwords(str_replace('_', ' ', str_replace('site_', '', $setting['setting_key']))); ?>
                                        </label>
                                        <input type="text" class="form-control" 
                                               id="<?php echo $setting['setting_key']; ?>" 
                                               name="settings[<?php echo $setting['setting_key']; ?>]" 
                                               value="<?php echo htmlspecialchars($setting['setting_value']); ?>">
                                        <?php if ($setting['description']): ?>
                                            <div class="form-text"><?php echo htmlspecialchars($setting['description']); ?></div>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Payment Settings -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-credit-card me-2"></i>Payment Settings
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <?php foreach ($grouped_settings['payment'] as $setting): ?>
                                    <div class="col-md-6 mb-3">
                                        <label for="<?php echo $setting['setting_key']; ?>" class="form-label">
                                            <?php echo ucwords(str_replace('_', ' ', $setting['setting_key'])); ?>
                                        </label>
                                        <?php if (strpos($setting['setting_key'], 'secret') !== false): ?>
                                            <input type="password" class="form-control" 
                                                   id="<?php echo $setting['setting_key']; ?>" 
                                                   name="settings[<?php echo $setting['setting_key']; ?>]" 
                                                   value="<?php echo htmlspecialchars($setting['setting_value']); ?>">
                                        <?php else: ?>
                                            <input type="text" class="form-control" 
                                                   id="<?php echo $setting['setting_key']; ?>" 
                                                   name="settings[<?php echo $setting['setting_key']; ?>]" 
                                                   value="<?php echo htmlspecialchars($setting['setting_value']); ?>">
                                        <?php endif; ?>
                                        <?php if ($setting['description']): ?>
                                            <div class="form-text"><?php echo htmlspecialchars($setting['description']); ?></div>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Razorpay Setup:</strong> 
                                <ol class="mb-0 mt-2">
                                    <li>Sign up at <a href="https://razorpay.com" target="_blank">razorpay.com</a></li>
                                    <li>Get your API keys from the dashboard</li>
                                    <li>Enter the keys above to enable automated payments</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Email Settings -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-envelope me-2"></i>Email Settings
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <?php foreach ($grouped_settings['email'] as $setting): ?>
                                    <div class="col-md-6 mb-3">
                                        <label for="<?php echo $setting['setting_key']; ?>" class="form-label">
                                            <?php echo ucwords(str_replace('_', ' ', str_replace('email_', '', $setting['setting_key']))); ?>
                                        </label>
                                        <?php if (strpos($setting['setting_key'], 'password') !== false): ?>
                                            <input type="password" class="form-control" 
                                                   id="<?php echo $setting['setting_key']; ?>" 
                                                   name="settings[<?php echo $setting['setting_key']; ?>]" 
                                                   value="<?php echo htmlspecialchars($setting['setting_value']); ?>">
                                        <?php else: ?>
                                            <input type="text" class="form-control" 
                                                   id="<?php echo $setting['setting_key']; ?>" 
                                                   name="settings[<?php echo $setting['setting_key']; ?>]" 
                                                   value="<?php echo htmlspecialchars($setting['setting_value']); ?>">
                                        <?php endif; ?>
                                        <?php if ($setting['description']): ?>
                                            <div class="form-text"><?php echo htmlspecialchars($setting['description']); ?></div>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Notification Settings -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-bell me-2"></i>Notification Settings
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <?php foreach ($grouped_settings['notification'] as $setting): ?>
                                    <div class="col-md-6 mb-3">
                                        <label for="<?php echo $setting['setting_key']; ?>" class="form-label">
                                            <?php echo ucwords(str_replace('_', ' ', $setting['setting_key'])); ?>
                                        </label>
                                        <input type="text" class="form-control" 
                                               id="<?php echo $setting['setting_key']; ?>" 
                                               name="settings[<?php echo $setting['setting_key']; ?>]" 
                                               value="<?php echo htmlspecialchars($setting['setting_value']); ?>">
                                        <?php if ($setting['description']): ?>
                                            <div class="form-text"><?php echo htmlspecialchars($setting['description']); ?></div>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Other Settings -->
                    <?php if (!empty($grouped_settings['other'])): ?>
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-cogs me-2"></i>Other Settings
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <?php foreach ($grouped_settings['other'] as $setting): ?>
                                        <div class="col-md-6 mb-3">
                                            <label for="<?php echo $setting['setting_key']; ?>" class="form-label">
                                                <?php echo ucwords(str_replace('_', ' ', $setting['setting_key'])); ?>
                                            </label>
                                            <input type="text" class="form-control" 
                                                   id="<?php echo $setting['setting_key']; ?>" 
                                                   name="settings[<?php echo $setting['setting_key']; ?>]" 
                                                   value="<?php echo htmlspecialchars($setting['setting_value']); ?>">
                                            <?php if ($setting['description']): ?>
                                                <div class="form-text"><?php echo htmlspecialchars($setting['description']); ?></div>
                                            <?php endif; ?>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Save Button -->
                    <div class="card">
                        <div class="card-body text-center">
                            <button type="submit" name="update_settings" class="btn btn-primary btn-lg">
                                <i class="fas fa-save me-2"></i>Save All Settings
                            </button>
                        </div>
                    </div>
                </form>
                
                <!-- System Information -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>System Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Server Information</h6>
                                <ul class="list-unstyled">
                                    <li><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></li>
                                    <li><strong>Server Software:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></li>
                                    <li><strong>Max Upload Size:</strong> <?php echo ini_get('upload_max_filesize'); ?></li>
                                    <li><strong>Max Post Size:</strong> <?php echo ini_get('post_max_size'); ?></li>
                                    <li><strong>Memory Limit:</strong> <?php echo ini_get('memory_limit'); ?></li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Platform Statistics</h6>
                                <?php
                                $platform_stats = $db->fetch("
                                    SELECT 
                                        (SELECT COUNT(*) FROM users) as total_users,
                                        (SELECT COUNT(*) FROM influencers) as total_influencers,
                                        (SELECT COUNT(*) FROM campaigns) as total_campaigns,
                                        (SELECT SUM(amount) FROM payouts WHERE status = 'completed') as total_payouts
                                ");
                                ?>
                                <ul class="list-unstyled">
                                    <li><strong>Total Users:</strong> <?php echo $platform_stats['total_users']; ?></li>
                                    <li><strong>Total Influencers:</strong> <?php echo $platform_stats['total_influencers']; ?></li>
                                    <li><strong>Total Campaigns:</strong> <?php echo $platform_stats['total_campaigns']; ?></li>
                                    <li><strong>Total Payouts:</strong> <?php echo format_currency($platform_stats['total_payouts'] ?? 0); ?></li>
                                    <li><strong>Platform Version:</strong> 1.0.0</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Setting Modal -->
<div class="modal fade" id="addSettingModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Setting</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" class="needs-validation" novalidate>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="setting_key" class="form-label">Setting Key</label>
                        <input type="text" class="form-control" id="setting_key" name="setting_key" required>
                        <div class="form-text">Use lowercase with underscores (e.g., custom_setting_name)</div>
                        <div class="invalid-feedback">Please provide a setting key.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="setting_value" class="form-label">Setting Value</label>
                        <input type="text" class="form-control" id="setting_value" name="setting_value">
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="2"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="add_setting" class="btn btn-primary">Add Setting</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
