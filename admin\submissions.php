<?php
require_once '../config.php';
require_login(['admin']);

$db = Database::getInstance();

// Handle submission review
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['review_submission'])) {
        $task_id = intval($_POST['task_id']);
        $action = sanitize_input($_POST['action']);
        $admin_notes = sanitize_input($_POST['admin_notes']);
        
        try {
            if ($action === 'approve') {
                // Get task details
                $task = $db->fetch("
                    SELECT ut.*, u.pending_payout, u.total_earned 
                    FROM user_tasks ut 
                    JOIN users u ON ut.user_id = u.id 
                    WHERE ut.id = ?
                ", [$task_id]);
                
                if ($task) {
                    // Update task status
                    $db->update('user_tasks', [
                        'status' => 'approved',
                        'approved_at' => date('Y-m-d H:i:s'),
                        'admin_notes' => $admin_notes
                    ], 'id = ?', [$task_id]);
                    
                    // Update user earnings
                    $new_pending = $task['pending_payout'] + $task['reward_amount'];
                    $new_total = $task['total_earned'] + $task['reward_amount'];
                    
                    $db->update('users', [
                        'pending_payout' => $new_pending,
                        'total_earned' => $new_total
                    ], 'id = ?', [$task['user_id']]);
                    
                    $_SESSION['success'] = 'Submission approved and payment added to user wallet!';
                }
            } elseif ($action === 'reject') {
                // Update task status
                $db->update('user_tasks', [
                    'status' => 'rejected',
                    'admin_notes' => $admin_notes
                ], 'id = ?', [$task_id]);
                
                $_SESSION['success'] = 'Submission rejected.';
            }
        } catch (Exception $e) {
            $_SESSION['error'] = 'Failed to process submission review.';
        }
    }
}

// Get filter parameters
$status = $_GET['status'] ?? 'submitted';
$campaign_id = $_GET['campaign_id'] ?? '';
$user_search = $_GET['user_search'] ?? '';

// Build query
$conditions = ['1=1'];
$params = [];

if ($status) {
    $conditions[] = "ut.status = ?";
    $params[] = $status;
}

if ($campaign_id) {
    $conditions[] = "ut.campaign_id = ?";
    $params[] = $campaign_id;
}

if ($user_search) {
    $conditions[] = "(u.username LIKE ? OR u.full_name LIKE ?)";
    $search_param = "%{$user_search}%";
    $params[] = $search_param;
    $params[] = $search_param;
}

$where_clause = implode(' AND ', $conditions);

// Get submissions
$submissions = $db->fetchAll("
    SELECT ut.*, u.username, u.full_name, u.email, u.instagram_handle,
           c.title as campaign_title, c.description as campaign_description,
           c.reward_amount, c.requirements, c.hashtags,
           b.company_name as brand_name,
           us.post_url, us.screenshot_path, us.caption, us.hashtags_used,
           us.submission_notes, us.submitted_at
    FROM user_tasks ut
    JOIN users u ON ut.user_id = u.id
    JOIN campaigns c ON ut.campaign_id = c.id
    LEFT JOIN brands b ON c.brand_id = b.id
    LEFT JOIN user_submissions us ON ut.id = us.task_id
    WHERE {$where_clause}
    ORDER BY ut.submitted_at DESC
", $params);

// Get campaigns for filter
$campaigns = $db->fetchAll("
    SELECT id, title FROM campaigns 
    WHERE status = 'active' 
    ORDER BY title
");

// Get statistics
$stats = $db->fetch("
    SELECT 
        COUNT(CASE WHEN status = 'submitted' THEN 1 END) as pending_review,
        COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved,
        COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected,
        SUM(CASE WHEN status = 'approved' THEN reward_amount ELSE 0 END) as total_approved_amount
    FROM user_tasks
");

$page_title = 'Submission Review';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-xl-2 sidebar p-0">
            <div class="p-3">
                <h5 class="text-white mb-4">
                    <i class="fas fa-crown me-2"></i>Admin Panel
                </h5>
                
                                <nav class="nav flex-column">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a class="nav-link" href="users.php">
                        <i class="fas fa-users"></i>Users
                    </a>
                    <a class="nav-link" href="influencers.php">
                        <i class="fas fa-star"></i>Influencers
                    </a>
                    <a class="nav-link" href="brands.php">
                        <i class="fas fa-building"></i>Brands
                    </a>
                    <a class="nav-link" href="campaigns.php">
                        <i class="fas fa-bullhorn"></i>Campaigns
                    </a>
                    <a class="nav-link active" href="submissions.php" class="nav-link active">
                        <i class="fas fa-clipboard-check"></i>Submissions
                    </a>
                    <a class="nav-link" href="assignments.php">
                        <i class="fas fa-user-check"></i>Assignments
                    </a>
                    <a class="nav-link" href="payouts.php">
                        <i class="fas fa-money-bill-wave"></i>Payouts
                    </a>
                    
                    <!-- Instagram & Verification Section -->
                    <hr class="my-2 text-light">
                    <small class="text-light px-3 mb-2">Instagram & Verification</small>
                    
                    <a class="nav-link" href="instagram_verifications.php">
                        <i class="fab fa-instagram"></i>Instagram Verifications
                    </a>
                    <a class="nav-link" href="influencer_follow_check.php">
                        <i class="fas fa-user-check"></i>Follow Status Check
                    </a>
                    <a class="nav-link" href="instagram_blue_check.php">
                        <i class="fas fa-check-circle"></i>Blue Check Management
                    </a>
                    <a class="nav-link" href="user_verification_status.php">
                        <i class="fas fa-clipboard-check"></i>User Follow Status
                    </a>
                    <a class="nav-link" href="suspended_users.php">
                        <i class="fas fa-ban"></i>Suspended Users
                    </a>
                    
                    <!-- Badge Management Section -->
                    <hr class="my-2 text-light">
                    <small class="text-light px-3 mb-2">Badge Management</small>
                    
                    <a class="nav-link" href="badge_management.php">
                        <i class="fas fa-award"></i>Badge Management
                    </a>
                    <a class="nav-link" href="user_badge_assignment.php">
                        <i class="fas fa-user-tag"></i>Assign Badges
                    </a>
                    
                    <!-- System Section -->
                    <hr class="my-2 text-light">
                    <small class="text-light px-3 mb-2">System</small>
                    
                    <a class="nav-link" href="settings.php">
                        <i class="fas fa-cog"></i>Settings
                    </a>
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10 main-content" style="margin-left: auto;">
            <!-- Top Bar -->
            <div class="bg-white shadow-sm p-3 mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">Submission Review</h4>
                        <small class="text-muted">Review and approve user submissions</small>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-outline-primary d-lg-none" onclick="toggleSidebar()">
                            <i class="fas fa-bars"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="p-3">
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card warning">
                            <div class="card-body text-center">
                                <i class="fas fa-clock fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['pending_review']; ?></h3>
                                <p class="mb-0">Pending Review</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card success">
                            <div class="card-body text-center">
                                <i class="fas fa-check-circle fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['approved']; ?></h3>
                                <p class="mb-0">Approved</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <i class="fas fa-times-circle fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['rejected']; ?></h3>
                                <p class="mb-0">Rejected</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card info">
                            <div class="card-body text-center">
                                <i class="fas fa-rupee-sign fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo format_currency($stats['total_approved_amount'] ?? 0); ?></h3>
                                <p class="mb-0">Total Approved</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-filter me-2"></i>Filter Submissions
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="submitted" <?php echo $status === 'submitted' ? 'selected' : ''; ?>>Pending Review</option>
                                    <option value="approved" <?php echo $status === 'approved' ? 'selected' : ''; ?>>Approved</option>
                                    <option value="rejected" <?php echo $status === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                                    <option value="" <?php echo $status === '' ? 'selected' : ''; ?>>All Status</option>
                                </select>
                            </div>
                            
                            <div class="col-md-3">
                                <label for="campaign_id" class="form-label">Campaign</label>
                                <select class="form-select" id="campaign_id" name="campaign_id">
                                    <option value="">All Campaigns</option>
                                    <?php foreach ($campaigns as $campaign): ?>
                                        <option value="<?php echo $campaign['id']; ?>" 
                                                <?php echo $campaign_id == $campaign['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($campaign['title']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-4">
                                <label for="user_search" class="form-label">User Search</label>
                                <input type="text" class="form-control" id="user_search" name="user_search" 
                                       value="<?php echo htmlspecialchars($user_search); ?>" 
                                       placeholder="Username or full name...">
                            </div>
                            
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                <a class="nav-link active" href="submissions.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Submissions List -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>Submissions (<?php echo count($submissions); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($submissions)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No submissions found</h5>
                                <p class="text-muted">No submissions match your current filters.</p>
                            </div>
                        <?php else: ?>
                            <div class="row">
                                <?php foreach ($submissions as $submission): ?>
                                    <div class="col-lg-6 col-xl-4 mb-4">
                                        <div class="card h-100 border-0 shadow-sm">
                                            <div class="card-header bg-white border-0 pb-2">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div>
                                                        <h6 class="mb-1"><?php echo htmlspecialchars($submission['campaign_title']); ?></h6>
                                                        <small class="text-muted">by @<?php echo htmlspecialchars($submission['username']); ?></small>
                                                    </div>
                                                    <?php
                                                    $status_class = '';
                                                    switch ($submission['status']) {
                                                        case 'submitted':
                                                            $status_class = 'bg-warning';
                                                            break;
                                                        case 'approved':
                                                            $status_class = 'bg-success';
                                                            break;
                                                        case 'rejected':
                                                            $status_class = 'bg-danger';
                                                            break;
                                                    }
                                                    ?>
                                                    <span class="badge <?php echo $status_class; ?>">
                                                        <?php echo ucfirst($submission['status']); ?>
                                                    </span>
                                                </div>
                                            </div>
                                            
                                            <div class="card-body">
                                                <div class="mb-3">
                                                    <strong class="text-success"><?php echo format_currency($submission['reward_amount']); ?></strong>
                                                    <span class="text-muted"> • <?php echo htmlspecialchars($submission['brand_name'] ?? 'RealEarners'); ?></span>
                                                </div>
                                                
                                                <?php if ($submission['post_url']): ?>
                                                    <div class="mb-2">
                                                        <small class="text-muted">Post URL:</small><br>
                                                        <a href="<?php echo htmlspecialchars($submission['post_url']); ?>" 
                                                           target="_blank" class="text-primary small">
                                                            View Instagram Post <i class="fas fa-external-link-alt"></i>
                                                        </a>
                                                    </div>
                                                <?php endif; ?>
                                                
                                                <?php if ($submission['screenshot_path']): ?>
                                                    <div class="mb-2">
                                                        <small class="text-muted">Screenshot:</small><br>
                                                        <img src="<?php echo htmlspecialchars($submission['screenshot_path']); ?>" 
                                                             alt="Submission Screenshot" class="img-fluid rounded" 
                                                             style="max-height: 150px; cursor: pointer;" 
                                                             onclick="showImageModal('<?php echo htmlspecialchars($submission['screenshot_path']); ?>')">
                                                    </div>
                                                <?php endif; ?>
                                                
                                                <div class="mb-2">
                                                    <small class="text-muted">Submitted:</small>
                                                    <small><?php echo time_ago($submission['submitted_at']); ?></small>
                                                </div>
                                            </div>
                                            
                                            <div class="card-footer bg-white border-0">
                                                <?php if ($submission['status'] === 'submitted'): ?>
                                                    <div class="d-grid gap-2">
                                                        <button class="btn btn-success btn-sm" 
                                                                data-bs-toggle="modal" 
                                                                data-bs-target="#reviewModal<?php echo $submission['id']; ?>">
                                                            <i class="fas fa-eye me-1"></i>Review Submission
                                                        </button>
                                                    </div>
                                                <?php else: ?>
                                                    <button class="btn btn-outline-info btn-sm w-100" 
                                                            data-bs-toggle="modal" 
                                                            data-bs-target="#viewModal<?php echo $submission['id']; ?>">
                                                        <i class="fas fa-eye me-1"></i>View Details
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
