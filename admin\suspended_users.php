<?php
require_once '../config.php';
require_once '../includes/instagram_verification.php';
require_login(['admin']);

$db = Database::getInstance();
$verification_system = new InstagramVerification();

// Handle unsuspension
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['unsuspend_user'])) {
        $verification_id = intval($_POST['verification_id']);
        $notes = sanitize_input($_POST['unsuspend_notes']);
        
        try {
            if ($verification_system->unsuspendUser($verification_id, get_user_id(), $notes)) {
                $_SESSION['success'] = 'User unsuspended successfully!';
            } else {
                $_SESSION['error'] = 'Failed to unsuspend user.';
            }
        } catch (Exception $e) {
            $_SESSION['error'] = 'Failed to unsuspend user: ' . $e->getMessage();
        }
    }
    
    if (isset($_POST['bulk_unsuspend'])) {
        $verification_ids = $_POST['verification_ids'] ?? [];
        $success_count = 0;
        
        foreach ($verification_ids as $verification_id) {
            if ($verification_system->unsuspendUser($verification_id, get_user_id(), 'Bulk unsuspension by admin')) {
                $success_count++;
            }
        }
        
        $_SESSION['success'] = "Successfully unsuspended {$success_count} users.";
    }
}

// Get all suspended users
$suspended_users = $db->fetchAll("
    SELECT iv.*, 
           CASE 
               WHEN iv.user_type = 'user' THEN u.full_name
               WHEN iv.user_type = 'influencer' THEN i.full_name
               WHEN iv.user_type = 'brand' THEN b.company_name
           END as user_name,
           CASE 
               WHEN iv.user_type = 'user' THEN u.email
               WHEN iv.user_type = 'influencer' THEN i.email
               WHEN iv.user_type = 'brand' THEN b.email
           END as user_email,
           CASE 
               WHEN iv.user_type = 'user' THEN u.phone
               WHEN iv.user_type = 'influencer' THEN i.phone
               WHEN iv.user_type = 'brand' THEN b.phone
           END as user_phone,
           DATEDIFF(NOW(), iv.suspension_date) as days_suspended,
           admin.full_name as suspended_by_name
    FROM instagram_verifications iv
    LEFT JOIN users u ON iv.user_type = 'user' AND iv.user_id = u.id
    LEFT JOIN influencers i ON iv.user_type = 'influencer' AND iv.user_id = i.id
    LEFT JOIN brands b ON iv.user_type = 'brand' AND iv.user_id = b.id
    LEFT JOIN admin ON iv.verified_by = admin.id
    WHERE iv.verification_status = 'suspended'
    ORDER BY iv.suspension_date DESC
");

// Get statistics
$stats = [
    'total_suspended' => count($suspended_users),
    'auto_suspended' => count(array_filter($suspended_users, function($u) { 
        return strpos($u['suspension_reason'], 'Auto-suspended') !== false; 
    })),
    'manual_suspended' => count(array_filter($suspended_users, function($u) { 
        return strpos($u['suspension_reason'], 'Auto-suspended') === false; 
    })),
    'recent_suspended' => count(array_filter($suspended_users, function($u) { 
        return $u['days_suspended'] <= 1; 
    }))
];

$page_title = 'Suspended Users Management';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-xl-2 sidebar p-0">
            <div class="p-3">
                <h5 class="text-white mb-4">
                    <i class="fas fa-crown me-2"></i>Admin Panel
                </h5>
                
                                <nav class="nav flex-column">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a class="nav-link" href="users.php">
                        <i class="fas fa-users"></i>Users
                    </a>
                    <a class="nav-link" href="influencers.php">
                        <i class="fas fa-star"></i>Influencers
                    </a>
                    <a class="nav-link" href="brands.php">
                        <i class="fas fa-building"></i>Brands
                    </a>
                    <a class="nav-link" href="campaigns.php">
                        <i class="fas fa-bullhorn"></i>Campaigns
                    </a>
                    <a class="nav-link" href="submissions.php">
                        <i class="fas fa-clipboard-check"></i>Submissions
                    </a>
                    <a class="nav-link" href="assignments.php">
                        <i class="fas fa-user-check"></i>Assignments
                    </a>
                    <a class="nav-link" href="payouts.php">
                        <i class="fas fa-money-bill-wave"></i>Payouts
                    </a>
                    
                    <!-- Instagram & Verification Section -->
                    <hr class="my-2 text-light">
                    <small class="text-light px-3 mb-2">Instagram & Verification</small>
                    
                    <a class="nav-link" href="instagram_verifications.php">
                        <i class="fab fa-instagram"></i>Instagram Verifications
                    </a>
                    <a class="nav-link" href="influencer_follow_check.php">
                        <i class="fas fa-user-check"></i>Follow Status Check
                    </a>
                    <a class="nav-link" href="instagram_blue_check.php">
                        <i class="fas fa-check-circle"></i>Blue Check Management
                    </a>
                    <a class="nav-link" href="user_verification_status.php">
                        <i class="fas fa-clipboard-check"></i>User Follow Status
                    </a>
                    <a class="nav-link active" href="suspended_users.php" class="nav-link active">
                        <i class="fas fa-ban"></i>Suspended Users
                    </a>
                    
                    <!-- Badge Management Section -->
                    <hr class="my-2 text-light">
                    <small class="text-light px-3 mb-2">Badge Management</small>
                    
                    <a class="nav-link" href="badge_management.php">
                        <i class="fas fa-award"></i>Badge Management
                    </a>
                    <a class="nav-link" href="user_badge_assignment.php">
                        <i class="fas fa-user-tag"></i>Assign Badges
                    </a>
                    
                    <!-- System Section -->
                    <hr class="my-2 text-light">
                    <small class="text-light px-3 mb-2">System</small>
                    
                    <a class="nav-link" href="settings.php">
                        <i class="fas fa-cog"></i>Settings
                    </a>
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10 main-content" style="margin-left: auto;">
            <!-- Top Bar -->
            <div class="bg-white shadow-sm p-3 mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">Suspended Users Management</h4>
                        <small class="text-muted">Manage users suspended for Instagram verification violations</small>
                    </div>
                </div>
            </div>
            
            <div class="p-3">
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card danger">
                            <div class="card-body text-center">
                                <i class="fas fa-user-slash fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['total_suspended']; ?></h3>
                                <p class="mb-0">Total Suspended</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card warning">
                            <div class="card-body text-center">
                                <i class="fas fa-robot fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['auto_suspended']; ?></h3>
                                <p class="mb-0">Auto-Suspended</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card info">
                            <div class="card-body text-center">
                                <i class="fas fa-user-cog fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['manual_suspended']; ?></h3>
                                <p class="mb-0">Manual Suspended</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card" style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);">
                            <div class="card-body text-center text-white">
                                <i class="fas fa-clock fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['recent_suspended']; ?></h3>
                                <p class="mb-0">Recent (24h)</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Bulk Actions -->
                <?php if (!empty($suspended_users)): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-tools me-2"></i>Bulk Actions
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="bulkForm">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="selectAll">
                                        <label class="form-check-label" for="selectAll">
                                            Select All Suspended Users
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6 text-end">
                                    <button type="submit" name="bulk_unsuspend" class="btn btn-success" 
                                            onclick="return confirm('Are you sure you want to unsuspend all selected users?')">
                                        <i class="fas fa-undo me-2"></i>Bulk Unsuspend Selected
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <?php endif; ?>
                
                <!-- Suspended Users Table -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-user-slash me-2"></i>Suspended Users (<?php echo count($suspended_users); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($suspended_users)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-smile fa-3x text-success mb-3"></i>
                                <h5 class="text-success">No Suspended Users!</h5>
                                <p class="text-muted">All users are following the required Instagram accounts.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th width="50">
                                                <input type="checkbox" id="selectAllTable" class="form-check-input">
                                            </th>
                                            <th>User</th>
                                            <th>Type</th>
                                            <th>Instagram</th>
                                            <th>Suspension Reason</th>
                                            <th>Suspended</th>
                                            <th>Days</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($suspended_users as $user): ?>
                                        <tr>
                                            <td>
                                                <input type="checkbox" name="verification_ids[]" 
                                                       value="<?php echo $user['id']; ?>" 
                                                       class="form-check-input user-checkbox"
                                                       form="bulkForm">
                                            </td>
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($user['user_name']); ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($user['user_email']); ?></small>
                                                    <?php if ($user['user_phone']): ?>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($user['user_phone']); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo $user['user_type'] === 'user' ? 'primary' : 
                                                        ($user['user_type'] === 'influencer' ? 'warning' : 'info'); 
                                                ?>">
                                                    <?php echo ucfirst($user['user_type']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <a href="https://instagram.com/<?php echo htmlspecialchars($user['instagram_username']); ?>" 
                                                   target="_blank" class="text-decoration-none">
                                                    <i class="fab fa-instagram me-1"></i>@<?php echo htmlspecialchars($user['instagram_username']); ?>
                                                </a>
                                            </td>
                                            <td>
                                                <small><?php echo htmlspecialchars($user['suspension_reason']); ?></small>
                                                <?php if ($user['suspended_by_name']): ?>
                                                    <br><small class="text-muted">by <?php echo htmlspecialchars($user['suspended_by_name']); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <small><?php echo date('M j, Y g:i A', strtotime($user['suspension_date'])); ?></small>
                                            </td>
                                            <td>
                                                <span class="fw-bold text-danger">
                                                    <?php echo $user['days_suspended']; ?> days
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button class="btn btn-sm btn-success" 
                                                            data-bs-toggle="modal" 
                                                            data-bs-target="#unsuspendModal<?php echo $user['id']; ?>">
                                                        <i class="fas fa-undo me-1"></i>Unsuspend
                                                    </button>
                                                    
                                                    <a href="https://instagram.com/<?php echo htmlspecialchars($user['instagram_username']); ?>/followers/" 
                                                       target="_blank" 
                                                       class="btn btn-sm btn-outline-info">
                                                        <i class="fas fa-external-link-alt me-1"></i>Check
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Unsuspend Modals -->
<?php foreach ($suspended_users as $user): ?>
<div class="modal fade" id="unsuspendModal<?php echo $user['id']; ?>" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success">
                <h5 class="modal-title text-white">
                    <i class="fas fa-undo me-2"></i>Unsuspend User Account
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <div class="alert alert-success">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Unsuspend Account:</strong> This will restore the user's access to all panels.
                    </div>
                    
                    <p><strong>User:</strong> <?php echo htmlspecialchars($user['user_name']); ?></p>
                    <p><strong>Instagram:</strong> @<?php echo htmlspecialchars($user['instagram_username']); ?></p>
                    <p><strong>Suspended:</strong> <?php echo $user['days_suspended']; ?> days ago</p>
                    <p><strong>Reason:</strong> <?php echo htmlspecialchars($user['suspension_reason']); ?></p>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-lightbulb me-2"></i>
                        <strong>Before unsuspending:</strong>
                        <ul class="mb-0 mt-2">
                            <li>Verify that the user has now followed both required Instagram accounts</li>
                            <li>Check their Instagram profile to confirm follows</li>
                            <li>Add notes explaining why you're unsuspending them</li>
                        </ul>
                    </div>
                    
                    <div class="mb-3">
                        <label for="unsuspend_notes_<?php echo $user['id']; ?>" class="form-label">Unsuspension Notes:</label>
                        <textarea class="form-control" 
                                  id="unsuspend_notes_<?php echo $user['id']; ?>" 
                                  name="unsuspend_notes" 
                                  rows="3" 
                                  placeholder="Explain why you're unsuspending this user..."
                                  required></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" 
                                   id="confirm_follows_<?php echo $user['id']; ?>" 
                                   required>
                            <label class="form-check-label" for="confirm_follows_<?php echo $user['id']; ?>">
                                I have verified that this user now follows both required Instagram accounts
                            </label>
                        </div>
                    </div>
                    
                    <input type="hidden" name="verification_id" value="<?php echo $user['id']; ?>">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="unsuspend_user" class="btn btn-success">
                        <i class="fas fa-undo me-2"></i>Unsuspend Account
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endforeach; ?>

<script>
// Bulk selection functionality
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.user-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

document.getElementById('selectAllTable').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.user-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
    document.getElementById('selectAll').checked = this.checked;
});
</script>

<?php include '../includes/footer.php'; ?>
