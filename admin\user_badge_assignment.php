<?php
require_once '../config.php';
require_once '../includes/badge_system.php';
require_login(['admin']);

$db = Database::getInstance();
$badge_system = new BadgeSystem();

$success = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'assign_badge') {
        $user_id = intval($_POST['user_id'] ?? 0);
        $user_type = $_POST['user_type'] ?? '';
        $badge_id = intval($_POST['badge_id'] ?? 0);
        $notes = trim($_POST['notes'] ?? '');
        
        if ($user_id > 0 && !empty($user_type) && $badge_id > 0) {
            if ($badge_system->assignBadge($user_id, $user_type, $badge_id, get_user_id(), $notes)) {
                $success = 'Badge assigned successfully!';
            } else {
                $error = 'Failed to assign badge. Badge may already be assigned.';
            }
        } else {
            $error = 'Please fill in all required fields.';
        }
    } elseif ($action === 'remove_badge') {
        $user_id = intval($_POST['user_id'] ?? 0);
        $user_type = $_POST['user_type'] ?? '';
        $badge_id = intval($_POST['badge_id'] ?? 0);
        
        if ($badge_system->removeBadge($user_id, $user_type, $badge_id)) {
            $success = 'Badge removed successfully!';
        } else {
            $error = 'Failed to remove badge.';
        }
    }
}

// Get filter parameters
$filter_type = $_GET['user_type'] ?? 'all';
$filter_badge = $_GET['badge_id'] ?? '';
$search = trim($_GET['search'] ?? '');

// Get all badges for dropdown
$all_badges = $badge_system->getAllBadges();

// Get users based on filters
$users = [];
if ($filter_type === 'all' || $filter_type === 'user') {
    $user_query = "
        SELECT u.id, u.username, u.email, u.full_name, u.created_at,
               'user' as account_type
        FROM users u
        ORDER BY u.full_name
    ";
    $users = array_merge($users, $db->fetchAll($user_query));
}

if ($filter_type === 'all' || $filter_type === 'influencer') {
    $influencer_query = "
        SELECT i.id, i.username, i.email, i.full_name, i.created_at,
               'influencer' as account_type
        FROM influencers i
        ORDER BY i.full_name
    ";
    $users = array_merge($users, $db->fetchAll($influencer_query));
}

// Apply search filter
if (!empty($search)) {
    $users = array_filter($users, function($user) use ($search) {
        return stripos($user['full_name'], $search) !== false || 
               stripos($user['email'], $search) !== false ||
               stripos($user['username'], $search) !== false;
    });
}

// Get user badges for each user
foreach ($users as &$user) {
    $user['badges'] = $badge_system->getUserBadges($user['id'], $user['account_type']);
}

// Filter by specific badge if selected
if (!empty($filter_badge)) {
    $users = array_filter($users, function($user) use ($filter_badge) {
        foreach ($user['badges'] as $badge) {
            if ($badge['id'] == $filter_badge) {
                return true;
            }
        }
        return false;
    });
}

$page_title = 'User Badge Assignment';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-xl-2 sidebar p-0">
            <div class="p-3">
                <h5 class="text-white mb-4">
                    <i class="fas fa-shield-alt me-2"></i>Admin Panel
                </h5>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a class="nav-link" href="users.php">
                        <i class="fas fa-users"></i>Users
                    </a>
                    <a class="nav-link" href="influencers.php">
                        <i class="fas fa-star"></i>Influencers
                    </a>
                    <a class="nav-link" href="brands.php">
                        <i class="fas fa-building"></i>Brands
                    </a>
                    <a class="nav-link" href="campaigns.php">
                        <i class="fas fa-bullhorn"></i>Campaigns
                    </a>
                    <a class="nav-link" href="instagram_verifications.php">
                        <i class="fab fa-instagram"></i>Instagram Verifications
                    </a>
                    <a class="nav-link" href="influencer_follow_check.php">
                        <i class="fas fa-user-check"></i>Follow Status Check
                    </a>
                    <a class="nav-link" href="instagram_blue_check.php">
                        <i class="fas fa-check-circle"></i>Blue Check Management
                    </a>
                    <a class="nav-link" href="badge_management.php">
                        <i class="fas fa-award"></i>Badge Management
                    </a>
                    <a class="nav-link active" href="user_badge_assignment.php">
                        <i class="fas fa-user-tag"></i>Assign Badges
                    </a>
                    <a class="nav-link" href="suspended_users.php">
                        <i class="fas fa-ban"></i>Suspended Users
                    </a>
                    <a class="nav-link" href="user_verification_status.php">
                        <i class="fas fa-clipboard-check"></i>User Follow Status
                    </a>
                    <a class="nav-link" href="payouts.php">
                        <i class="fas fa-money-bill-wave"></i>Payouts
                    </a>
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10 main-content" style="margin-left: auto;">
            <!-- Top Bar -->
            <div class="bg-white shadow-sm p-3 mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">User Badge Assignment</h4>
                        <small class="text-muted">Assign and manage badges for users and influencers</small>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#assignBadgeModal">
                            <i class="fas fa-plus me-2"></i>Assign Badge
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="p-3">
                <!-- Success/Error Messages -->
                <?php if ($success): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Quick Stats -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo count($users); ?></h3>
                                <p class="mb-0">Total Users</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card stats-card success">
                            <div class="card-body text-center">
                                <i class="fas fa-award fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo count($all_badges); ?></h3>
                                <p class="mb-0">Available Badges</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card stats-card info">
                            <div class="card-body text-center">
                                <i class="fas fa-user-tag fa-2x mb-2"></i>
                                <h3 class="fw-bold">
                                    <?php 
                                    $total_assignments = 0;
                                    foreach ($users as $user) {
                                        $total_assignments += count($user['badges']);
                                    }
                                    echo $total_assignments;
                                    ?>
                                </h3>
                                <p class="mb-0">Total Assignments</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card stats-card warning">
                            <div class="card-body text-center">
                                <i class="fas fa-percentage fa-2x mb-2"></i>
                                <h3 class="fw-bold">
                                    <?php 
                                    $users_with_badges = count(array_filter($users, function($user) {
                                        return count($user['badges']) > 0;
                                    }));
                                    echo count($users) > 0 ? round(($users_with_badges / count($users)) * 100) : 0;
                                    ?>%
                                </h3>
                                <p class="mb-0">Users with Badges</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-3">
                                <label for="user_type" class="form-label">User Type</label>
                                <select class="form-select" id="user_type" name="user_type">
                                    <option value="all" <?php echo $filter_type === 'all' ? 'selected' : ''; ?>>All Users</option>
                                    <option value="user" <?php echo $filter_type === 'user' ? 'selected' : ''; ?>>Regular Users</option>
                                    <option value="influencer" <?php echo $filter_type === 'influencer' ? 'selected' : ''; ?>>Influencers</option>
                                </select>
                            </div>

                            <div class="col-md-3">
                                <label for="badge_id" class="form-label">Filter by Badge</label>
                                <select class="form-select" id="badge_id" name="badge_id">
                                    <option value="">All Badges</option>
                                    <?php foreach ($all_badges as $badge): ?>
                                        <option value="<?php echo $badge['id']; ?>" <?php echo $filter_badge == $badge['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($badge['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="col-md-4">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" class="form-control" id="search" name="search"
                                       value="<?php echo htmlspecialchars($search); ?>"
                                       placeholder="Search by name, email, or username">
                            </div>

                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-1"></i>Filter
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Users List -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>Users and Their Badges
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($users)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <h6 class="text-muted">No users found</h6>
                                <p class="text-muted">Try adjusting your search criteria.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>User</th>
                                            <th>Type</th>
                                            <th>Current Badges</th>
                                            <th>Member Since</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($users as $user): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <?php if ($user['account_type'] === 'influencer'): ?>
                                                        <div class="bg-warning rounded-circle d-flex align-items-center justify-content-center me-2"
                                                             style="width: 40px; height: 40px;">
                                                            <i class="fas fa-star text-white"></i>
                                                        </div>
                                                    <?php else: ?>
                                                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-2"
                                                             style="width: 40px; height: 40px;">
                                                            <i class="fas fa-user text-white"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                    <div>
                                                        <strong><?php echo htmlspecialchars($user['full_name']); ?></strong>
                                                        <br><small class="text-muted">@<?php echo htmlspecialchars($user['username']); ?></small>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($user['email']); ?></small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <?php if ($user['account_type'] === 'influencer'): ?>
                                                    <span class="badge bg-warning">
                                                        <i class="fas fa-star me-1"></i>Influencer
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge bg-primary">
                                                        <i class="fas fa-user me-1"></i>User
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="d-flex flex-wrap gap-1">
                                                    <?php if (empty($user['badges'])): ?>
                                                        <small class="text-muted">No badges assigned</small>
                                                    <?php else: ?>
                                                        <?php foreach ($user['badges'] as $badge): ?>
                                                            <span class="badge bg-<?php echo $badge['color']; ?> position-relative">
                                                                <i class="<?php echo $badge['icon']; ?> me-1"></i>
                                                                <?php echo htmlspecialchars($badge['name']); ?>
                                                                <button type="button" class="btn-close btn-close-white position-absolute top-0 start-100 translate-middle"
                                                                        style="font-size: 0.6em; padding: 0.1em;"
                                                                        onclick="removeBadge(<?php echo $user['id']; ?>, '<?php echo $user['account_type']; ?>', <?php echo $badge['id']; ?>)"
                                                                        title="Remove badge"></button>
                                                            </span>
                                                        <?php endforeach; ?>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <small><?php echo date('M j, Y', strtotime($user['created_at'])); ?></small>
                                            </td>
                                            <td>
                                                <button class="btn btn-sm btn-success"
                                                        onclick="assignBadgeToUser(<?php echo $user['id']; ?>, '<?php echo $user['account_type']; ?>', '<?php echo htmlspecialchars($user['full_name']); ?>')">
                                                    <i class="fas fa-plus me-1"></i>Assign Badge
                                                </button>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Assign Badge Modal -->
<div class="modal fade" id="assignBadgeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Assign Badge</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="assign_badge">
                    <input type="hidden" name="user_id" id="assign_user_id">
                    <input type="hidden" name="user_type" id="assign_user_type">

                    <div class="mb-3">
                        <label class="form-label">Assigning to:</label>
                        <div id="assign_user_info" class="alert alert-info"></div>
                    </div>

                    <div class="mb-3">
                        <label for="assign_badge_id" class="form-label">Select Badge *</label>
                        <select class="form-select" id="assign_badge_id" name="badge_id" required>
                            <option value="">Choose a badge...</option>
                            <?php foreach ($all_badges as $badge): ?>
                                <option value="<?php echo $badge['id']; ?>"
                                        data-icon="<?php echo $badge['icon']; ?>"
                                        data-color="<?php echo $badge['color']; ?>">
                                    <?php echo htmlspecialchars($badge['name']); ?> (<?php echo ucfirst($badge['badge_type']); ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="assign_notes" class="form-label">Notes (Optional)</label>
                        <textarea class="form-control" id="assign_notes" name="notes" rows="3"
                                  placeholder="Reason for assigning this badge..."></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Badge Preview</label>
                        <div id="assign_badge_preview">
                            <small class="text-muted">Select a badge to see preview</small>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Assign Badge</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Remove Badge Form (Hidden) -->
<form id="removeBadgeForm" method="POST" style="display: none;">
    <input type="hidden" name="action" value="remove_badge">
    <input type="hidden" name="user_id" id="remove_user_id">
    <input type="hidden" name="user_type" id="remove_user_type">
    <input type="hidden" name="badge_id" id="remove_badge_id">
</form>

<script>
// Assign badge to specific user
function assignBadgeToUser(userId, userType, userName) {
    document.getElementById('assign_user_id').value = userId;
    document.getElementById('assign_user_type').value = userType;
    document.getElementById('assign_user_info').innerHTML =
        `<i class="fas fa-user me-2"></i><strong>${userName}</strong> (${userType.charAt(0).toUpperCase() + userType.slice(1)})`;

    // Reset form
    document.getElementById('assign_badge_id').value = '';
    document.getElementById('assign_notes').value = '';
    document.getElementById('assign_badge_preview').innerHTML = '<small class="text-muted">Select a badge to see preview</small>';

    const modal = new bootstrap.Modal(document.getElementById('assignBadgeModal'));
    modal.show();
}

// Remove badge from user
function removeBadge(userId, userType, badgeId) {
    if (confirm('Are you sure you want to remove this badge from the user?')) {
        document.getElementById('remove_user_id').value = userId;
        document.getElementById('remove_user_type').value = userType;
        document.getElementById('remove_badge_id').value = badgeId;
        document.getElementById('removeBadgeForm').submit();
    }
}

// Update badge preview
function updateBadgePreview() {
    const select = document.getElementById('assign_badge_id');
    const preview = document.getElementById('assign_badge_preview');

    if (select.value) {
        const option = select.options[select.selectedIndex];
        const icon = option.getAttribute('data-icon');
        const color = option.getAttribute('data-color');
        const name = option.text.split(' (')[0]; // Remove the type part

        preview.innerHTML = `<span class="badge bg-${color}"><i class="${icon} me-1"></i>${name}</span>`;
    } else {
        preview.innerHTML = '<small class="text-muted">Select a badge to see preview</small>';
    }
}

// Add event listeners
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('assign_badge_id').addEventListener('change', updateBadgePreview);
});
</script>

<?php include '../includes/footer.php'; ?>
