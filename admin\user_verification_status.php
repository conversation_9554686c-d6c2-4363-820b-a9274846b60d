<?php
require_once '../config.php';
require_once '../includes/instagram_verification.php';
require_login(['admin']);

$db = Database::getInstance();
$verification_system = new InstagramVerification();

// Get filter parameters
$filter = $_GET['filter'] ?? 'all';
$search = $_GET['search'] ?? '';
$user_type_filter = $_GET['user_type'] ?? '';

// Build query conditions
$where_conditions = [];
$params = [];

if ($search) {
    $where_conditions[] = "(full_name LIKE ? OR email LIKE ? OR username LIKE ? OR instagram_username LIKE ?)";
    $params = array_merge($params, ["%$search%", "%$search%", "%$search%", "%$search%"]);
}

if ($user_type_filter) {
    $where_conditions[] = "account_type = ?";
    $params[] = $user_type_filter;
}

// Filter by verification status
switch ($filter) {
    case 'verified':
        $where_conditions[] = "verified = 1";
        break;
    case 'pending':
        $where_conditions[] = "verification_status = 'pending'";
        break;
    case 'suspended':
        $where_conditions[] = "verification_status = 'suspended'";
        break;
    case 'not_submitted':
        $where_conditions[] = "verification_id IS NULL";
        break;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get all users and influencers with their verification status
$users_query = "
    SELECT u.id, u.username, u.email, u.full_name, u.created_at,
           'user' as account_type,
           iv.id as verification_id,
           iv.instagram_username,
           iv.verification_status,
           iv.verified,
           iv.accounts_verified,
           iv.submitted_at,
           iv.verified_at,
           iv.suspension_date,
           iv.suspension_reason,
           DATEDIFF(NOW(), iv.submitted_at) as days_since_submission,
           DATEDIFF(NOW(), iv.suspension_date) as days_suspended
    FROM users u
    LEFT JOIN instagram_verifications iv ON u.id = iv.user_id AND iv.user_type = 'user'
";

$influencers_query = "
    SELECT i.id, i.username, i.email, i.full_name, i.created_at,
           'influencer' as account_type,
           iv.id as verification_id,
           iv.instagram_username,
           iv.verification_status,
           iv.verified,
           iv.accounts_verified,
           iv.submitted_at,
           iv.verified_at,
           iv.suspension_date,
           iv.suspension_reason,
           DATEDIFF(NOW(), iv.submitted_at) as days_since_submission,
           DATEDIFF(NOW(), iv.suspension_date) as days_suspended
    FROM influencers i
    LEFT JOIN instagram_verifications iv ON i.id = iv.user_id AND iv.user_type = 'influencer'
";

// Combine both queries
$combined_query = "($users_query) UNION ALL ($influencers_query)";

// Apply filters to the combined query
if (!empty($where_conditions)) {
    // We need to modify the where conditions to work with the UNION
    $filtered_where = implode(' AND ', $where_conditions);
    $combined_query = "
        SELECT * FROM (
            $combined_query
        ) as combined_users
        WHERE $filtered_where
    ";
}

$combined_query .= "
    ORDER BY
        CASE
            WHEN verification_status = 'suspended' THEN 1
            WHEN verification_status = 'pending' THEN 2
            WHEN verified = 1 THEN 3
            ELSE 4
        END,
        created_at DESC
";

$users = $db->fetchAll($combined_query, $params);

// Get statistics for both users and influencers
$all_users_stats = $db->fetchAll("
    SELECT u.id, iv.verified, iv.verification_status, 'user' as account_type
    FROM users u
    LEFT JOIN instagram_verifications iv ON u.id = iv.user_id AND iv.user_type = 'user'
    UNION ALL
    SELECT i.id, iv.verified, iv.verification_status, 'influencer' as account_type
    FROM influencers i
    LEFT JOIN instagram_verifications iv ON i.id = iv.user_id AND iv.user_type = 'influencer'
");

$stats = [
    'total_users' => count($all_users_stats),
    'verified' => count(array_filter($all_users_stats, function($u) { return $u['verified'] == 1; })),
    'pending' => count(array_filter($all_users_stats, function($u) { return $u['verification_status'] === 'pending'; })),
    'suspended' => count(array_filter($all_users_stats, function($u) { return $u['verification_status'] === 'suspended'; })),
    'not_submitted' => count(array_filter($all_users_stats, function($u) { return $u['verification_status'] === null; })),
    'total_regular_users' => count(array_filter($all_users_stats, function($u) { return $u['account_type'] === 'user'; })),
    'total_influencers' => count(array_filter($all_users_stats, function($u) { return $u['account_type'] === 'influencer'; }))
];

$page_title = 'User Follow Status';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-xl-2 sidebar p-0">
            <div class="p-3">
                <h5 class="text-white mb-4">
                    <i class="fas fa-crown me-2"></i>Admin Panel
                </h5>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a class="nav-link" href="users.php">
                        <i class="fas fa-users"></i>All Users
                    </a>
                    <a class="nav-link" href="suspended_users.php">
                        <i class="fas fa-user-slash"></i>Suspended Users
                    </a>
                    <a class="nav-link active" href="user_verification_status.php">
                        <i class="fab fa-instagram"></i>User Follow Status
                    </a>
                    <a class="nav-link" href="influencers.php">
                        <i class="fas fa-star"></i>Influencers
                    </a>
                    <a class="nav-link" href="campaigns.php">
                        <i class="fas fa-bullhorn"></i>Campaigns
                    </a>
                    <a class="nav-link" href="submissions.php">
                        <i class="fas fa-clipboard-check"></i>Submissions
                    </a>
                    <a class="nav-link" href="payouts.php">
                        <i class="fas fa-money-bill-wave"></i>Payouts
                    </a>
                    <a class="nav-link" href="brands.php">
                        <i class="fas fa-building"></i>Brands
                    </a>
                    <a class="nav-link" href="instagram_verifications.php">
                        <i class="fab fa-instagram"></i>Instagram Verifications
                    </a>
                    <a class="nav-link" href="settings.php">
                        <i class="fas fa-cog"></i>Settings
                    </a>
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10 main-content" style="margin-left: auto;">
            <!-- Top Bar -->
            <div class="bg-white shadow-sm p-3 mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">All Users Instagram Follow Status</h4>
                        <small class="text-muted">Monitor all users and influencers and their Instagram verification status</small>
                    </div>
                </div>
            </div>
            
            <div class="p-3">
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-lg-2 col-md-4 mb-3">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['total_users']; ?></h3>
                                <p class="mb-0 small">Total All</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-4 mb-3">
                        <div class="card stats-card info">
                            <div class="card-body text-center">
                                <i class="fas fa-user fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['total_regular_users']; ?></h3>
                                <p class="mb-0 small">Users</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-4 mb-3">
                        <div class="card stats-card warning">
                            <div class="card-body text-center">
                                <i class="fas fa-star fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['total_influencers']; ?></h3>
                                <p class="mb-0 small">Influencers</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-2 col-md-4 mb-3">
                        <div class="card stats-card success">
                            <div class="card-body text-center">
                                <i class="fas fa-check-circle fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['verified']; ?></h3>
                                <p class="mb-0 small">Verified</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-4 mb-3">
                        <div class="card stats-card warning">
                            <div class="card-body text-center">
                                <i class="fas fa-clock fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['pending']; ?></h3>
                                <p class="mb-0 small">Pending</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-4 mb-3">
                        <div class="card stats-card danger">
                            <div class="card-body text-center">
                                <i class="fas fa-ban fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['suspended']; ?></h3>
                                <p class="mb-0 small">Suspended</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-4 mb-3">
                        <div class="card stats-card secondary">
                            <div class="card-body text-center">
                                <i class="fas fa-question-circle fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['not_submitted']; ?></h3>
                                <p class="mb-0 small">Not Submitted</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-filter me-2"></i>Filter Users
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-3">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="<?php echo htmlspecialchars($search); ?>" 
                                       placeholder="Name, email, username, Instagram...">
                            </div>
                            
                            <div class="col-md-2">
                                <label for="filter" class="form-label">Status</label>
                                <select class="form-select" id="filter" name="filter">
                                    <option value="all" <?php echo $filter === 'all' ? 'selected' : ''; ?>>All Status</option>
                                    <option value="verified" <?php echo $filter === 'verified' ? 'selected' : ''; ?>>Verified</option>
                                    <option value="pending" <?php echo $filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                    <option value="suspended" <?php echo $filter === 'suspended' ? 'selected' : ''; ?>>Suspended</option>
                                    <option value="not_submitted" <?php echo $filter === 'not_submitted' ? 'selected' : ''; ?>>Not Submitted</option>
                                </select>
                            </div>
                            
                            <div class="col-md-2">
                                <label for="user_type" class="form-label">User Type</label>
                                <select class="form-select" id="user_type" name="user_type">
                                    <option value="">All Types</option>
                                    <option value="user" <?php echo $user_type_filter === 'user' ? 'selected' : ''; ?>>User</option>
                                    <option value="influencer" <?php echo $user_type_filter === 'influencer' ? 'selected' : ''; ?>>Influencer</option>
                                    <option value="brand" <?php echo $user_type_filter === 'brand' ? 'selected' : ''; ?>>Brand</option>
                                </select>
                            </div>
                            
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                <a href="user_verification_status.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Clear
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Users Table -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>Users Follow Status (<?php echo count($users); ?>)
                        </h5>
                        <div>
                            <button class="btn btn-success btn-sm" onclick="exportData()">
                                <i class="fas fa-download me-1"></i>Export CSV
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (empty($users)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No users found</h5>
                                <p class="text-muted">No users match your current filters.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>User</th>
                                            <th>Type</th>
                                            <th>Instagram</th>
                                            <th>Follow Status</th>
                                            <th>Verification Status</th>
                                            <th>Timeline</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($users as $user): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <?php if ($user['account_type'] === 'influencer'): ?>
                                                        <div class="bg-warning rounded-circle d-flex align-items-center justify-content-center me-2"
                                                             style="width: 40px; height: 40px;">
                                                            <i class="fas fa-star text-white"></i>
                                                        </div>
                                                    <?php else: ?>
                                                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-2"
                                                             style="width: 40px; height: 40px;">
                                                            <i class="fas fa-user text-white"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                    <div>
                                                        <strong><?php echo htmlspecialchars($user['full_name']); ?></strong>
                                                        <br><small class="text-muted">@<?php echo htmlspecialchars($user['username']); ?></small>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($user['email']); ?></small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <?php if ($user['account_type'] === 'influencer'): ?>
                                                    <span class="badge bg-warning">
                                                        <i class="fas fa-star me-1"></i>Influencer
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge bg-primary">
                                                        <i class="fas fa-user me-1"></i>User
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($user['instagram_username']): ?>
                                                    <a href="https://instagram.com/<?php echo htmlspecialchars($user['instagram_username']); ?>" 
                                                       target="_blank" class="text-decoration-none">
                                                        <i class="fab fa-instagram me-1"></i>@<?php echo htmlspecialchars($user['instagram_username']); ?>
                                                    </a>
                                                <?php else: ?>
                                                    <span class="text-muted">Not provided</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($user['verified']): ?>
                                                    <?php 
                                                    $verified_accounts = json_decode($user['accounts_verified'], true) ?: [];
                                                    $required_accounts = $verification_system->getRequiredAccounts();
                                                    ?>
                                                    <div class="d-flex flex-column gap-1">
                                                        <?php foreach ($required_accounts as $account): ?>
                                                            <div class="d-flex align-items-center">
                                                                <?php if (in_array($account, $verified_accounts)): ?>
                                                                    <i class="fas fa-check-circle text-success me-2"></i>
                                                                    <span class="text-success small">@<?php echo $account; ?></span>
                                                                <?php else: ?>
                                                                    <i class="fas fa-times-circle text-danger me-2"></i>
                                                                    <span class="text-danger small">@<?php echo $account; ?></span>
                                                                <?php endif; ?>
                                                            </div>
                                                        <?php endforeach; ?>
                                                    </div>
                                                <?php elseif ($user['verification_status']): ?>
                                                    <span class="text-warning">
                                                        <i class="fas fa-clock me-1"></i>Verification pending
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">
                                                        <i class="fas fa-question-circle me-1"></i>Not submitted
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                $status_class = '';
                                                $status_text = '';
                                                $status_icon = '';
                                                
                                                if ($user['verified']) {
                                                    $status_class = 'bg-success';
                                                    $status_text = 'Verified';
                                                    $status_icon = 'fas fa-check-circle';
                                                } elseif ($user['verification_status'] === 'suspended') {
                                                    $status_class = 'bg-danger';
                                                    $status_text = 'Suspended';
                                                    $status_icon = 'fas fa-ban';
                                                } elseif ($user['verification_status'] === 'pending') {
                                                    $status_class = 'bg-warning';
                                                    $status_text = 'Pending';
                                                    $status_icon = 'fas fa-clock';
                                                } else {
                                                    $status_class = 'bg-secondary';
                                                    $status_text = 'Not Submitted';
                                                    $status_icon = 'fas fa-question-circle';
                                                }
                                                ?>
                                                <span class="badge <?php echo $status_class; ?>">
                                                    <i class="<?php echo $status_icon; ?> me-1"></i><?php echo $status_text; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($user['verification_status'] === 'suspended'): ?>
                                                    <small class="text-danger">
                                                        Suspended <?php echo $user['days_suspended']; ?> days ago
                                                    </small>
                                                <?php elseif ($user['verified']): ?>
                                                    <small class="text-success">
                                                        Verified <?php echo date('M j', strtotime($user['verified_at'])); ?>
                                                    </small>
                                                <?php elseif ($user['verification_status'] === 'pending'): ?>
                                                    <small class="text-warning">
                                                        Pending <?php echo $user['days_since_submission']; ?> days
                                                    </small>
                                                <?php else: ?>
                                                    <small class="text-muted">
                                                        Joined <?php echo date('M j', strtotime($user['created_at'])); ?>
                                                    </small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <?php if ($user['instagram_username']): ?>
                                                        <a href="https://instagram.com/<?php echo htmlspecialchars($user['instagram_username']); ?>/followers/" 
                                                           target="_blank" 
                                                           class="btn btn-sm btn-outline-info">
                                                            <i class="fas fa-external-link-alt me-1"></i>Check
                                                        </a>
                                                    <?php endif; ?>
                                                    
                                                    <?php if ($user['verification_id']): ?>
                                                        <a href="instagram_verifications.php" 
                                                           class="btn btn-sm btn-outline-primary">
                                                            <i class="fas fa-cog me-1"></i>Manage
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportData() {
    window.location.href = 'export.php?type=user_verification_status';
}
</script>

<?php include '../includes/footer.php'; ?>
