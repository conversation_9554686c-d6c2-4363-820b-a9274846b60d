<?php
require_once '../config.php';
require_login(['admin']);

$db = Database::getInstance();

// Handle user actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['update_status'])) {
        $user_id = intval($_POST['user_id']);
        $status = sanitize_input($_POST['status']);
        
        try {
            $db->update('users', ['status' => $status], 'id = ?', [$user_id]);
            $_SESSION['success'] = 'User status updated successfully!';
        } catch (Exception $e) {
            $_SESSION['error'] = 'Failed to update user status.';
        }
    }
    
    if (isset($_POST['delete_user'])) {
        $user_id = intval($_POST['user_id']);
        
        try {
            // Delete related records first
            $db->delete('user_tasks', 'user_id = ?', [$user_id]);
            $db->delete('user_submissions', 'user_id = ?', [$user_id]);
            $db->delete('payouts', 'user_type = ? AND user_id = ?', ['user', $user_id]);
            $db->delete('users', 'id = ?', [$user_id]);
            
            $_SESSION['success'] = 'User deleted successfully!';
        } catch (Exception $e) {
            $_SESSION['error'] = 'Failed to delete user.';
        }
    }
}

// Get filter parameters
$search = $_GET['search'] ?? '';
$status = $_GET['status'] ?? '';
$sort = $_GET['sort'] ?? 'created_at';
$order = $_GET['order'] ?? 'DESC';

// Build query
$conditions = ['1=1'];
$params = [];

if ($search) {
    $conditions[] = "(username LIKE ? OR full_name LIKE ? OR email LIKE ?)";
    $search_param = "%{$search}%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

if ($status) {
    $conditions[] = "status = ?";
    $params[] = $status;
}

$where_clause = implode(' AND ', $conditions);
$order_clause = "ORDER BY {$sort} {$order}";

// Get users with Instagram verification status
$users = $db->fetchAll("
    SELECT u.*,
           COUNT(ut.id) as total_tasks,
           SUM(CASE WHEN ut.status = 'approved' THEN 1 ELSE 0 END) as completed_tasks,
           iv.verification_status,
           iv.verified as instagram_verified,
           iv.instagram_username
    FROM users u
    LEFT JOIN user_tasks ut ON u.id = ut.user_id
    LEFT JOIN instagram_verifications iv ON u.id = iv.user_id AND iv.user_type = 'user'
    WHERE {$where_clause}
    GROUP BY u.id
    {$order_clause}
", $params);

// Get statistics
$stats = $db->fetch("
    SELECT 
        COUNT(*) as total_users,
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_users,
        SUM(CASE WHEN status = 'inactive' THEN 1 ELSE 0 END) as inactive_users,
        SUM(CASE WHEN status = 'suspended' THEN 1 ELSE 0 END) as suspended_users,
        SUM(total_earned) as total_paid
    FROM users
");

$page_title = 'User Management';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-xl-2 sidebar p-0">
            <div class="p-3">
                <h5 class="text-white mb-4">
                    <i class="fas fa-crown me-2"></i>Admin Panel
                </h5>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a class="nav-link active" href="users.php">
                        <i class="fas fa-users"></i>Users
                    </a>
                    <a class="nav-link" href="influencers.php">
                        <i class="fas fa-star"></i>Influencers
                    </a>
                    <a class="nav-link" href="brands.php">
                        <i class="fas fa-building"></i>Brands
                    </a>
                    <a class="nav-link" href="campaigns.php">
                        <i class="fas fa-bullhorn"></i>Campaigns
                    </a>
                    <a class="nav-link" href="submissions.php">
                        <i class="fas fa-clipboard-check"></i>Submissions
                    </a>
                    <a class="nav-link" href="assignments.php">
                        <i class="fas fa-user-check"></i>Assignments
                    </a>
                    <a class="nav-link" href="payouts.php">
                        <i class="fas fa-money-bill-wave"></i>Payouts
                    </a>

                    <!-- Instagram & Verification Section -->
                    <hr class="my-2 text-light">
                    <small class="text-light px-3 mb-2">Instagram & Verification</small>

                    <a class="nav-link" href="instagram_verifications.php">
                        <i class="fab fa-instagram"></i>Instagram Verifications
                    </a>
                    <a class="nav-link" href="influencer_follow_check.php">
                        <i class="fas fa-user-check"></i>Follow Status Check
                    </a>
                    <a class="nav-link" href="instagram_blue_check.php">
                        <i class="fas fa-check-circle"></i>Blue Check Management
                    </a>
                    <a class="nav-link" href="user_verification_status.php">
                        <i class="fas fa-clipboard-check"></i>User Follow Status
                    </a>
                    <a class="nav-link" href="suspended_users.php">
                        <i class="fas fa-ban"></i>Suspended Users
                    </a>

                    <!-- Badge Management Section -->
                    <hr class="my-2 text-light">
                    <small class="text-light px-3 mb-2">Badge Management</small>

                    <a class="nav-link" href="badge_management.php">
                        <i class="fas fa-award"></i>Badge Management
                    </a>
                    <a class="nav-link" href="user_badge_assignment.php">
                        <i class="fas fa-user-tag"></i>Assign Badges
                    </a>

                    <!-- System Section -->
                    <hr class="my-2 text-light">
                    <small class="text-light px-3 mb-2">System</small>

                    <a class="nav-link" href="settings.php">
                        <i class="fas fa-cog"></i>Settings
                    </a>
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10 main-content" style="margin-left: auto;">
            <!-- Top Bar -->
            <div class="bg-white shadow-sm p-3 mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">User Management</h4>
                        <small class="text-muted">Manage all platform users</small>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-outline-primary d-lg-none" onclick="toggleSidebar()">
                            <i class="fas fa-bars"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="p-3">
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['total_users']; ?></h3>
                                <p class="mb-0">Total Users</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card success">
                            <div class="card-body text-center">
                                <i class="fas fa-check-circle fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['active_users']; ?></h3>
                                <p class="mb-0">Active Users</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card warning">
                            <div class="card-body text-center">
                                <i class="fas fa-pause-circle fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['inactive_users']; ?></h3>
                                <p class="mb-0">Inactive Users</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card info">
                            <div class="card-body text-center">
                                <i class="fas fa-rupee-sign fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo format_currency($stats['total_paid'] ?? 0); ?></h3>
                                <p class="mb-0">Total Paid</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-filter me-2"></i>Filter Users
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="<?php echo htmlspecialchars($search); ?>" 
                                       placeholder="Username, name, or email...">
                            </div>
                            
                            <div class="col-md-2">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">All Status</option>
                                    <option value="active" <?php echo $status === 'active' ? 'selected' : ''; ?>>Active</option>
                                    <option value="inactive" <?php echo $status === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                                    <option value="suspended" <?php echo $status === 'suspended' ? 'selected' : ''; ?>>Suspended</option>
                                </select>
                            </div>
                            
                            <div class="col-md-2">
                                <label for="sort" class="form-label">Sort By</label>
                                <select class="form-select" id="sort" name="sort">
                                    <option value="created_at" <?php echo $sort === 'created_at' ? 'selected' : ''; ?>>Join Date</option>
                                    <option value="full_name" <?php echo $sort === 'full_name' ? 'selected' : ''; ?>>Name</option>
                                    <option value="total_earned" <?php echo $sort === 'total_earned' ? 'selected' : ''; ?>>Earnings</option>
                                    <option value="username" <?php echo $sort === 'username' ? 'selected' : ''; ?>>Username</option>
                                </select>
                            </div>
                            
                            <div class="col-md-2">
                                <label for="order" class="form-label">Order</label>
                                <select class="form-select" id="order" name="order">
                                    <option value="DESC" <?php echo $order === 'DESC' ? 'selected' : ''; ?>>Descending</option>
                                    <option value="ASC" <?php echo $order === 'ASC' ? 'selected' : ''; ?>>Ascending</option>
                                </select>
                            </div>
                            
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                <a href="users.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Users Table -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>All Users (<?php echo count($users); ?>)
                        </h5>
                        <div>
                            <button class="btn btn-success btn-sm" onclick="exportUsers()">
                                <i class="fas fa-download me-1"></i>Export CSV
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (empty($users)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No users found</h5>
                                <p class="text-muted">No users match your current filters.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>User</th>
                                            <th>Contact</th>
                                            <th>Tasks</th>
                                            <th>Earnings</th>
                                            <th>Status</th>
                                            <th>Joined</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($users as $user): ?>
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <?php if ($user['profile_image']): ?>
                                                            <img src="<?php echo htmlspecialchars($user['profile_image']); ?>" 
                                                                 alt="Profile" class="rounded-circle me-2" 
                                                                 style="width: 40px; height: 40px; object-fit: cover;">
                                                        <?php else: ?>
                                                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-2" 
                                                                 style="width: 40px; height: 40px;">
                                                                <i class="fas fa-user text-white"></i>
                                                            </div>
                                                        <?php endif; ?>
                                                        <div>
                                                            <strong><?php echo htmlspecialchars($user['full_name']); ?></strong>
                                                            <br><small class="text-muted">@<?php echo htmlspecialchars($user['username']); ?></small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div>
                                                        <small class="d-block"><?php echo htmlspecialchars($user['email']); ?></small>
                                                        <small class="text-muted"><?php echo htmlspecialchars($user['phone']); ?></small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="text-center">
                                                        <strong class="text-primary"><?php echo $user['total_tasks']; ?></strong>
                                                        <small class="d-block text-muted">
                                                            <?php echo $user['completed_tasks']; ?> completed
                                                        </small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="text-center">
                                                        <strong class="text-success"><?php echo format_currency($user['total_earned']); ?></strong>
                                                        <small class="d-block text-muted">
                                                            <?php echo format_currency($user['pending_payout']); ?> pending
                                                        </small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <?php
                                                    $status_class = '';
                                                    switch ($user['status']) {
                                                        case 'active':
                                                            $status_class = 'bg-success';
                                                            break;
                                                        case 'inactive':
                                                            $status_class = 'bg-warning';
                                                            break;
                                                        case 'suspended':
                                                            $status_class = 'bg-danger';
                                                            break;
                                                    }
                                                    ?>
                                                    <span class="badge <?php echo $status_class; ?>">
                                                        <?php echo ucfirst($user['status']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <small><?php echo date('M j, Y', strtotime($user['created_at'])); ?></small>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <button class="btn btn-sm btn-outline-primary" 
                                                                data-bs-toggle="modal" 
                                                                data-bs-target="#userModal<?php echo $user['id']; ?>">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-warning" 
                                                                data-bs-toggle="modal" 
                                                                data-bs-target="#statusModal<?php echo $user['id']; ?>">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-danger delete-btn" 
                                                                data-bs-toggle="modal" 
                                                                data-bs-target="#deleteModal<?php echo $user['id']; ?>">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            
                                            <!-- User Details Modal -->
                                            <div class="modal fade" id="userModal<?php echo $user['id']; ?>" tabindex="-1">
                                                <div class="modal-dialog modal-lg">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title">User Details</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <div class="row">
                                                                <div class="col-md-6">
                                                                    <h6 class="fw-bold">Personal Information</h6>
                                                                    <p><strong>Full Name:</strong> <?php echo htmlspecialchars($user['full_name']); ?></p>
                                                                    <p><strong>Username:</strong> @<?php echo htmlspecialchars($user['username']); ?></p>
                                                                    <p><strong>Email:</strong> <?php echo htmlspecialchars($user['email']); ?></p>
                                                                    <p><strong>Phone:</strong> <?php echo htmlspecialchars($user['phone']); ?></p>
                                                                    <p><strong>Instagram:</strong> 
                                                                        <?php if ($user['instagram_handle']): ?>
                                                                            @<?php echo htmlspecialchars($user['instagram_handle']); ?>
                                                                        <?php else: ?>
                                                                            <span class="text-muted">Not provided</span>
                                                                        <?php endif; ?>
                                                                    </p>
                                                                    <p><strong>UPI ID:</strong> 
                                                                        <?php if ($user['upi_id']): ?>
                                                                            <?php echo htmlspecialchars($user['upi_id']); ?>
                                                                        <?php else: ?>
                                                                            <span class="text-muted">Not provided</span>
                                                                        <?php endif; ?>
                                                                    </p>
                                                                </div>
                                                                <div class="col-md-6">
                                                                    <h6 class="fw-bold">Account Statistics</h6>
                                                                    <p><strong>Status:</strong> 
                                                                        <span class="badge <?php echo $status_class; ?>">
                                                                            <?php echo ucfirst($user['status']); ?>
                                                                        </span>
                                                                    </p>
                                                                    <p><strong>Total Tasks:</strong> <?php echo $user['total_tasks']; ?></p>
                                                                    <p><strong>Completed Tasks:</strong> <?php echo $user['completed_tasks']; ?></p>
                                                                    <p><strong>Total Earned:</strong> <?php echo format_currency($user['total_earned']); ?></p>
                                                                    <p><strong>Pending Payout:</strong> <?php echo format_currency($user['pending_payout']); ?></p>
                                                                    <p><strong>Joined:</strong> <?php echo date('F j, Y', strtotime($user['created_at'])); ?></p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <!-- Status Update Modal -->
                                            <div class="modal fade" id="statusModal<?php echo $user['id']; ?>" tabindex="-1">
                                                <div class="modal-dialog">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title">Update User Status</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                        </div>
                                                        <form method="POST">
                                                            <div class="modal-body">
                                                                <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                                <div class="mb-3">
                                                                    <label for="status<?php echo $user['id']; ?>" class="form-label">Status</label>
                                                                    <select class="form-select" id="status<?php echo $user['id']; ?>" name="status" required>
                                                                        <option value="active" <?php echo $user['status'] === 'active' ? 'selected' : ''; ?>>Active</option>
                                                                        <option value="inactive" <?php echo $user['status'] === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                                                                        <option value="suspended" <?php echo $user['status'] === 'suspended' ? 'selected' : ''; ?>>Suspended</option>
                                                                    </select>
                                                                </div>
                                                            </div>
                                                            <div class="modal-footer">
                                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                                <button type="submit" name="update_status" class="btn btn-primary">Update Status</button>
                                                            </div>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <!-- Delete Modal -->
                                            <div class="modal fade" id="deleteModal<?php echo $user['id']; ?>" tabindex="-1">
                                                <div class="modal-dialog">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title">Delete User</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <p>Are you sure you want to delete user <strong><?php echo htmlspecialchars($user['full_name']); ?></strong>?</p>
                                                            <p class="text-danger"><strong>Warning:</strong> This will also delete all their tasks, submissions, and payout records. This action cannot be undone.</p>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                            <form method="POST" class="d-inline">
                                                                <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                                <button type="submit" name="delete_user" class="btn btn-danger">Delete User</button>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportUsers() {
    window.location.href = 'export.php?type=users';
}
</script>

<?php include '../includes/footer.php'; ?>
