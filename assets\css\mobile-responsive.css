/* ========================================
   REALEARNERS MOBILE RESPONSIVE FRAMEWORK
   Complete A-Z Mobile Optimization
   ======================================== */

/* ===== MOBILE-FIRST RESPONSIVE BREAKPOINTS ===== */
:root {
    --mobile-xs: 320px;
    --mobile-sm: 375px;
    --mobile-md: 414px;
    --mobile-lg: 480px;
    --tablet-sm: 768px;
    --tablet-lg: 1024px;
    --desktop-sm: 1200px;
    --desktop-lg: 1440px;
    
    /* Mobile-optimized spacing */
    --mobile-padding: 1rem;
    --mobile-margin: 0.75rem;
    --mobile-gap: 0.5rem;
    
    /* Touch-friendly sizes */
    --touch-target: 44px;
    --mobile-font-base: 16px;
    --mobile-line-height: 1.5;
}

/* ===== DESKTOP-FIRST RESPONSIVE OPTIMIZATIONS ===== */

/* Only apply global changes for mobile devices */
@media (max-width: 1023px) {
    * {
        box-sizing: border-box;
    }

    html {
        font-size: var(--mobile-font-base);
        line-height: var(--mobile-line-height);
        -webkit-text-size-adjust: 100%;
        -webkit-tap-highlight-color: transparent;
    }

    body {
        overflow-x: hidden;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}

/* Desktop - preserve original Bootstrap behavior */
@media (min-width: 1024px) {
    /* Reset any mobile overrides for desktop */
    .container-fluid {
        padding-left: 15px;
        padding-right: 15px;
    }

    .row {
        margin-left: -15px;
        margin-right: -15px;
    }

    [class*="col-"] {
        padding-left: 15px;
        padding-right: 15px;
    }

    /* Ensure original desktop spacing */
    .main-content {
        padding: 0;
    }

    .p-3 {
        padding: 1rem !important;
    }

    .mb-4 {
        margin-bottom: 1.5rem !important;
    }

    .card-body {
        padding: 1.25rem;
    }
}

/* ===== PRESERVE AUTH PAGES LAYOUT ===== */

/* Login, Register, and other auth pages should not be affected */
body:not(.has-sidebar) .container-fluid,
.auth-page .container-fluid {
    padding-left: 15px !important;
    padding-right: 15px !important;
}

body:not(.has-sidebar) .row,
.auth-page .row {
    margin-left: -15px !important;
    margin-right: -15px !important;
}

body:not(.has-sidebar) [class*="col-"],
.auth-page [class*="col-"] {
    padding-left: 15px !important;
    padding-right: 15px !important;
}

/* Ensure auth pages work properly on all devices */
.auth-page .card {
    margin-bottom: 1.5rem;
}

.auth-page .card-body {
    padding: 2rem;
}

@media (max-width: 767px) {
    .auth-page .card-body {
        padding: 1.5rem;
    }

    .auth-page .col-lg-6 {
        padding: 1rem;
    }
}

/* ===== RESPONSIVE LAYOUT SYSTEM ===== */

/* ===== DESKTOP LAYOUT - PRESERVE ORIGINAL ===== */

/* Desktop - keep original Bootstrap behavior */
@media (min-width: 1024px) {
    .container-fluid {
        padding-left: 15px;
        padding-right: 15px;
        margin: 0;
    }

    .row {
        margin-left: -15px;
        margin-right: -15px;
        display: flex;
        flex-wrap: wrap;
    }

    /* Desktop sidebar - original behavior */
    .sidebar {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        position: relative;
        padding-left: 15px;
        padding-right: 15px;
    }

    /* Desktop main content - original behavior */
    .main-content {
        min-height: 100vh;
        padding-left: 15px;
        padding-right: 15px;
    }

    /* Ensure Bootstrap columns work properly */
    .col-lg-3,
    .col-xl-2 {
        flex: 0 0 auto;
        width: auto;
    }

    .col-lg-9,
    .col-xl-10 {
        flex: 0 0 auto;
        width: auto;
    }
}

/* Mobile Navigation Toggle - only show on mobile */
.mobile-nav-toggle {
    display: none;
    position: fixed;
    top: 15px;
    left: 15px;
    z-index: 1060;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 8px;
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    cursor: pointer;
}

.mobile-nav-toggle:hover {
    background: #5a67d8;
}

/* Mobile sidebar overlay */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1040;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    display: none;
}

.sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
    display: block;
}

/* ===== MOBILE AND TABLET LAYOUT ===== */

/* Mobile and Tablet Navigation - All devices under 1024px with sidebar */
@media (max-width: 1023px) {
    /* Only apply to pages with sidebar */
    .has-sidebar .container-fluid {
        padding: 0 !important;
        margin: 0 !important;
    }

    .has-sidebar .row {
        display: block !important;
        margin: 0 !important;
    }

    .has-sidebar [class*="col-"] {
        padding: 0 !important;
        width: 100% !important;
        max-width: 100% !important;
        flex: none !important;
    }

    /* Mobile navigation toggle - show on mobile for sidebar pages */
    .mobile-nav-toggle {
        display: none !important;
    }

    .has-sidebar .mobile-nav-toggle {
        display: flex !important;
        align-items: center;
        justify-content: center;
        position: fixed;
        top: 15px;
        left: 15px;
        z-index: 1060;
        background: #667eea;
        color: white;
        border: none;
        border-radius: 8px;
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        cursor: pointer;
        transition: all 0.2s ease;
        opacity: 1;
        visibility: visible;
    }

    .has-sidebar .mobile-nav-toggle:hover {
        background: #5a67d8;
        transform: scale(1.05);
    }

    .has-sidebar .mobile-nav-toggle:active {
        transform: scale(0.95);
    }

    /* Mobile sidebar - only for sidebar pages */
    .has-sidebar .sidebar {
        position: fixed !important;
        top: 0 !important;
        left: -100% !important;
        width: 280px !important;
        height: 100vh !important;
        z-index: 1050 !important;
        transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        overflow-y: auto !important;
        -webkit-overflow-scrolling: touch !important;
        box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1) !important;
        transform: translateX(0) !important;
        padding: 0 !important;
    }

    .has-sidebar .sidebar.show {
        left: 0 !important;
    }

    /* Mobile main content - only for sidebar pages */
    .has-sidebar .main-content {
        margin-left: 0 !important;
        width: 100% !important;
        padding-top: 80px !important;
        padding-left: 0 !important;
        padding-right: 0 !important;
    }

    /* Mobile overlay - only for sidebar pages */
    .has-sidebar .sidebar-overlay {
        display: block;
    }
}

/* ===== RESPONSIVE LAYOUT ADJUSTMENTS ===== */

/* ===== DESKTOP LAYOUT - MINIMAL CHANGES ===== */

/* All Desktop Sizes (1024px and up) - Preserve original behavior */
@media (min-width: 1024px) {
    /* Hide mobile elements */
    .mobile-nav-toggle {
        display: none !important;
    }

    .sidebar-overlay {
        display: none !important;
    }

    /* Ensure sidebar is in normal flow */
    .sidebar {
        position: relative !important;
        left: 0 !important;
        height: auto !important;
        min-height: 100vh;
        z-index: auto !important;
        box-shadow: none !important;
        transform: none !important;
        width: auto !important;
    }

    /* Ensure main content is in normal flow */
    .main-content {
        margin-left: 0 !important;
        padding-top: 0 !important;
        width: auto !important;
    }

    /* Let Bootstrap handle the layout naturally */
    .row {
        display: flex;
        flex-wrap: wrap;
        margin-left: -15px;
        margin-right: -15px;
    }

    /* Don't override Bootstrap column behavior */
    .col-lg-3,
    .col-xl-2,
    .col-lg-9,
    .col-xl-10 {
        position: relative;
        width: 100%;
        padding-left: 15px;
        padding-right: 15px;
    }

    /* Let Bootstrap calculate widths */
    .col-lg-3 {
        flex: 0 0 25%;
        max-width: 25%;
    }

    .col-xl-2 {
        flex: 0 0 16.666667%;
        max-width: 16.666667%;
    }

    .col-lg-9 {
        flex: 0 0 75%;
        max-width: 75%;
    }

    .col-xl-10 {
        flex: 0 0 83.333333%;
        max-width: 83.333333%;
    }
}

/* ===== MOBILE CARD OPTIMIZATIONS ===== */
.card {
    margin-bottom: var(--mobile-margin);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-body {
    padding: 1rem;
}

.card-header {
    padding: 0.75rem 1rem;
    font-size: 1rem;
    font-weight: 600;
}

/* ===== MOBILE FORM OPTIMIZATIONS ===== */
.form-control,
.form-select,
.btn {
    min-height: var(--touch-target);
    font-size: 16px; /* Prevents zoom on iOS */
    border-radius: 8px;
}

.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.input-group {
    margin-bottom: 1rem;
}

/* ===== MOBILE TABLE OPTIMIZATIONS ===== */
.table-responsive {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table {
    margin-bottom: 0;
    font-size: 0.875rem;
}

.table th,
.table td {
    padding: 0.75rem 0.5rem;
    vertical-align: middle;
    border-color: #e5e7eb;
}

/* Mobile-specific table layout */
.mobile-table {
    display: none;
}

/* ===== MOBILE BUTTON OPTIMIZATIONS ===== */
.btn {
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    transition: all 0.2s ease;
    min-height: var(--touch-target);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-group-mobile {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    width: 100%;
}

.btn-group-mobile .btn {
    width: 100%;
    justify-content: center;
}

/* ===== MOBILE MODAL OPTIMIZATIONS ===== */
.modal-dialog {
    margin: 1rem;
    max-width: calc(100% - 2rem);
}

.modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.modal-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e5e7eb;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e5e7eb;
    gap: 0.5rem;
}

/* ===== MOBILE ALERT OPTIMIZATIONS ===== */
.alert {
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    border: none;
    font-size: 0.9rem;
}

.alert-dismissible .btn-close {
    padding: 1rem;
}

/* ===== MOBILE BADGE OPTIMIZATIONS ===== */
.badge-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    margin-top: 0.5rem;
}

.badge-custom {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 6px;
    white-space: nowrap;
}

.badge-custom.badge-sm {
    font-size: 0.625rem;
    padding: 0.25rem 0.5rem;
}

/* ===== MOBILE STATS CARD OPTIMIZATIONS ===== */
.stats-card {
    text-align: center;
    padding: 1.5rem 1rem;
    border-radius: 12px;
    margin-bottom: 1rem;
    color: white;
}

.stats-card h3 {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stats-card p {
    font-size: 0.875rem;
    opacity: 0.9;
    margin-bottom: 0;
}

.stats-card i {
    font-size: 2rem;
    opacity: 0.8;
    margin-bottom: 1rem;
}

/* ===== MOBILE UTILITY CLASSES ===== */
.mobile-only {
    display: none;
}

.desktop-only {
    display: block;
}

.mobile-center {
    text-align: center;
}

.mobile-full-width {
    width: 100%;
}

.mobile-hidden {
    display: none;
}

.mobile-padding {
    padding: var(--mobile-padding);
}

.mobile-margin {
    margin: var(--mobile-margin);
}

.mobile-gap {
    gap: var(--mobile-gap);
}

/* Touch-friendly spacing */
.touch-spacing {
    margin: 0.75rem 0;
}

.touch-padding {
    padding: 0.75rem;
}

/* ===== BETTER RESPONSIVE BREAKPOINTS ===== */

/* ===== MOBILE RESPONSIVE STYLES ===== */

/* Large Mobile/Small Tablet (480px - 767px) - Large phones in landscape */
@media (min-width: 480px) and (max-width: 767px) {
    .has-sidebar .mobile-nav-toggle {
        display: flex !important;
        opacity: 1 !important;
        visibility: visible !important;
    }

    .sidebar {
        position: fixed !important;
        left: -100% !important;
        width: 300px !important;
        height: 100vh !important;
        z-index: 1050 !important;
    }

    .main-content {
        margin-left: 0 !important;
        padding: 80px 1.25rem 1.25rem 1.25rem !important;
        width: 100% !important;
    }

    .container-fluid {
        padding: 0 !important;
    }

    .row {
        display: block !important;
    }

    .card-body {
        padding: 1.25rem;
    }

    .btn-group {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .btn-group .btn {
        flex: 1;
        min-width: calc(50% - 0.25rem);
    }
}

/* Standard Mobile (375px - 479px) - iPhone, standard Android */
@media (min-width: 375px) and (max-width: 479px) {
    .has-sidebar .mobile-nav-toggle {
        display: flex !important;
        width: 48px;
        height: 48px;
        opacity: 1 !important;
        visibility: visible !important;
    }

    .sidebar {
        position: fixed !important;
        left: -100% !important;
        width: 280px !important;
        height: 100vh !important;
        z-index: 1050 !important;
    }

    .main-content {
        margin-left: 0 !important;
        padding: 75px 1rem 1rem 1rem !important;
        width: 100% !important;
    }

    .container-fluid {
        padding: 0 !important;
    }

    .row {
        display: block !important;
    }

    .card {
        margin-bottom: 1rem;
        border-radius: 12px;
    }

    .card-body {
        padding: 1rem;
    }

    .btn {
        min-height: 44px;
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }

    .btn-group {
        flex-direction: column;
        width: 100%;
        gap: 0.5rem;
    }

    .btn-group .btn {
        width: 100%;
        margin: 0;
    }

    .modal-dialog {
        margin: 1rem;
        max-width: calc(100% - 2rem);
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .table th,
    .table td {
        padding: 0.5rem 0.25rem;
        font-size: 0.8rem;
    }

    .form-control,
    .form-select {
        font-size: 16px; /* Prevent zoom on iOS */
        padding: 0.75rem;
    }
}

/* Small Mobile (320px - 374px) - iPhone SE, small Android */
@media (min-width: 320px) and (max-width: 374px) {
    .mobile-nav-toggle {
        display: flex !important;
        width: 44px;
        height: 44px;
        top: 10px;
        left: 10px;
    }

    .sidebar {
        position: fixed !important;
        left: -100% !important;
        width: 260px !important;
        height: 100vh !important;
        z-index: 1050 !important;
    }

    .main-content {
        margin-left: 0 !important;
        padding: 70px 0.75rem 0.75rem 0.75rem !important;
        width: 100% !important;
    }

    .container-fluid {
        padding: 0 !important;
    }

    .row {
        display: block !important;
    }

    .card {
        margin-bottom: 0.75rem;
        border-radius: 10px;
    }

    .card-body {
        padding: 0.75rem;
    }

    .btn {
        min-height: 44px;
        padding: 0.625rem 0.75rem;
        font-size: 0.875rem;
    }

    .btn-group {
        flex-direction: column;
        width: 100%;
        gap: 0.25rem;
    }

    .modal-dialog {
        margin: 0.5rem;
        max-width: calc(100% - 1rem);
    }

    .table th,
    .table td {
        padding: 0.375rem 0.25rem;
        font-size: 0.75rem;
    }

    .form-control,
    .form-select {
        font-size: 16px;
        padding: 0.625rem;
    }

    .sidebar .nav-link {
        padding: 0.875rem 1rem;
        font-size: 0.9rem;
    }

    .sidebar .nav-link i {
        width: 20px;
        margin-right: 10px;
        font-size: 1rem;
    }
}

/* Extra Small Mobile (up to 319px) - Very small devices */
@media (max-width: 319px) {
    .mobile-nav-toggle {
        display: flex !important;
        width: 40px;
        height: 40px;
        top: 8px;
        left: 8px;
        font-size: 1rem;
    }

    .sidebar {
        position: fixed !important;
        left: -100% !important;
        width: 240px !important;
        height: 100vh !important;
        z-index: 1050 !important;
    }

    .main-content {
        margin-left: 0 !important;
        padding: 65px 0.5rem 0.5rem 0.5rem !important;
        width: 100% !important;
    }

    .container-fluid {
        padding: 0 !important;
    }

    .card-body {
        padding: 0.5rem;
    }

    .btn {
        min-height: 40px;
        padding: 0.5rem;
        font-size: 0.8rem;
    }

    .form-control,
    .form-select {
        font-size: 16px;
        padding: 0.5rem;
    }
}

/* ===== TABLET RESPONSIVE STYLES ===== */

/* Large Tablets/iPad Pro (1024px - 1199px in landscape) - Hybrid experience */
@media (min-width: 1024px) and (max-width: 1199px) and (orientation: landscape) {
    /* Use desktop layout but more compact */
    .sidebar {
        flex: 0 0 240px;
        width: 240px;
    }

    .main-content {
        padding: 1rem;
    }
}

/* Standard Tablets (768px - 1023px) - Mobile navigation with tablet spacing */
@media (min-width: 768px) and (max-width: 1023px) {
    .mobile-nav-toggle {
        display: flex !important;
        align-items: center;
        justify-content: center;
    }

    .sidebar {
        position: fixed !important;
        left: -100% !important;
        width: 320px !important;
        height: 100vh !important;
        z-index: 1050 !important;
        box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1) !important;
        transform: translateX(0) !important;
        transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }

    .sidebar.show {
        left: 0 !important;
    }

    .main-content {
        margin-left: 0 !important;
        padding-top: 80px !important;
        width: 100% !important;
        padding: 80px 1.5rem 1.5rem 1.5rem !important;
    }

    .container-fluid {
        padding: 0 !important;
    }

    .row {
        display: block !important;
    }

    .col-lg-3,
    .col-xl-2,
    .col-lg-9,
    .col-xl-10 {
        flex: none !important;
        width: 100% !important;
        max-width: 100% !important;
    }

    /* Tablet-specific improvements */
    .card {
        margin-bottom: 1.5rem;
    }

    .card-body {
        padding: 1.5rem;
    }

    .btn {
        min-height: 48px;
        padding: 0.75rem 1.5rem;
    }

    .btn-group {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .btn-group .btn {
        flex: 1;
        min-width: calc(50% - 0.25rem);
    }

    .modal-dialog {
        max-width: 85%;
        margin: 2rem auto;
    }

    .table th,
    .table td {
        padding: 0.875rem;
        font-size: 0.9rem;
    }

    /* Tablet navigation improvements */
    .sidebar .nav-link {
        padding: 1.25rem 1.5rem;
        font-size: 1.1rem;
    }

    .sidebar .nav-link i {
        width: 28px;
        margin-right: 15px;
        font-size: 1.2rem;
    }
}

/* ===== DESKTOP RESPONSIVE STYLES ===== */

/* Desktop (1024px and up) - Preserve original desktop experience */
@media (min-width: 1024px) {
    .mobile-nav-toggle {
        display: none !important;
    }

    .sidebar {
        position: relative !important;
        left: 0 !important;
        width: auto !important;
        height: auto !important;
        min-height: 100vh;
        z-index: auto !important;
        box-shadow: none !important;
    }

    .sidebar-overlay {
        display: none !important;
    }

    .main-content {
        padding-top: 0 !important;
    }

    /* Restore original desktop layout */
    .container-fluid {
        padding-left: 0;
        padding-right: 0;
    }
}

/* Large Desktop optimizations */
@media (min-width: 1200px) {
    .modal-dialog {
        max-width: 70%;
    }
}

@media (min-width: 1440px) {
    .modal-dialog {
        max-width: 60%;
    }
}

/* ===== MOBILE-SPECIFIC COMPONENT STYLES ===== */

/* Mobile Navigation Enhancements */
@media (max-width: 1023px) {
    .mobile-only {
        display: block !important;
    }

    .desktop-only {
        display: none !important;
    }

    /* Mobile-optimized sidebar navigation */
    .sidebar .nav-link {
        padding: 1rem 1.5rem;
        font-size: 1rem;
        border-radius: 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        color: rgba(255, 255, 255, 0.9);
        transition: all 0.2s ease;
    }

    .sidebar .nav-link:hover,
    .sidebar .nav-link.active {
        background: rgba(255, 255, 255, 0.1);
        color: white;
    }

    .sidebar .nav-link i {
        width: 24px;
        margin-right: 12px;
        font-size: 1.1rem;
    }

    /* Mobile card improvements */
    .card {
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        margin-bottom: 1rem;
    }

    /* Mobile form improvements */
    .form-control,
    .form-select {
        border-radius: 8px;
        border: 1px solid #d1d5db;
        padding: 0.75rem;
    }

    .form-control:focus,
    .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    /* Mobile button improvements */
    .btn {
        border-radius: 8px;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        transition: all 0.2s ease;
    }

    .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    /* Mobile dropdown improvements */
    .dropdown-menu {
        border-radius: 12px;
        border: none;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        padding: 0.5rem;
    }

    .dropdown-item {
        padding: 0.75rem 1rem;
        border-radius: 8px;
        margin-bottom: 0.25rem;
        transition: all 0.2s ease;
    }

    .dropdown-item:hover {
        background: #f3f4f6;
        transform: translateX(4px);
    }

    /* Mobile alert improvements */
    .alert {
        border-radius: 12px;
        border: none;
        padding: 1rem 1.25rem;
        margin-bottom: 1rem;
    }

    /* Mobile badge improvements */
    .badge {
        border-radius: 6px;
        padding: 0.375rem 0.75rem;
        font-size: 0.75rem;
        font-weight: 500;
    }

    /* Mobile table improvements */
    .table-responsive {
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .table {
        margin-bottom: 0;
    }

    .table th {
        background: #f8f9fa;
        border-bottom: 2px solid #dee2e6;
        font-weight: 600;
        color: #495057;
    }
}

/* ===== MOBILE TABLE ALTERNATIVE LAYOUTS ===== */
@media (max-width: 767px) {
    .mobile-table {
        display: block;
    }

    .mobile-table .table {
        display: block;
        width: 100%;
        overflow-x: auto;
        white-space: nowrap;
    }

    /* Card-based table layout for mobile */
    .table-card-mobile {
        display: block;
    }

    .table-card-mobile .table {
        display: none;
    }

    .table-card-mobile .mobile-card {
        background: white;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 0.75rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
    }

    .table-card-mobile .mobile-card-header {
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid #e5e7eb;
    }

    .table-card-mobile .mobile-card-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.25rem 0;
        border-bottom: 1px solid #f3f4f6;
    }

    .table-card-mobile .mobile-card-row:last-child {
        border-bottom: none;
        padding-bottom: 0;
    }

    .table-card-mobile .mobile-card-label {
        font-weight: 500;
        color: #6b7280;
        font-size: 0.875rem;
    }

    .table-card-mobile .mobile-card-value {
        font-weight: 500;
        text-align: right;
    }
}

/* ===== MOBILE PERFORMANCE OPTIMIZATIONS ===== */
@media (max-width: 767px) {
    /* Reduce animations on mobile for better performance */
    .card:hover {
        transform: none;
    }

    .instagram-card:hover {
        transform: none;
    }

    /* Optimize images for mobile */
    img {
        max-width: 100%;
        height: auto;
    }

    /* Reduce box shadows on mobile */
    .card,
    .modal-content,
    .dropdown-menu {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    /* Optimize fonts for mobile */
    h1 { font-size: 1.75rem; }
    h2 { font-size: 1.5rem; }
    h3 { font-size: 1.25rem; }
    h4 { font-size: 1.125rem; }
    h5 { font-size: 1rem; }
    h6 { font-size: 0.875rem; }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .card {
        border: 2px solid #000;
    }

    .btn {
        border: 2px solid currentColor;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-color: #1a1a1a;
        --text-color: #ffffff;
        --card-bg: #2d2d2d;
    }

    body {
        background-color: var(--bg-color);
        color: var(--text-color);
    }

    .card {
        background-color: var(--card-bg);
        border-color: #404040;
    }
}
