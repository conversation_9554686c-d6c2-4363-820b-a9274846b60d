/**
 * RealEarners Mobile Responsive JavaScript
 * Complete mobile optimization and interaction handling
 */

class MobileResponsive {
    constructor() {
        this.isMobile = window.innerWidth <= 767;
        this.isTablet = window.innerWidth >= 768 && window.innerWidth <= 1023;
        this.isDesktop = window.innerWidth >= 1024;
        this.sidebarOpen = false;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.setupMobileNavigation();
        this.setupTouchOptimizations();
        this.setupResponsiveImages();
        this.setupMobileTables();
        this.setupMobileModals();
        this.setupSwipeGestures();
        this.optimizeForMobile();
    }
    
    setupEventListeners() {
        // Window resize handler
        window.addEventListener('resize', this.debounce(() => {
            this.handleResize();
        }, 250));
        
        // Orientation change handler
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.handleResize();
            }, 100);
        });
        
        // Touch event optimizations
        document.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: true });
        document.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: false });
        document.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: true });
    }
    
    setupMobileNavigation() {
        // Create mobile navigation toggle if it doesn't exist
        if (!document.querySelector('.mobile-nav-toggle')) {
            this.createMobileNavToggle();
        }
        
        // Setup sidebar functionality
        const sidebar = document.querySelector('.sidebar');
        const overlay = this.createSidebarOverlay();
        
        if (sidebar) {
            // Mobile nav toggle click
            document.addEventListener('click', (e) => {
                if (e.target.closest('.mobile-nav-toggle')) {
                    this.toggleSidebar();
                }
                
                if (e.target.closest('.sidebar-overlay')) {
                    this.closeSidebar();
                }
            });
            
            // Close sidebar when clicking nav links on mobile
            sidebar.addEventListener('click', (e) => {
                if (e.target.closest('.nav-link') && this.isMobile) {
                    this.closeSidebar();
                }
            });
        }
    }
    
    createMobileNavToggle() {
        const toggle = document.createElement('button');
        toggle.className = 'mobile-nav-toggle btn';
        toggle.innerHTML = '<i class="fas fa-bars"></i>';
        toggle.setAttribute('aria-label', 'Toggle navigation');
        document.body.appendChild(toggle);
    }
    
    createSidebarOverlay() {
        let overlay = document.querySelector('.sidebar-overlay');
        if (!overlay) {
            overlay = document.createElement('div');
            overlay.className = 'sidebar-overlay';
            document.body.appendChild(overlay);
        }
        return overlay;
    }
    
    toggleSidebar() {
        const sidebar = document.querySelector('.sidebar');
        const overlay = document.querySelector('.sidebar-overlay');
        
        if (sidebar && overlay) {
            this.sidebarOpen = !this.sidebarOpen;
            
            if (this.sidebarOpen) {
                sidebar.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                sidebar.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
    }
    
    closeSidebar() {
        const sidebar = document.querySelector('.sidebar');
        const overlay = document.querySelector('.sidebar-overlay');
        
        if (sidebar && overlay) {
            this.sidebarOpen = false;
            sidebar.classList.remove('show');
            overlay.classList.remove('show');
            document.body.style.overflow = '';
        }
    }
    
    setupTouchOptimizations() {
        // Add touch-friendly classes to interactive elements
        const interactiveElements = document.querySelectorAll('button, .btn, .nav-link, .dropdown-item, .page-link');
        
        interactiveElements.forEach(element => {
            element.style.minHeight = '44px';
            element.style.minWidth = '44px';
            
            // Add touch feedback
            element.addEventListener('touchstart', function() {
                this.style.opacity = '0.7';
            }, { passive: true });
            
            element.addEventListener('touchend', function() {
                setTimeout(() => {
                    this.style.opacity = '';
                }, 150);
            }, { passive: true });
        });
    }
    
    setupResponsiveImages() {
        const images = document.querySelectorAll('img');
        
        images.forEach(img => {
            // Add responsive classes
            if (!img.classList.contains('img-fluid')) {
                img.classList.add('img-fluid');
            }
            
            // Lazy loading for mobile
            if (this.isMobile && 'loading' in HTMLImageElement.prototype) {
                img.loading = 'lazy';
            }
        });
    }
    
    setupMobileTables() {
        const tables = document.querySelectorAll('.table');
        
        tables.forEach(table => {
            if (this.isMobile) {
                this.convertTableToCards(table);
            }
        });
    }
    
    convertTableToCards(table) {
        const tableContainer = table.closest('.table-responsive');
        if (!tableContainer || tableContainer.querySelector('.mobile-cards')) return;
        
        const headers = Array.from(table.querySelectorAll('thead th')).map(th => th.textContent.trim());
        const rows = Array.from(table.querySelectorAll('tbody tr'));
        
        const mobileCards = document.createElement('div');
        mobileCards.className = 'mobile-cards d-block d-md-none';
        
        rows.forEach(row => {
            const cells = Array.from(row.querySelectorAll('td'));
            const card = document.createElement('div');
            card.className = 'mobile-card';
            
            cells.forEach((cell, index) => {
                if (headers[index]) {
                    const cardRow = document.createElement('div');
                    cardRow.className = 'mobile-card-row';
                    
                    const label = document.createElement('div');
                    label.className = 'mobile-card-label';
                    label.textContent = headers[index];
                    
                    const value = document.createElement('div');
                    value.className = 'mobile-card-value';
                    value.innerHTML = cell.innerHTML;
                    
                    cardRow.appendChild(label);
                    cardRow.appendChild(value);
                    card.appendChild(cardRow);
                }
            });
            
            mobileCards.appendChild(card);
        });
        
        // Hide original table on mobile
        table.classList.add('d-none', 'd-md-table');
        tableContainer.appendChild(mobileCards);
    }
    
    setupMobileModals() {
        const modals = document.querySelectorAll('.modal');
        
        modals.forEach(modal => {
            modal.addEventListener('show.bs.modal', () => {
                if (this.isMobile) {
                    document.body.style.overflow = 'hidden';
                    modal.style.paddingRight = '0';
                }
            });
            
            modal.addEventListener('hidden.bs.modal', () => {
                if (this.isMobile) {
                    document.body.style.overflow = '';
                }
            });
        });
    }
    
    setupSwipeGestures() {
        let startX = 0;
        let startY = 0;
        
        document.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        }, { passive: true });
        
        document.addEventListener('touchend', (e) => {
            if (!startX || !startY) return;
            
            const endX = e.changedTouches[0].clientX;
            const endY = e.changedTouches[0].clientY;
            
            const diffX = startX - endX;
            const diffY = startY - endY;
            
            // Horizontal swipe detection
            if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
                if (diffX > 0) {
                    // Swipe left - close sidebar
                    if (this.sidebarOpen) {
                        this.closeSidebar();
                    }
                } else {
                    // Swipe right - open sidebar
                    if (!this.sidebarOpen && startX < 50) {
                        this.toggleSidebar();
                    }
                }
            }
            
            startX = 0;
            startY = 0;
        }, { passive: true });
    }
    
    optimizeForMobile() {
        if (this.isMobile) {
            // Optimize viewport
            this.setViewport();
            
            // Optimize forms
            this.optimizeForms();
            
            // Optimize buttons
            this.optimizeButtons();
            
            // Optimize cards
            this.optimizeCards();
        }
    }
    
    setViewport() {
        let viewport = document.querySelector('meta[name="viewport"]');
        if (!viewport) {
            viewport = document.createElement('meta');
            viewport.name = 'viewport';
            document.head.appendChild(viewport);
        }
        viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
    }
    
    optimizeForms() {
        const inputs = document.querySelectorAll('input, select, textarea');
        
        inputs.forEach(input => {
            // Prevent zoom on iOS
            if (input.type !== 'file') {
                input.style.fontSize = '16px';
            }
            
            // Add mobile-friendly attributes
            if (input.type === 'email') {
                input.setAttribute('autocomplete', 'email');
                input.setAttribute('inputmode', 'email');
            }
            
            if (input.type === 'tel') {
                input.setAttribute('inputmode', 'tel');
            }
            
            if (input.type === 'number') {
                input.setAttribute('inputmode', 'numeric');
            }
        });
    }
    
    optimizeButtons() {
        const buttons = document.querySelectorAll('.btn');
        
        buttons.forEach(button => {
            // Ensure minimum touch target size
            const rect = button.getBoundingClientRect();
            if (rect.height < 44) {
                button.style.minHeight = '44px';
            }
            if (rect.width < 44) {
                button.style.minWidth = '44px';
            }
        });
    }
    
    optimizeCards() {
        const cards = document.querySelectorAll('.card');
        
        cards.forEach(card => {
            // Add mobile-friendly spacing
            card.style.marginBottom = '1rem';
            
            // Optimize card body padding
            const cardBody = card.querySelector('.card-body');
            if (cardBody) {
                cardBody.style.padding = '1rem';
            }
        });
    }
    
    handleResize() {
        const newIsMobile = window.innerWidth <= 767;
        const newIsTablet = window.innerWidth >= 768 && window.innerWidth <= 1023;
        const newIsDesktop = window.innerWidth >= 1024;
        
        // Update device type flags
        this.isMobile = newIsMobile;
        this.isTablet = newIsTablet;
        this.isDesktop = newIsDesktop;
        
        // Close sidebar on desktop
        if (this.isDesktop && this.sidebarOpen) {
            this.closeSidebar();
        }
        
        // Re-optimize for new screen size
        this.optimizeForMobile();
    }
    
    handleTouchStart(e) {
        // Add touch start handling if needed
    }
    
    handleTouchMove(e) {
        // Prevent overscroll on iOS
        if (e.target.closest('.modal-body') || e.target.closest('.sidebar')) {
            return;
        }
        
        // Prevent pull-to-refresh on mobile
        if (window.scrollY === 0 && e.touches[0].clientY > e.touches[0].clientY) {
            e.preventDefault();
        }
    }
    
    handleTouchEnd(e) {
        // Add touch end handling if needed
    }
    
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Global helper functions
window.toggleSidebar = function() {
    if (window.mobileResponsive) {
        window.mobileResponsive.toggleSidebar();
    }
};

window.closeSidebar = function() {
    if (window.mobileResponsive) {
        window.mobileResponsive.closeSidebar();
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.mobileResponsive = new MobileResponsive();
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MobileResponsive;
}
