<?php
require_once '../config.php';
require_once '../includes/instagram_verification.php';

// Prevent caching
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// Require login
if (!is_logged_in()) {
    redirect('login.php');
}

$user_id = get_user_id();
$user_type = get_user_type();
$verification = new InstagramVerification();

// SAFETY CHECK: If user is suspended, redirect to suspension page
if ($verification->isSuspended($user_id, $user_type)) {
    redirect('account_suspended.php');
}

$status = $verification->getVerificationStatus($user_id, $user_type);
$required_accounts = $verification->getRequiredAccounts();

// If user is already verified, redirect to dashboard
if ($status['verified']) {
    redirect('../' . $user_type . '/dashboard.php');
}

$error = '';
$success = '';

// Check if user was recently unsuspended
$was_unsuspended = false;
if (isset($_SESSION['unsuspended']) && $_SESSION['unsuspended'] === true) {
    $was_unsuspended = true;
    $success = 'Great news! Your account has been unsuspended. You can now submit your verification again.';
    unset($_SESSION['unsuspended']);
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['submit_verification'])) {
        $instagram_username = sanitize_input($_POST['instagram_username']);
        
        if (empty($instagram_username)) {
            $error = 'Please enter your Instagram username.';
        } else {
            try {
                $verification->submitVerification($user_id, $user_type, $instagram_username);

                // Set success message in session and redirect to dashboard
                $_SESSION['verification_submitted'] = true;
                $_SESSION['success'] = 'Verification request submitted! You now have access to your dashboard while we verify your follows.';

                // Redirect to dashboard
                redirect('../' . $user_type . '/dashboard.php');
            } catch (Exception $e) {
                $error = 'Failed to submit verification request.';
            }
        }
    }
}

$page_title = 'Instagram Verification Required';
include '../includes/header.php';
?>

<div class="container-fluid vh-100">
    <div class="row h-100">
        <!-- Left Side - Branding -->
        <div class="col-lg-6 d-none d-lg-flex align-items-center justify-content-center" 
             style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="text-center text-white">
                <h1 class="display-4 fw-bold mb-4">
                    <i class="fab fa-instagram me-3"></i>Follow to Continue
                </h1>
                <p class="lead mb-4">Follow our Instagram accounts to access your panel</p>
                
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="card bg-white bg-opacity-20 border-0 backdrop-blur">
                            <div class="card-body p-4">
                                <h5 class="fw-bold text-white mb-3">Required Follows:</h5>
                                
                                <div class="d-flex flex-column gap-3">
                                    <a href="https://instagram.com/thesyedabubakkar" 
                                       target="_blank" 
                                       class="btn btn-light btn-lg">
                                        <i class="fab fa-instagram me-2"></i>@thesyedabubakkar
                                        <small class="d-block">Founder & CEO</small>
                                    </a>
                                    
                                    <a href="https://instagram.com/real_earners.in" 
                                       target="_blank" 
                                       class="btn btn-outline-light btn-lg">
                                        <i class="fab fa-instagram me-2"></i>@real_earners.in
                                        <small class="d-block">Official Brand Account</small>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Right Side - Verification Form -->
        <div class="col-lg-6 d-flex align-items-center justify-content-center">
            <div class="w-100" style="max-width: 500px;">
                <div class="card shadow-lg border-0">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <i class="fab fa-instagram fa-3x text-primary mb-3"></i>
                            <h2 class="fw-bold text-primary">Instagram Verification</h2>
                            <p class="text-muted">Follow our accounts to access your <?php echo ucfirst($user_type); ?> panel</p>
                        </div>
                        
                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                                <?php if ($was_unsuspended): ?>
                                    <hr>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-undo me-2"></i>
                                        <strong>Account Restored:</strong> Your account suspension has been lifted. Please follow both Instagram accounts and submit your username again.
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Verification Required (user is not verified) -->
                            <!-- Verification Required -->
                            <div class="mb-4">
                                <h5 class="fw-bold mb-3">Step 1: Follow Required Accounts</h5>
                                
                                <?php foreach ($required_accounts as $account): ?>
                                <div class="d-flex align-items-center justify-content-between p-3 border rounded mb-2">
                                    <div class="d-flex align-items-center">
                                        <i class="fab fa-instagram fa-lg text-primary me-3"></i>
                                        <div>
                                            <h6 class="mb-0">@<?php echo $account; ?></h6>
                                            <small class="text-muted">
                                                <?php echo $account === 'thesyedabubakkar' ? 'Founder & CEO' : 'Official Brand'; ?>
                                            </small>
                                        </div>
                                    </div>
                                    <div>
                                        <?php if (in_array($account, $status['accounts_verified'])): ?>
                                            <span class="badge bg-success">
                                                <i class="fas fa-check me-1"></i>Verified
                                            </span>
                                        <?php else: ?>
                                            <a href="https://instagram.com/<?php echo $account; ?>" 
                                               target="_blank" 
                                               class="btn btn-primary btn-sm">
                                                <i class="fab fa-instagram me-1"></i>Follow
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            
                            <div class="mb-4">
                                <h5 class="fw-bold mb-3">Step 2: Submit Your Instagram Username</h5>
                                <p class="text-muted mb-3">After following both accounts, enter your Instagram username below for verification.</p>

                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>Important:</strong> Once you submit your username, you have <strong>2 days</strong> to follow both required Instagram accounts. If you don't follow both accounts within 2 days, your account will be suspended.
                                </div>
                                
                                <form method="POST" class="needs-validation" novalidate>
                                    <div class="mb-3">
                                        <label for="instagram_username" class="form-label">Your Instagram Username</label>
                                        <div class="input-group">
                                            <span class="input-group-text">@</span>
                                            <input type="text" class="form-control" id="instagram_username" 
                                                   name="instagram_username" 
                                                   placeholder="your_username" 
                                                   required>
                                            <div class="invalid-feedback">Please enter your Instagram username.</div>
                                        </div>
                                        <small class="text-muted">Enter your username without the @ symbol</small>
                                    </div>
                                    
                                    <div class="d-grid">
                                        <button type="submit" name="submit_verification" class="btn btn-primary btn-lg">
                                            <i class="fas fa-paper-plane me-2"></i>Submit for Verification
                                        </button>
                                    </div>
                                </form>
                            </div>
                            
                            <?php if (isset($status['verification_status']) && $status['verification_status'] === 'pending'): ?>
                            <div class="alert alert-info">
                                <i class="fas fa-clock me-2"></i>
                                <strong>Verification Pending:</strong> Our team is reviewing your follows. This usually takes up to 24 hours.
                                <hr>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <small><strong>Important:</strong> You have 2 days from submission to follow both accounts. After 2 days, your account will be suspended if you haven't followed both required accounts.</small>
                                </div>
                            </div>
                            <?php endif; ?>
                        
                        <hr class="my-4">
                        
                        <div class="text-center">
                            <h6 class="fw-bold mb-2">Why do I need to follow?</h6>
                            <p class="text-muted small">
                                Following our Instagram accounts helps you stay updated with the latest campaigns, 
                                tips, and success stories. It's also how we build our community of earners!
                            </p>
                        </div>
                        
                        <div class="text-center mt-3">
                            <a href="../auth/logout.php" class="text-muted text-decoration-none">
                                <i class="fas fa-sign-out-alt me-1"></i>Logout
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<?php include '../includes/footer.php'; ?>
