<?php
require_once '../config.php';

// Redirect if already logged in
if (is_logged_in()) {
    $user_type = get_user_type();
    switch ($user_type) {
        case 'user':
            redirect('../user/dashboard.php');
            break;
        case 'influencer':
            redirect('../influencer/dashboard.php');
            break;
        case 'admin':
            redirect('../admin/dashboard.php');
            break;
        default:
            redirect('../index.php');
    }
}

$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = sanitize_input($_POST['email']);
    $password = $_POST['password'];
    $user_type = sanitize_input($_POST['user_type']);
    
    if (empty($email) || empty($password) || empty($user_type)) {
        $error = 'All fields are required.';
    } else {
        $db = Database::getInstance();
        
        // Determine table based on user type
        $table = '';
        switch ($user_type) {
            case 'user':
                $table = 'users';
                break;
            case 'influencer':
                $table = 'influencers';
                break;
            case 'admin':
                $table = 'admin';
                break;
            case 'brand':
                $table = 'brands';
                break;
            default:
                $error = 'Invalid user type.';
        }
        
        if (empty($error)) {
            $user = $db->fetch("SELECT * FROM {$table} WHERE email = ?", [$email]);
            
            if ($user && password_verify($password, $user['password'])) {
                // Check user status
                if (isset($user['status'])) {
                    if ($user['status'] === 'inactive' || $user['status'] === 'suspended') {
                        $error = 'Your account has been ' . $user['status'] . '. Please contact support.';
                    } elseif ($user_type === 'influencer' && $user['status'] === 'pending') {
                        $error = 'Your influencer account is pending approval. Please wait for admin approval.';
                    } elseif ($user_type === 'influencer' && $user['status'] === 'rejected') {
                        $error = 'Your influencer application has been rejected. Please contact support.';
                    } elseif ($user_type === 'brand' && $user['status'] === 'pending') {
                        $error = 'Your brand account is pending approval. Please wait for admin approval.';
                    } elseif ($user_type === 'brand' && $user['status'] === 'rejected') {
                        $error = 'Your brand application has been rejected. Please contact support.';
                    }
                }
                
                if (empty($error)) {
                    // Set session variables
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['user_type'] = $user_type;
                    $_SESSION['username'] = $user['username'];
                    $_SESSION['full_name'] = $user['full_name'];
                    $_SESSION['email'] = $user['email'];
                    
                    // Update last login for admin and brand
                    if ($user_type === 'admin') {
                        $db->update('admin', ['last_login' => date('Y-m-d H:i:s')], 'id = ?', [$user['id']]);
                    } elseif ($user_type === 'brand') {
                        $db->update('brands', ['last_login' => date('Y-m-d H:i:s')], 'id = ?', [$user['id']]);
                    }
                    
                    $_SESSION['success'] = 'Welcome back, ' . $user['full_name'] . '!';
                    
                    // Redirect based on user type
                    switch ($user_type) {
                        case 'user':
                            redirect('../user/dashboard.php');
                            break;
                        case 'influencer':
                            redirect('../influencer/dashboard.php');
                            break;
                        case 'admin':
                            redirect('../admin/dashboard.php');
                            break;
                        case 'brand':
                            redirect('../brand/dashboard.php');
                            break;
                    }
                }
            } else {
                $error = 'Invalid email or password.';
            }
        }
    }
}

$page_title = 'Login';
include '../includes/header.php';
?>

<div class="container-fluid vh-100">
    <div class="row h-100">
        <!-- Left Side - Branding -->
        <div class="col-lg-6 d-none d-lg-flex align-items-center justify-content-center" 
             style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="text-center text-white">
                <h1 class="display-4 fw-bold mb-4">
                    <i class="fas fa-coins me-3"></i>RealEarners
                </h1>
                <p class="lead mb-4">Earn money by posting ads and promoting brands</p>
                <div class="row text-center">
                    <div class="col-3">
                        <div class="mb-3">
                            <i class="fas fa-users fa-2x mb-2"></i>
                            <h6>For Users</h6>
                            <p class="small">Earn by posting ads</p>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="mb-3">
                            <i class="fas fa-star fa-2x mb-2"></i>
                            <h6>For Influencers</h6>
                            <p class="small">Promote brands</p>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="mb-3">
                            <i class="fas fa-building fa-2x mb-2"></i>
                            <h6>For Brands</h6>
                            <p class="small">Find influencers</p>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="mb-3">
                            <i class="fas fa-crown fa-2x mb-2"></i>
                            <h6>For Admins</h6>
                            <p class="small">Manage platform</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Right Side - Login Form -->
        <div class="col-lg-6 d-flex align-items-center justify-content-center">
            <div class="w-100" style="max-width: 400px;">
                <div class="card shadow-lg border-0">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <h2 class="fw-bold text-primary">Welcome Back</h2>
                            <p class="text-muted">Sign in to your account</p>
                        </div>
                        
                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" class="needs-validation" novalidate>
                            <div class="mb-3">
                                <label for="user_type" class="form-label">Login As</label>
                                <select class="form-select" id="user_type" name="user_type" required>
                                    <option value="">Select Role</option>
                                    <option value="user" <?php echo (isset($_POST['user_type']) && $_POST['user_type'] === 'user') ? 'selected' : ''; ?>>
                                        User (Earn by posting ads)
                                    </option>
                                    <option value="influencer" <?php echo (isset($_POST['user_type']) && $_POST['user_type'] === 'influencer') ? 'selected' : ''; ?>>
                                        Influencer (Promote brands)
                                    </option>
                                    <option value="brand" <?php echo (isset($_POST['user_type']) && $_POST['user_type'] === 'brand') ? 'selected' : ''; ?>>
                                        Brand (Find influencers)
                                    </option>
                                    <option value="admin" <?php echo (isset($_POST['user_type']) && $_POST['user_type'] === 'admin') ? 'selected' : ''; ?>>
                                        Admin (Platform management)
                                    </option>
                                </select>
                                <div class="invalid-feedback">Please select your role.</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">Email Address</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>" 
                                           required>
                                    <div class="invalid-feedback">Please provide a valid email.</div>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <label for="password" class="form-label">Password</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                                        <i class="fas fa-eye" id="toggleIcon"></i>
                                    </button>
                                    <div class="invalid-feedback">Please provide your password.</div>
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>Sign In
                                </button>
                            </div>
                        </form>
                        
                        <hr class="my-4">
                        
                        <div class="text-center">
                            <p class="mb-2">Don't have an account?</p>
                            <a href="register.php" class="btn btn-outline-primary">
                                <i class="fas fa-user-plus me-2"></i>Create Account
                            </a>
                        </div>
                        
                        <div class="text-center mt-3">
                            <a href="../index.php" class="text-muted text-decoration-none">
                                <i class="fas fa-arrow-left me-1"></i>Back to Homepage
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function togglePassword() {
    const passwordField = document.getElementById('password');
    const toggleIcon = document.getElementById('toggleIcon');
    
    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

// Auto-fill demo credentials
document.getElementById('user_type').addEventListener('change', function() {
    const emailField = document.getElementById('email');
    const passwordField = document.getElementById('password');
    
    switch(this.value) {
        case 'admin':
            emailField.value = '<EMAIL>';
            passwordField.value = 'password';
            break;
        case 'brand':
            emailField.value = '<EMAIL>';
            passwordField.value = 'password';
            break;
        default:
            // Don't auto-fill for users and influencers
            break;
    }
});
</script>

<?php include '../includes/footer.php'; ?>
