<?php
require_once '../config.php';

// Redirect if already logged in
if (is_logged_in()) {
    $user_type = get_user_type();
    switch ($user_type) {
        case 'user':
            redirect('../user/dashboard.php');
            break;
        case 'influencer':
            redirect('../influencer/dashboard.php');
            break;
        case 'admin':
            redirect('../admin/dashboard.php');
            break;
        default:
            redirect('../index.php');
    }
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $user_type = sanitize_input($_POST['user_type']);
    $username = sanitize_input($_POST['username']);
    $email = sanitize_input($_POST['email']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    $full_name = sanitize_input($_POST['full_name']);
    $phone = sanitize_input($_POST['phone']);
    $instagram_handle = sanitize_input($_POST['instagram_handle']);
    $upi_id = sanitize_input($_POST['upi_id']);
    
    // Validation
    if (empty($user_type) || empty($username) || empty($email) || empty($password) || 
        empty($confirm_password) || empty($full_name) || empty($phone)) {
        $error = 'All required fields must be filled.';
    } elseif ($password !== $confirm_password) {
        $error = 'Passwords do not match.';
    } elseif (strlen($password) < 6) {
        $error = 'Password must be at least 6 characters long.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Invalid email format.';
    } elseif ($user_type === 'influencer' && empty($instagram_handle)) {
        $error = 'Instagram handle is required for influencers.';
    } else {
        $db = Database::getInstance();
        
        // Determine table based on user type
        $table = '';
        switch ($user_type) {
            case 'user':
                $table = 'users';
                break;
            case 'influencer':
                $table = 'influencers';
                break;
            default:
                $error = 'Invalid user type.';
        }
        
        if (empty($error)) {
            // Check if username or email already exists
            $existing_user = $db->fetch("SELECT id FROM {$table} WHERE username = ? OR email = ?", [$username, $email]);
            
            if ($existing_user) {
                $error = 'Username or email already exists.';
            } else {
                // Prepare data for insertion
                $data = [
                    'username' => $username,
                    'email' => $email,
                    'password' => password_hash($password, PASSWORD_DEFAULT),
                    'full_name' => $full_name,
                    'phone' => $phone,
                    'instagram_handle' => $instagram_handle,
                    'upi_id' => $upi_id
                ];
                
                // Add specific fields for influencers
                if ($user_type === 'influencer') {
                    $data['status'] = 'pending'; // Influencers need approval
                    $data['bio'] = sanitize_input($_POST['bio'] ?? '');
                    $data['category'] = sanitize_input($_POST['category'] ?? '');
                    $data['instagram_followers'] = intval($_POST['instagram_followers'] ?? 0);
                }
                
                try {
                    $user_id = $db->insert($table, $data);
                    
                    if ($user_type === 'user') {
                        // Auto-login for users
                        $_SESSION['user_id'] = $user_id;
                        $_SESSION['user_type'] = $user_type;
                        $_SESSION['username'] = $username;
                        $_SESSION['full_name'] = $full_name;
                        $_SESSION['email'] = $email;
                        
                        $_SESSION['success'] = 'Registration successful! Welcome to RealEarners!';
                        redirect('../user/dashboard.php');
                    } else {
                        // Influencers need approval
                        $success = 'Registration successful! Your influencer account is pending approval. You will be notified once approved.';
                    }
                } catch (Exception $e) {
                    $error = 'Registration failed. Please try again.';
                }
            }
        }
    }
}

$page_title = 'Register';
include '../includes/header.php';
?>

<div class="container-fluid min-vh-100 py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-xl-6">
            <div class="card shadow-lg border-0">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <h2 class="fw-bold text-primary">Join RealEarners</h2>
                        <p class="text-muted">Create your account and start earning</p>
                    </div>
                    
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" class="needs-validation" novalidate id="registerForm">
                        <!-- User Type Selection -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">Choose Your Role</label>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="card h-100 user-type-card" data-type="user">
                                        <div class="card-body text-center">
                                            <i class="fas fa-users fa-3x text-primary mb-3"></i>
                                            <h5>User</h5>
                                            <p class="small text-muted">Earn money by posting simple ads</p>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="user_type" 
                                                       id="user_type_user" value="user" required>
                                                <label class="form-check-label" for="user_type_user">
                                                    Select User
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="card h-100 user-type-card" data-type="influencer">
                                        <div class="card-body text-center">
                                            <i class="fas fa-star fa-3x text-warning mb-3"></i>
                                            <h5>Influencer</h5>
                                            <p class="small text-muted">Promote brands and earn more</p>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="user_type" 
                                                       id="user_type_influencer" value="influencer" required>
                                                <label class="form-check-label" for="user_type_influencer">
                                                    Select Influencer
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Basic Information -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label">Username</label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>" 
                                       required>
                                <div class="invalid-feedback">Please provide a username.</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="full_name" class="form-label">Full Name</label>
                                <input type="text" class="form-control" id="full_name" name="full_name" 
                                       value="<?php echo isset($_POST['full_name']) ? htmlspecialchars($_POST['full_name']) : ''; ?>" 
                                       required>
                                <div class="invalid-feedback">Please provide your full name.</div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email Address</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>" 
                                       required>
                                <div class="invalid-feedback">Please provide a valid email.</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>" 
                                       required>
                                <div class="invalid-feedback">Please provide your phone number.</div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">Password</label>
                                <input type="password" class="form-control" id="password" name="password" 
                                       minlength="6" required>
                                <div class="invalid-feedback">Password must be at least 6 characters.</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="confirm_password" class="form-label">Confirm Password</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                       minlength="6" required>
                                <div class="invalid-feedback">Please confirm your password.</div>
                            </div>
                        </div>
                        
                        <!-- Social Media & Payment -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="instagram_handle" class="form-label">
                                    Instagram Handle 
                                    <span class="influencer-required text-danger" style="display: none;">*</span>
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">@</span>
                                    <input type="text" class="form-control" id="instagram_handle" name="instagram_handle" 
                                           value="<?php echo isset($_POST['instagram_handle']) ? htmlspecialchars($_POST['instagram_handle']) : ''; ?>">
                                </div>
                                <div class="invalid-feedback">Instagram handle is required for influencers.</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="upi_id" class="form-label">UPI ID (for payments)</label>
                                <input type="text" class="form-control" id="upi_id" name="upi_id" 
                                       value="<?php echo isset($_POST['upi_id']) ? htmlspecialchars($_POST['upi_id']) : ''; ?>" 
                                       placeholder="yourname@paytm">
                                <small class="text-muted">Optional: You can add this later</small>
                            </div>
                        </div>
                        
                        <!-- Influencer Specific Fields -->
                        <div id="influencer-fields" style="display: none;">
                            <hr class="my-4">
                            <h5 class="text-primary mb-3">Influencer Information</h5>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="instagram_followers" class="form-label">Instagram Followers</label>
                                    <input type="number" class="form-control" id="instagram_followers" name="instagram_followers" 
                                           value="<?php echo isset($_POST['instagram_followers']) ? $_POST['instagram_followers'] : ''; ?>" 
                                           min="0">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="category" class="form-label">Content Category</label>
                                    <select class="form-select" id="category" name="category">
                                        <option value="">Select Category</option>
                                        <option value="lifestyle">Lifestyle</option>
                                        <option value="fashion">Fashion</option>
                                        <option value="food">Food & Cooking</option>
                                        <option value="fitness">Fitness & Health</option>
                                        <option value="travel">Travel</option>
                                        <option value="tech">Technology</option>
                                        <option value="beauty">Beauty</option>
                                        <option value="education">Education</option>
                                        <option value="entertainment">Entertainment</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="bio" class="form-label">Bio</label>
                                <textarea class="form-control" id="bio" name="bio" rows="3" 
                                          placeholder="Tell us about yourself and your content..."><?php echo isset($_POST['bio']) ? htmlspecialchars($_POST['bio']) : ''; ?></textarea>
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-user-plus me-2"></i>Create Account
                            </button>
                        </div>
                    </form>
                    
                    <hr class="my-4">
                    
                    <div class="text-center">
                        <p class="mb-2">Already have an account?</p>
                        <a href="login.php" class="btn btn-outline-primary">
                            <i class="fas fa-sign-in-alt me-2"></i>Sign In
                        </a>
                    </div>
                    
                    <div class="text-center mt-3">
                        <a href="../index.php" class="text-muted text-decoration-none">
                            <i class="fas fa-arrow-left me-1"></i>Back to Homepage
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Handle user type selection
document.querySelectorAll('input[name="user_type"]').forEach(function(radio) {
    radio.addEventListener('change', function() {
        const influencerFields = document.getElementById('influencer-fields');
        const instagramHandle = document.getElementById('instagram_handle');
        const influencerRequired = document.querySelectorAll('.influencer-required');
        
        if (this.value === 'influencer') {
            influencerFields.style.display = 'block';
            instagramHandle.required = true;
            influencerRequired.forEach(el => el.style.display = 'inline');
        } else {
            influencerFields.style.display = 'none';
            instagramHandle.required = false;
            influencerRequired.forEach(el => el.style.display = 'none');
        }
        
        // Update card styling
        document.querySelectorAll('.user-type-card').forEach(card => {
            card.classList.remove('border-primary');
        });
        document.querySelector(`[data-type="${this.value}"]`).classList.add('border-primary');
    });
});

// Password confirmation validation
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (password !== confirmPassword) {
        this.setCustomValidity('Passwords do not match');
    } else {
        this.setCustomValidity('');
    }
});

// Card click to select user type
document.querySelectorAll('.user-type-card').forEach(function(card) {
    card.addEventListener('click', function() {
        const type = this.dataset.type;
        document.getElementById(`user_type_${type}`).checked = true;
        document.getElementById(`user_type_${type}`).dispatchEvent(new Event('change'));
    });
});
</script>

<?php include '../includes/footer.php'; ?>
