<?php
require_once '../config.php';

// Redirect if already logged in
if (is_logged_in()) {
    $user_type = get_user_type();
    switch ($user_type) {
        case 'user':
            redirect('../user/dashboard.php');
            break;
        case 'influencer':
            redirect('../influencer/dashboard.php');
            break;
        case 'admin':
            redirect('../admin/dashboard.php');
            break;
        case 'brand':
            redirect('../brand/dashboard.php');
            break;
        default:
            redirect('../index.php');
    }
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $user_type = sanitize_input($_POST['user_type']);
    $username = sanitize_input($_POST['username']);
    $email = sanitize_input($_POST['email']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    $full_name = sanitize_input($_POST['full_name']);
    $phone = sanitize_input($_POST['phone']);
    $instagram_handle = sanitize_input($_POST['instagram_handle']);
    $upi_id = sanitize_input($_POST['upi_id']);

    // Brand specific fields
    $company_name = sanitize_input($_POST['company_name'] ?? '');
    $contact_person = sanitize_input($_POST['contact_person'] ?? '');
    $website = sanitize_input($_POST['website'] ?? '');
    $industry = sanitize_input($_POST['industry'] ?? '');
    
    // Validation
    if (empty($user_type) || empty($username) || empty($email) || empty($password) || 
        empty($confirm_password) || empty($full_name) || empty($phone)) {
        $error = 'All required fields must be filled.';
    } elseif ($password !== $confirm_password) {
        $error = 'Passwords do not match.';
    } elseif (strlen($password) < 6) {
        $error = 'Password must be at least 6 characters long.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Invalid email format.';
    } elseif ($user_type === 'influencer' && empty($instagram_handle)) {
        $error = 'Instagram handle is required for influencers.';
    } elseif ($user_type === 'brand' && (empty($company_name) || empty($contact_person))) {
        $error = 'Company name and contact person are required for brands.';
    } else {
        $db = Database::getInstance();
        
        // Determine table based on user type
        $table = '';
        switch ($user_type) {
            case 'user':
                $table = 'users';
                break;
            case 'influencer':
                $table = 'influencers';
                break;
            case 'brand':
                $table = 'brands';
                break;
            default:
                $error = 'Invalid user type.';
        }
        
        if (empty($error)) {
            // Check if username or email already exists
            $existing_user = $db->fetch("SELECT id FROM {$table} WHERE username = ? OR email = ?", [$username, $email]);
            
            if ($existing_user) {
                $error = 'Username or email already exists.';
            } else {
                // Prepare data for insertion
                $data = [
                    'username' => $username,
                    'email' => $email,
                    'password' => password_hash($password, PASSWORD_DEFAULT),
                    'full_name' => $full_name,
                    'phone' => $phone,
                    'instagram_handle' => $instagram_handle,
                    'upi_id' => $upi_id
                ];
                
                // Add specific fields for influencers
                if ($user_type === 'influencer') {
                    $data['status'] = 'pending'; // Influencers need approval
                    $data['bio'] = sanitize_input($_POST['bio'] ?? '');
                    $data['category'] = sanitize_input($_POST['category'] ?? '');
                    $data['instagram_followers'] = intval($_POST['instagram_followers'] ?? 0);
                } elseif ($user_type === 'brand') {
                    $data['company_name'] = $company_name;
                    $data['contact_person'] = $contact_person;
                    $data['website'] = $website;
                    $data['industry'] = $industry;
                    $data['status'] = 'pending'; // Brands need approval
                    $data['description'] = sanitize_input($_POST['description'] ?? '');
                    $data['min_followers'] = intval($_POST['min_followers'] ?? 1000);
                    $data['max_followers'] = intval($_POST['max_followers'] ?? 100000);
                    $data['budget'] = floatval($_POST['budget'] ?? 0);
                }
                
                try {
                    $user_id = $db->insert($table, $data);
                    
                    if ($user_type === 'user') {
                        // Auto-login for users
                        $_SESSION['user_id'] = $user_id;
                        $_SESSION['user_type'] = $user_type;
                        $_SESSION['username'] = $username;
                        $_SESSION['full_name'] = $full_name;
                        $_SESSION['email'] = $email;
                        
                        $_SESSION['success'] = 'Registration successful! Welcome to RealEarners!';
                        redirect('../user/dashboard.php');
                    } elseif ($user_type === 'influencer') {
                        // Influencers need approval
                        $success = 'Registration successful! Your influencer account is pending approval. You will be notified once approved.';
                    } else {
                        // Brands need approval
                        $success = 'Registration successful! Your brand account is pending approval. You will be notified once approved.';
                    }
                } catch (Exception $e) {
                    $error = 'Registration failed. Please try again.';
                }
            }
        }
    }
}

$page_title = 'Register';
include '../includes/header.php';
?>

<div class="container-fluid min-vh-100 py-5 auth-page">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-xl-6">
            <div class="card shadow-lg border-0">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <h2 class="fw-bold text-primary">Join RealEarners</h2>
                        <p class="text-muted">Create your account and start earning</p>
                    </div>
                    
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" class="needs-validation" novalidate id="registerForm">
                        <!-- User Type Selection -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">Choose Your Role</label>
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <div class="card h-100 user-type-card" data-type="user">
                                        <div class="card-body text-center">
                                            <i class="fas fa-users fa-2x text-primary mb-2"></i>
                                            <h6>User</h6>
                                            <p class="small text-muted">Earn money by posting simple ads</p>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="user_type"
                                                       id="user_type_user" value="user" required>
                                                <label class="form-check-label" for="user_type_user">
                                                    Select User
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="card h-100 user-type-card" data-type="influencer">
                                        <div class="card-body text-center">
                                            <i class="fas fa-star fa-2x text-warning mb-2"></i>
                                            <h6>Influencer</h6>
                                            <p class="small text-muted">Promote brands and earn more</p>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="user_type"
                                                       id="user_type_influencer" value="influencer" required>
                                                <label class="form-check-label" for="user_type_influencer">
                                                    Select Influencer
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="card h-100 user-type-card" data-type="brand">
                                        <div class="card-body text-center">
                                            <i class="fas fa-building fa-2x text-info mb-2"></i>
                                            <h6>Brand</h6>
                                            <p class="small text-muted">Find influencers for campaigns</p>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="user_type"
                                                       id="user_type_brand" value="brand" required>
                                                <label class="form-check-label" for="user_type_brand">
                                                    Select Brand
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Basic Information -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label">Username</label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>" 
                                       required>
                                <div class="invalid-feedback">Please provide a username.</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="full_name" class="form-label">Full Name</label>
                                <input type="text" class="form-control" id="full_name" name="full_name" 
                                       value="<?php echo isset($_POST['full_name']) ? htmlspecialchars($_POST['full_name']) : ''; ?>" 
                                       required>
                                <div class="invalid-feedback">Please provide your full name.</div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email Address</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>" 
                                       required>
                                <div class="invalid-feedback">Please provide a valid email.</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>" 
                                       required>
                                <div class="invalid-feedback">Please provide your phone number.</div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">Password</label>
                                <input type="password" class="form-control" id="password" name="password" 
                                       minlength="6" required>
                                <div class="invalid-feedback">Password must be at least 6 characters.</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="confirm_password" class="form-label">Confirm Password</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                       minlength="6" required>
                                <div class="invalid-feedback">Please confirm your password.</div>
                            </div>
                        </div>
                        
                        <!-- Social Media & Payment -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="instagram_handle" class="form-label">
                                    Instagram Handle 
                                    <span class="influencer-required text-danger" style="display: none;">*</span>
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">@</span>
                                    <input type="text" class="form-control" id="instagram_handle" name="instagram_handle" 
                                           value="<?php echo isset($_POST['instagram_handle']) ? htmlspecialchars($_POST['instagram_handle']) : ''; ?>">
                                </div>
                                <div class="invalid-feedback">Instagram handle is required for influencers.</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="upi_id" class="form-label">UPI ID (for payments)</label>
                                <input type="text" class="form-control" id="upi_id" name="upi_id" 
                                       value="<?php echo isset($_POST['upi_id']) ? htmlspecialchars($_POST['upi_id']) : ''; ?>" 
                                       placeholder="yourname@paytm">
                                <small class="text-muted">Optional: You can add this later</small>
                            </div>
                        </div>
                        
                        <!-- Influencer Specific Fields -->
                        <div id="influencer-fields" style="display: none;">
                            <hr class="my-4">
                            <h5 class="text-primary mb-3">Influencer Information</h5>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="instagram_followers" class="form-label">Instagram Followers</label>
                                    <input type="number" class="form-control" id="instagram_followers" name="instagram_followers"
                                           value="<?php echo isset($_POST['instagram_followers']) ? $_POST['instagram_followers'] : ''; ?>"
                                           min="0">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="category" class="form-label">Content Category</label>
                                    <select class="form-select" id="category" name="category">
                                        <option value="">Select Category</option>
                                        <option value="lifestyle">Lifestyle</option>
                                        <option value="fashion">Fashion</option>
                                        <option value="food">Food & Cooking</option>
                                        <option value="fitness">Fitness & Health</option>
                                        <option value="travel">Travel</option>
                                        <option value="tech">Technology</option>
                                        <option value="beauty">Beauty</option>
                                        <option value="education">Education</option>
                                        <option value="entertainment">Entertainment</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="bio" class="form-label">Bio</label>
                                <textarea class="form-control" id="bio" name="bio" rows="3"
                                          placeholder="Tell us about yourself and your content..."><?php echo isset($_POST['bio']) ? htmlspecialchars($_POST['bio']) : ''; ?></textarea>
                            </div>
                        </div>

                        <!-- Brand Specific Fields -->
                        <div id="brand-fields" style="display: none;">
                            <hr class="my-4">
                            <h5 class="text-info mb-3">Brand Information</h5>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="company_name" class="form-label">Company Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="company_name" name="company_name"
                                           value="<?php echo isset($_POST['company_name']) ? htmlspecialchars($_POST['company_name']) : ''; ?>">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="contact_person" class="form-label">Contact Person <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="contact_person" name="contact_person"
                                           value="<?php echo isset($_POST['contact_person']) ? htmlspecialchars($_POST['contact_person']) : ''; ?>">
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="website" class="form-label">Website</label>
                                    <input type="url" class="form-control" id="website" name="website"
                                           value="<?php echo isset($_POST['website']) ? htmlspecialchars($_POST['website']) : ''; ?>"
                                           placeholder="https://yourcompany.com">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="industry" class="form-label">Industry</label>
                                    <select class="form-select" id="industry" name="industry">
                                        <option value="">Select Industry</option>
                                        <option value="Technology">Technology</option>
                                        <option value="Fashion">Fashion</option>
                                        <option value="Beauty">Beauty</option>
                                        <option value="Food">Food & Beverage</option>
                                        <option value="Travel">Travel</option>
                                        <option value="Fitness">Fitness</option>
                                        <option value="Gaming">Gaming</option>
                                        <option value="Education">Education</option>
                                        <option value="Finance">Finance</option>
                                        <option value="Other">Other</option>
                                    </select>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="budget" class="form-label">Monthly Budget (₹)</label>
                                    <input type="number" class="form-control" id="budget" name="budget"
                                           value="<?php echo isset($_POST['budget']) ? $_POST['budget'] : ''; ?>"
                                           min="0" step="1000" placeholder="50000">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="min_followers" class="form-label">Min Followers</label>
                                    <input type="number" class="form-control" id="min_followers" name="min_followers"
                                           value="<?php echo isset($_POST['min_followers']) ? $_POST['min_followers'] : '1000'; ?>"
                                           min="1000" value="1000">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="max_followers" class="form-label">Max Followers</label>
                                    <input type="number" class="form-control" id="max_followers" name="max_followers"
                                           value="<?php echo isset($_POST['max_followers']) ? $_POST['max_followers'] : '100000'; ?>"
                                           min="1000" value="100000">
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">Company Description</label>
                                <textarea class="form-control" id="description" name="description" rows="3"
                                          placeholder="Tell us about your company and what you do..."><?php echo isset($_POST['description']) ? htmlspecialchars($_POST['description']) : ''; ?></textarea>
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-user-plus me-2"></i>Create Account
                            </button>
                        </div>
                    </form>
                    
                    <hr class="my-4">
                    
                    <div class="text-center">
                        <p class="mb-2">Already have an account?</p>
                        <a href="login.php" class="btn btn-outline-primary">
                            <i class="fas fa-sign-in-alt me-2"></i>Sign In
                        </a>
                    </div>
                    
                    <div class="text-center mt-3">
                        <a href="../index.php" class="text-muted text-decoration-none">
                            <i class="fas fa-arrow-left me-1"></i>Back to Homepage
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Handle user type selection
document.querySelectorAll('input[name="user_type"]').forEach(function(radio) {
    radio.addEventListener('change', function() {
        const influencerFields = document.getElementById('influencer-fields');
        const brandFields = document.getElementById('brand-fields');
        const instagramHandle = document.getElementById('instagram_handle');
        const influencerRequired = document.querySelectorAll('.influencer-required');
        const companyName = document.getElementById('company_name');
        const contactPerson = document.getElementById('contact_person');

        // Hide all specific fields first
        influencerFields.style.display = 'none';
        brandFields.style.display = 'none';
        instagramHandle.required = false;
        influencerRequired.forEach(el => el.style.display = 'none');
        companyName.required = false;
        contactPerson.required = false;

        if (this.value === 'influencer') {
            influencerFields.style.display = 'block';
            instagramHandle.required = true;
            influencerRequired.forEach(el => el.style.display = 'inline');
        } else if (this.value === 'brand') {
            brandFields.style.display = 'block';
            companyName.required = true;
            contactPerson.required = true;
        }

        // Update card styling
        document.querySelectorAll('.user-type-card').forEach(card => {
            card.classList.remove('border-primary');
        });
        document.querySelector(`[data-type="${this.value}"]`).classList.add('border-primary');
    });
});

// Password confirmation validation
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (password !== confirmPassword) {
        this.setCustomValidity('Passwords do not match');
    } else {
        this.setCustomValidity('');
    }
});

// Card click to select user type
document.querySelectorAll('.user-type-card').forEach(function(card) {
    card.addEventListener('click', function() {
        const type = this.dataset.type;
        document.getElementById(`user_type_${type}`).checked = true;
        document.getElementById(`user_type_${type}`).dispatchEvent(new Event('change'));
    });
});
</script>

<?php include '../includes/footer.php'; ?>
