<?php
require_once '../config.php';
require_once '../includes/instagram_verification.php';
require_login(['brand']);
require_instagram_verification('brand');

$db = Database::getInstance();
$brand_id = get_user_id();

// Get analytics data
$campaign_stats = $db->fetch("
    SELECT 
        COUNT(bc.id) as total_campaigns,
        COUNT(CASE WHEN bc.status = 'active' THEN 1 END) as active_campaigns,
        COUNT(CASE WHEN bc.status = 'completed' THEN 1 END) as completed_campaigns,
        COALESCE(SUM(bc.total_budget), 0) as total_budget,
        COALESCE(AVG(bc.reward_amount), 0) as avg_reward
    FROM brand_campaigns bc
    WHERE bc.brand_id = ?
", [$brand_id]);

$influencer_stats = $db->fetch("
    SELECT 
        COUNT(DISTINCT bia.influencer_id) as total_influencers,
        COUNT(CASE WHEN bia.status = 'accepted' THEN 1 END) as accepted_invitations,
        COUNT(CASE WHEN bia.status = 'completed' THEN 1 END) as completed_collaborations,
        COALESCE(AVG(bia.negotiated_rate), 0) as avg_rate
    FROM brand_influencer_assignments bia
    WHERE bia.brand_id = ?
", [$brand_id]);

// Get monthly campaign data for chart
$monthly_data = $db->fetchAll("
    SELECT 
        DATE_FORMAT(bc.created_at, '%Y-%m') as month,
        COUNT(bc.id) as campaigns_created,
        SUM(bc.total_budget) as budget_spent
    FROM brand_campaigns bc
    WHERE bc.brand_id = ? AND bc.created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
    GROUP BY DATE_FORMAT(bc.created_at, '%Y-%m')
    ORDER BY month ASC
", [$brand_id]);

// Get top performing campaigns
$top_campaigns = $db->fetchAll("
    SELECT 
        bc.title,
        bc.total_budget,
        bc.reward_amount,
        COUNT(bia.id) as total_influencers,
        COUNT(CASE WHEN bia.status = 'completed' THEN 1 END) as completed_collaborations,
        bc.created_at
    FROM brand_campaigns bc
    LEFT JOIN brand_influencer_assignments bia ON bc.id = bia.brand_campaign_id
    WHERE bc.brand_id = ?
    GROUP BY bc.id
    ORDER BY completed_collaborations DESC, total_influencers DESC
    LIMIT 5
", [$brand_id]);

// Get recent activity
$recent_activity = $db->fetchAll("
    SELECT 
        'campaign_created' as activity_type,
        bc.title as description,
        bc.created_at as activity_date
    FROM brand_campaigns bc
    WHERE bc.brand_id = ?
    
    UNION ALL
    
    SELECT 
        'influencer_assigned' as activity_type,
        CONCAT('Assigned ', i.full_name, ' to ', bc.title) as description,
        bia.assigned_at as activity_date
    FROM brand_influencer_assignments bia
    JOIN influencers i ON bia.influencer_id = i.id
    JOIN brand_campaigns bc ON bia.brand_campaign_id = bc.id
    WHERE bia.brand_id = ?
    
    ORDER BY activity_date DESC
    LIMIT 10
", [$brand_id, $brand_id]);

$page_title = 'Brand Analytics';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-xl-2 sidebar p-0">
            <div class="p-4">
                <h4 class="text-white mb-4">
                    <i class="fas fa-building me-2"></i>Brand Panel
                </h4>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a class="nav-link" href="campaigns.php">
                        <i class="fas fa-bullhorn"></i>Campaigns
                    </a>
                    <a class="nav-link" href="influencers.php">
                        <i class="fas fa-users"></i>Find Influencers
                    </a>
                    <a class="nav-link active" href="analytics.php">
                        <i class="fas fa-chart-line"></i>Analytics
                    </a>
                    <a class="nav-link" href="profile.php">
                        <i class="fas fa-user-edit"></i>Profile
                    </a>
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10 main-content" style="margin-left: auto;">
            <!-- Top Bar -->
            <div class="bg-white shadow-sm p-3 mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">Analytics Dashboard</h4>
                        <small class="text-muted">Track your campaign performance and ROI</small>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-outline-primary d-lg-none" onclick="toggleSidebar()">
                            <i class="fas fa-bars"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="container-fluid py-4">
                <!-- Stats Cards -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            Total Campaigns
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($campaign_stats['total_campaigns']); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-bullhorn fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            Total Influencers
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($influencer_stats['total_influencers']); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-users fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-info shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            Total Budget
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            ₹<?php echo number_format($campaign_stats['total_budget'], 0); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-rupee-sign fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-warning shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            Avg. Rate
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            ₹<?php echo number_format($influencer_stats['avg_rate'], 0); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- Top Campaigns -->
                    <div class="col-lg-8 mb-4">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">Top Performing Campaigns</h6>
                            </div>
                            <div class="card-body">
                                <?php if (empty($top_campaigns)): ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-chart-bar fa-3x text-gray-300 mb-3"></i>
                                        <p class="text-muted">No campaign data available yet.</p>
                                    </div>
                                <?php else: ?>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Campaign</th>
                                                    <th>Budget</th>
                                                    <th>Influencers</th>
                                                    <th>Completed</th>
                                                    <th>Success Rate</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($top_campaigns as $campaign): ?>
                                                    <tr>
                                                        <td>
                                                            <strong><?php echo htmlspecialchars($campaign['title']); ?></strong>
                                                            <br>
                                                            <small class="text-muted">
                                                                <?php echo date('M j, Y', strtotime($campaign['created_at'])); ?>
                                                            </small>
                                                        </td>
                                                        <td>₹<?php echo number_format($campaign['total_budget'], 0); ?></td>
                                                        <td><?php echo $campaign['total_influencers']; ?></td>
                                                        <td><?php echo $campaign['completed_collaborations']; ?></td>
                                                        <td>
                                                            <?php 
                                                            $success_rate = $campaign['total_influencers'] > 0 ? 
                                                                ($campaign['completed_collaborations'] / $campaign['total_influencers']) * 100 : 0;
                                                            ?>
                                                            <span class="badge bg-<?php echo $success_rate >= 80 ? 'success' : ($success_rate >= 50 ? 'warning' : 'danger'); ?>">
                                                                <?php echo round($success_rate); ?>%
                                                            </span>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="col-lg-4 mb-4">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">Recent Activity</h6>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recent_activity)): ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-clock fa-2x text-gray-300 mb-3"></i>
                                        <p class="text-muted small">No recent activity.</p>
                                    </div>
                                <?php else: ?>
                                    <?php foreach ($recent_activity as $activity): ?>
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="flex-shrink-0">
                                                <div class="avatar-sm bg-<?php echo $activity['activity_type'] === 'campaign_created' ? 'primary' : 'success'; ?> rounded-circle d-flex align-items-center justify-content-center">
                                                    <i class="fas fa-<?php echo $activity['activity_type'] === 'campaign_created' ? 'bullhorn' : 'user-plus'; ?> text-white"></i>
                                                </div>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <p class="mb-0 small"><?php echo htmlspecialchars($activity['description']); ?></p>
                                                <p class="text-muted small mb-0">
                                                    <?php echo time_ago($activity['activity_date']); ?>
                                                </p>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Performance Metrics -->
                <div class="row">
                    <div class="col-12">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">Performance Metrics</h6>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-md-3">
                                        <div class="border-end">
                                            <h4 class="text-primary"><?php echo $campaign_stats['active_campaigns']; ?></h4>
                                            <p class="text-muted mb-0">Active Campaigns</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="border-end">
                                            <h4 class="text-success"><?php echo $influencer_stats['accepted_invitations']; ?></h4>
                                            <p class="text-muted mb-0">Accepted Invitations</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="border-end">
                                            <h4 class="text-info"><?php echo $influencer_stats['completed_collaborations']; ?></h4>
                                            <p class="text-muted mb-0">Completed Collaborations</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <h4 class="text-warning">₹<?php echo number_format($campaign_stats['avg_reward'], 0); ?></h4>
                                        <p class="text-muted mb-0">Avg. Reward</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleSidebar() {
    document.querySelector('.sidebar').classList.toggle('show');
}
</script>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.avatar-sm {
    width: 2.5rem;
    height: 2.5rem;
}
</style>

<?php include '../includes/footer.php'; ?>
