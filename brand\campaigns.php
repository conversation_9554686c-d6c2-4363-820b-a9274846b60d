<?php
require_once '../config.php';
require_once '../includes/instagram_verification.php';
require_login(['brand']);
require_instagram_verification('brand');

$db = Database::getInstance();
$brand_id = get_user_id();

// Handle campaign creation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_campaign'])) {
    $title = sanitize_input($_POST['title']);
    $description = sanitize_input($_POST['description']);
    $target_audience = sanitize_input($_POST['target_audience']);
    $post_format = sanitize_input($_POST['post_format']);
    $reward_amount = floatval($_POST['reward_amount']);
    $total_budget = floatval($_POST['total_budget']);
    $min_followers = intval($_POST['min_followers']);
    $max_followers = intval($_POST['max_followers']);
    $required_categories = sanitize_input($_POST['required_categories']);
    $max_influencers = intval($_POST['max_influencers']);
    $start_date = sanitize_input($_POST['start_date']);
    $end_date = sanitize_input($_POST['end_date']);
    $requirements = sanitize_input($_POST['requirements']);
    $hashtags = sanitize_input($_POST['hashtags']);
    
    $errors = [];
    
    if (empty($title)) $errors[] = 'Campaign title is required.';
    if (empty($description)) $errors[] = 'Campaign description is required.';
    if ($reward_amount <= 0) $errors[] = 'Reward amount must be greater than 0.';
    if ($total_budget <= 0) $errors[] = 'Total budget must be greater than 0.';
    if ($min_followers <= 0) $errors[] = 'Minimum followers must be greater than 0.';
    if ($max_followers <= $min_followers) $errors[] = 'Maximum followers must be greater than minimum followers.';
    if ($max_influencers <= 0) $errors[] = 'Maximum influencers must be greater than 0.';
    if (empty($start_date)) $errors[] = 'Start date is required.';
    if (empty($end_date)) $errors[] = 'End date is required.';
    if (strtotime($end_date) <= strtotime($start_date)) $errors[] = 'End date must be after start date.';
    
    if (empty($errors)) {
        try {
            $campaign_id = $db->insert('brand_campaigns', [
                'brand_id' => $brand_id,
                'title' => $title,
                'description' => $description,
                'target_audience' => $target_audience,
                'post_format' => $post_format,
                'reward_amount' => $reward_amount,
                'total_budget' => $total_budget,
                'min_followers' => $min_followers,
                'max_followers' => $max_followers,
                'required_categories' => $required_categories,
                'max_influencers' => $max_influencers,
                'start_date' => $start_date,
                'end_date' => $end_date,
                'requirements' => $requirements,
                'hashtags' => $hashtags,
                'status' => 'active'
            ]);
            
            $_SESSION['success'] = 'Campaign created successfully!';
            redirect('campaigns.php');
        } catch (Exception $e) {
            $_SESSION['error'] = 'Failed to create campaign.';
        }
    } else {
        $_SESSION['error'] = implode('<br>', $errors);
    }
}

// Handle campaign status update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status'])) {
    $campaign_id = intval($_POST['campaign_id']);
    $status = sanitize_input($_POST['status']);
    
    try {
        $db->update('brand_campaigns', 
            ['status' => $status], 
            'id = ? AND brand_id = ?', 
            [$campaign_id, $brand_id]
        );
        $_SESSION['success'] = 'Campaign status updated successfully!';
    } catch (Exception $e) {
        $_SESSION['error'] = 'Failed to update campaign status.';
    }
}

// Get filter parameters
$status_filter = $_GET['status'] ?? '';
$search = $_GET['search'] ?? '';

// Build query conditions
$conditions = ['bc.brand_id = ?'];
$params = [$brand_id];

if ($status_filter) {
    $conditions[] = "bc.status = ?";
    $params[] = $status_filter;
}

if ($search) {
    $conditions[] = "(bc.title LIKE ? OR bc.description LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

$where_clause = implode(' AND ', $conditions);

// Get campaigns
$campaigns = $db->fetchAll("
    SELECT bc.*, 
           COUNT(bia.id) as assigned_influencers,
           COUNT(CASE WHEN bia.status = 'completed' THEN 1 END) as completed_assignments
    FROM brand_campaigns bc
    LEFT JOIN brand_influencer_assignments bia ON bc.id = bia.brand_campaign_id
    WHERE {$where_clause}
    GROUP BY bc.id
    ORDER BY bc.created_at DESC
", $params);

$page_title = 'Brand Campaigns';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-xl-2 sidebar p-0">
            <div class="p-4">
                <h4 class="text-white mb-4">
                    <i class="fas fa-building me-2"></i>Brand Panel
                </h4>

                <nav class="nav flex-column">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a class="nav-link active" href="campaigns.php">
                        <i class="fas fa-bullhorn"></i>Campaigns
                    </a>
                    <a class="nav-link" href="influencers.php">
                        <i class="fas fa-users"></i>Find Influencers
                    </a>
                    <a class="nav-link" href="analytics.php">
                        <i class="fas fa-chart-line"></i>Analytics
                    </a>
                    <a class="nav-link" href="profile.php">
                        <i class="fas fa-user-edit"></i>Profile
                    </a>
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </nav>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10 main-content" style="margin-left: auto;">
            <!-- Top Bar -->
            <div class="bg-white shadow-sm p-3 mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">Campaign Management</h4>
                        <small class="text-muted">Create and manage your brand campaigns</small>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createCampaignModal">
                            <i class="fas fa-plus me-2"></i>Create Campaign
                        </button>
                        <button class="btn btn-outline-primary d-lg-none ms-2" onclick="toggleSidebar()">
                            <i class="fas fa-bars"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="container-fluid py-4">

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">All Statuses</option>
                                <option value="draft" <?php echo $status_filter === 'draft' ? 'selected' : ''; ?>>Draft</option>
                                <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>Active</option>
                                <option value="paused" <?php echo $status_filter === 'paused' ? 'selected' : ''; ?>>Paused</option>
                                <option value="completed" <?php echo $status_filter === 'completed' ? 'selected' : ''; ?>>Completed</option>
                                <option value="cancelled" <?php echo $status_filter === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo htmlspecialchars($search); ?>" 
                                   placeholder="Search campaigns...">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-outline-primary">
                                    <i class="fas fa-search me-1"></i>Filter
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Campaigns List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <?php if (empty($campaigns)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-bullhorn fa-4x text-gray-300 mb-4"></i>
                            <h4 class="text-muted">No campaigns found</h4>
                            <p class="text-muted">Create your first campaign to start collaborating with influencers.</p>
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createCampaignModal">
                                <i class="fas fa-plus me-2"></i>Create Campaign
                            </button>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Campaign</th>
                                        <th>Status</th>
                                        <th>Budget</th>
                                        <th>Followers Range</th>
                                        <th>Influencers</th>
                                        <th>Progress</th>
                                        <th>Duration</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($campaigns as $campaign): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($campaign['title']); ?></strong>
                                                    <br>
                                                    <small class="text-muted">
                                                        <?php echo htmlspecialchars(substr($campaign['description'], 0, 50)) . '...'; ?>
                                                    </small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo $campaign['status'] === 'active' ? 'success' : 
                                                        ($campaign['status'] === 'completed' ? 'primary' : 
                                                        ($campaign['status'] === 'paused' ? 'warning' : 'secondary')); 
                                                ?>">
                                                    <?php echo ucfirst($campaign['status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong>₹<?php echo number_format($campaign['total_budget'], 2); ?></strong>
                                                    <br>
                                                    <small class="text-muted">₹<?php echo number_format($campaign['reward_amount'], 2); ?> per influencer</small>
                                                </div>
                                            </td>
                                            <td>
                                                <?php echo number_format($campaign['min_followers']); ?> - 
                                                <?php echo number_format($campaign['max_followers']); ?>
                                            </td>
                                            <td>
                                                <?php echo $campaign['assigned_influencers']; ?>/<?php echo $campaign['max_influencers']; ?>
                                            </td>
                                            <td>
                                                <?php 
                                                $progress = $campaign['assigned_influencers'] > 0 ? 
                                                    ($campaign['completed_assignments'] / $campaign['assigned_influencers']) * 100 : 0;
                                                ?>
                                                <div class="progress mb-1" style="height: 6px;">
                                                    <div class="progress-bar" style="width: <?php echo $progress; ?>%"></div>
                                                </div>
                                                <small class="text-muted"><?php echo round($progress); ?>% complete</small>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?php echo date('M j', strtotime($campaign['start_date'])); ?> - 
                                                    <?php echo date('M j, Y', strtotime($campaign['end_date'])); ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="influencers.php?campaign=<?php echo $campaign['id']; ?>" 
                                                       class="btn btn-sm btn-outline-primary" title="Assign Influencers">
                                                        <i class="fas fa-users"></i>
                                                    </a>
                                                    <button class="btn btn-sm btn-outline-info" 
                                                            data-bs-toggle="modal" 
                                                            data-bs-target="#viewCampaignModal<?php echo $campaign['id']; ?>"
                                                            title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <?php if ($campaign['status'] !== 'completed'): ?>
                                                        <button class="btn btn-sm btn-outline-warning" 
                                                                data-bs-toggle="modal" 
                                                                data-bs-target="#statusModal<?php echo $campaign['id']; ?>"
                                                                title="Update Status">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleSidebar() {
    document.querySelector('.sidebar').classList.toggle('show');
}
</script>

<!-- Create Campaign Modal -->
<div class="modal fade" id="createCampaignModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Campaign</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="title" class="form-label">Campaign Title *</label>
                                <input type="text" class="form-control" id="title" name="title" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="target_audience" class="form-label">Target Audience</label>
                                <input type="text" class="form-control" id="target_audience" name="target_audience" 
                                       placeholder="e.g., Young adults, Tech enthusiasts">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Campaign Description *</label>
                        <textarea class="form-control" id="description" name="description" rows="3" required></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="post_format" class="form-label">Post Format</label>
                                <select class="form-select" id="post_format" name="post_format">
                                    <option value="any">Any Format</option>
                                    <option value="post">Instagram Post</option>
                                    <option value="story">Instagram Story</option>
                                    <option value="reel">Instagram Reel</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="reward_amount" class="form-label">Reward per Influencer (₹) *</label>
                                <input type="number" class="form-control" id="reward_amount" name="reward_amount" 
                                       min="100" step="50" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="total_budget" class="form-label">Total Budget (₹) *</label>
                                <input type="number" class="form-control" id="total_budget" name="total_budget" 
                                       min="1000" step="100" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="min_followers" class="form-label">Min Followers *</label>
                                <input type="number" class="form-control" id="min_followers" name="min_followers" 
                                       min="1000" value="1000" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="max_followers" class="form-label">Max Followers *</label>
                                <input type="number" class="form-control" id="max_followers" name="max_followers" 
                                       min="1000" value="100000" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="max_influencers" class="form-label">Max Influencers *</label>
                                <input type="number" class="form-control" id="max_influencers" name="max_influencers" 
                                       min="1" value="10" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="start_date" class="form-label">Start Date *</label>
                                <input type="date" class="form-control" id="start_date" name="start_date" 
                                       min="<?php echo date('Y-m-d'); ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="end_date" class="form-label">End Date *</label>
                                <input type="date" class="form-control" id="end_date" name="end_date" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="required_categories" class="form-label">Preferred Categories</label>
                        <input type="text" class="form-control" id="required_categories" name="required_categories" 
                               placeholder="e.g., Fashion, Tech, Lifestyle (comma separated)">
                    </div>
                    
                    <div class="mb-3">
                        <label for="requirements" class="form-label">Campaign Requirements</label>
                        <textarea class="form-control" id="requirements" name="requirements" rows="3" 
                                  placeholder="Specific requirements for influencers..."></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="hashtags" class="form-label">Required Hashtags</label>
                        <input type="text" class="form-control" id="hashtags" name="hashtags" 
                               placeholder="#YourBrand #Campaign #Sponsored">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="create_campaign" class="btn btn-primary">Create Campaign</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
