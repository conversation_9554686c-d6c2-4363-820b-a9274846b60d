<?php
require_once '../config.php';
require_login(['brand']);

$db = Database::getInstance();
$brand_id = get_user_id();

// Get brand stats
$stats = $db->fetch("
    SELECT 
        COUNT(bc.id) as total_campaigns,
        COUNT(CASE WHEN bc.status = 'active' THEN 1 END) as active_campaigns,
        COUNT(CASE WHEN bc.status = 'completed' THEN 1 END) as completed_campaigns,
        COALESCE(SUM(bc.total_budget), 0) as total_spent,
        COUNT(DISTINCT bia.influencer_id) as total_influencers
    FROM brand_campaigns bc
    LEFT JOIN brand_influencer_assignments bia ON bc.id = bia.brand_campaign_id
    WHERE bc.brand_id = ?
", [$brand_id]);

// Get recent campaigns
$recent_campaigns = $db->fetchAll("
    SELECT bc.*, 
           COUNT(bia.id) as assigned_influencers,
           COUNT(CASE WHEN bia.status = 'completed' THEN 1 END) as completed_assignments
    FROM brand_campaigns bc
    LEFT JOIN brand_influencer_assignments bia ON bc.id = bia.brand_campaign_id
    WHERE bc.brand_id = ?
    GROUP BY bc.id
    ORDER BY bc.created_at DESC
    LIMIT 5
", [$brand_id]);

// Get recent influencer assignments
$recent_assignments = $db->fetchAll("
    SELECT bia.*, bc.title as campaign_title, i.full_name, i.instagram_handle, i.instagram_followers
    FROM brand_influencer_assignments bia
    JOIN brand_campaigns bc ON bia.brand_campaign_id = bc.id
    JOIN influencers i ON bia.influencer_id = i.id
    WHERE bia.brand_id = ?
    ORDER BY bia.assigned_at DESC
    LIMIT 5
", [$brand_id]);

// Get brand info
$brand = $db->fetch("SELECT * FROM brands WHERE id = ?", [$brand_id]);

$page_title = 'Brand Dashboard';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-xl-2 sidebar p-0">
            <div class="p-4">
                <h4 class="text-white mb-4">
                    <i class="fas fa-building me-2"></i>Brand Panel
                </h4>

                <nav class="nav flex-column">
                    <a class="nav-link active" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a class="nav-link" href="campaigns.php">
                        <i class="fas fa-bullhorn"></i>Campaigns
                    </a>
                    <a class="nav-link" href="influencers.php">
                        <i class="fas fa-users"></i>Find Influencers
                    </a>
                    <a class="nav-link" href="analytics.php">
                        <i class="fas fa-chart-line"></i>Analytics
                    </a>
                    <a class="nav-link" href="profile.php">
                        <i class="fas fa-user-edit"></i>Profile
                    </a>
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </nav>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10 main-content" style="margin-left: auto;">
            <!-- Top Bar -->
            <div class="bg-white shadow-sm p-3 mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">Brand Dashboard</h4>
                        <small class="text-muted">Welcome back, <?php echo htmlspecialchars($brand['company_name']); ?>!</small>
                    </div>
                    <div class="col-auto">
                        <a href="campaigns.php" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Create Campaign
                        </a>
                        <button class="btn btn-outline-primary d-lg-none ms-2" onclick="toggleSidebar()">
                            <i class="fas fa-bars"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="container-fluid py-4">

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Campaigns
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($stats['total_campaigns']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-bullhorn fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Active Campaigns
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($stats['active_campaigns']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-play-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Total Influencers
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($stats['total_influencers']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Total Spent
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                ₹<?php echo number_format($stats['total_spent'], 2); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-rupee-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Campaigns -->
        <div class="col-lg-8 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Campaigns</h6>
                    <a href="campaigns.php" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    <?php if (empty($recent_campaigns)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-bullhorn fa-3x text-gray-300 mb-3"></i>
                            <p class="text-muted">No campaigns created yet.</p>
                            <a href="campaigns.php" class="btn btn-primary">Create Your First Campaign</a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Campaign</th>
                                        <th>Status</th>
                                        <th>Budget</th>
                                        <th>Influencers</th>
                                        <th>Progress</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_campaigns as $campaign): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($campaign['title']); ?></strong>
                                                    <br>
                                                    <small class="text-muted">
                                                        <?php echo date('M j, Y', strtotime($campaign['created_at'])); ?>
                                                    </small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo $campaign['status'] === 'active' ? 'success' : 
                                                        ($campaign['status'] === 'completed' ? 'primary' : 'secondary'); 
                                                ?>">
                                                    <?php echo ucfirst($campaign['status']); ?>
                                                </span>
                                            </td>
                                            <td>₹<?php echo number_format($campaign['total_budget'], 2); ?></td>
                                            <td>
                                                <?php echo $campaign['assigned_influencers']; ?>/<?php echo $campaign['max_influencers']; ?>
                                            </td>
                                            <td>
                                                <?php 
                                                $progress = $campaign['assigned_influencers'] > 0 ? 
                                                    ($campaign['completed_assignments'] / $campaign['assigned_influencers']) * 100 : 0;
                                                ?>
                                                <div class="progress" style="height: 6px;">
                                                    <div class="progress-bar" style="width: <?php echo $progress; ?>%"></div>
                                                </div>
                                                <small class="text-muted"><?php echo round($progress); ?>% complete</small>
                                            </td>
                                            <td>
                                                <a href="campaigns.php?view=<?php echo $campaign['id']; ?>" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Recent Assignments -->
        <div class="col-lg-4 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Assignments</h6>
                    <a href="influencers.php" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    <?php if (empty($recent_assignments)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-user-plus fa-2x text-gray-300 mb-3"></i>
                            <p class="text-muted small">No influencer assignments yet.</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($recent_assignments as $assignment): ?>
                            <div class="d-flex align-items-center mb-3">
                                <div class="flex-shrink-0">
                                    <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center">
                                        <i class="fas fa-user text-white"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="mb-0"><?php echo htmlspecialchars($assignment['full_name']); ?></h6>
                                    <p class="text-muted small mb-0">
                                        @<?php echo htmlspecialchars($assignment['instagram_handle']); ?>
                                        (<?php echo number_format($assignment['instagram_followers']); ?> followers)
                                    </p>
                                    <p class="text-muted small mb-0">
                                        <?php echo htmlspecialchars($assignment['campaign_title']); ?>
                                    </p>
                                </div>
                                <div class="flex-shrink-0">
                                    <span class="badge bg-<?php 
                                        echo $assignment['status'] === 'accepted' ? 'success' : 
                                            ($assignment['status'] === 'completed' ? 'primary' : 'warning'); 
                                    ?>">
                                        <?php echo ucfirst($assignment['status']); ?>
                                    </span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleSidebar() {
    document.querySelector('.sidebar').classList.toggle('show');
}
</script>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.avatar-sm {
    width: 2.5rem;
    height: 2.5rem;
}
</style>

<?php include '../includes/footer.php'; ?>
