<?php
require_once '../config.php';
require_login(['brand']);

$db = Database::getInstance();
$brand_id = get_user_id();

// Handle influencer assignment
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['assign_influencer'])) {
    $campaign_id = intval($_POST['campaign_id']);
    $influencer_id = intval($_POST['influencer_id']);
    $negotiated_rate = floatval($_POST['negotiated_rate']);
    $brand_notes = sanitize_input($_POST['brand_notes']);
    
    // Verify campaign belongs to brand
    $campaign = $db->fetch("
        SELECT * FROM brand_campaigns 
        WHERE id = ? AND brand_id = ? AND status = 'active'
    ", [$campaign_id, $brand_id]);
    
    if ($campaign) {
        // Check if already assigned
        $existing = $db->fetch("
            SELECT id FROM brand_influencer_assignments 
            WHERE brand_campaign_id = ? AND influencer_id = ?
        ", [$campaign_id, $influencer_id]);
        
        if (!$existing) {
            try {
                $db->insert('brand_influencer_assignments', [
                    'brand_campaign_id' => $campaign_id,
                    'brand_id' => $brand_id,
                    'influencer_id' => $influencer_id,
                    'status' => 'invited',
                    'negotiated_rate' => $negotiated_rate ?: $campaign['reward_amount'],
                    'brand_notes' => $brand_notes
                ]);
                
                // Update campaign influencer count
                $db->update('brand_campaigns', 
                    ['current_influencers' => $campaign['current_influencers'] + 1], 
                    'id = ?', [$campaign_id]
                );
                
                $_SESSION['success'] = 'Influencer invited successfully!';
            } catch (Exception $e) {
                $_SESSION['error'] = 'Failed to assign influencer.';
            }
        } else {
            $_SESSION['warning'] = 'Influencer already assigned to this campaign.';
        }
    } else {
        $_SESSION['error'] = 'Invalid campaign.';
    }
}

// Get filter parameters
$campaign_filter = $_GET['campaign'] ?? '';
$category_filter = $_GET['category'] ?? '';
$min_followers = $_GET['min_followers'] ?? '';
$max_followers = $_GET['max_followers'] ?? '';
$search = $_GET['search'] ?? '';

// Get brand campaigns for filter
$brand_campaigns = $db->fetchAll("
    SELECT id, title FROM brand_campaigns 
    WHERE brand_id = ? AND status = 'active'
    ORDER BY created_at DESC
", [$brand_id]);

// Build influencer query conditions
$conditions = ['i.status = ?'];
$params = ['approved'];

if ($category_filter) {
    $conditions[] = "i.category = ?";
    $params[] = $category_filter;
}

if ($min_followers) {
    $conditions[] = "i.instagram_followers >= ?";
    $params[] = intval($min_followers);
}

if ($max_followers) {
    $conditions[] = "i.instagram_followers <= ?";
    $params[] = intval($max_followers);
}

if ($search) {
    $conditions[] = "(i.full_name LIKE ? OR i.instagram_handle LIKE ? OR i.bio LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

$where_clause = implode(' AND ', $conditions);

// Get influencers
$influencers = $db->fetchAll("
    SELECT i.*, 
           COUNT(bia.id) as total_brand_campaigns,
           COUNT(CASE WHEN bia.status = 'completed' THEN 1 END) as completed_campaigns
    FROM influencers i
    LEFT JOIN brand_influencer_assignments bia ON i.id = bia.influencer_id
    WHERE {$where_clause}
    GROUP BY i.id
    ORDER BY i.instagram_followers DESC
", $params);

// Get categories for filter
$categories = $db->fetchAll("
    SELECT DISTINCT category FROM influencers 
    WHERE category IS NOT NULL AND category != '' AND status = 'approved'
    ORDER BY category
");

$page_title = 'Find Influencers';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-xl-2 sidebar p-0">
            <div class="p-4">
                <h4 class="text-white mb-4">
                    <i class="fas fa-building me-2"></i>Brand Panel
                </h4>

                <nav class="nav flex-column">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a class="nav-link" href="campaigns.php">
                        <i class="fas fa-bullhorn"></i>Campaigns
                    </a>
                    <a class="nav-link active" href="influencers.php">
                        <i class="fas fa-users"></i>Find Influencers
                    </a>
                    <a class="nav-link" href="analytics.php">
                        <i class="fas fa-chart-line"></i>Analytics
                    </a>
                    <a class="nav-link" href="profile.php">
                        <i class="fas fa-user-edit"></i>Profile
                    </a>
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </nav>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10 main-content" style="margin-left: auto;">
            <!-- Top Bar -->
            <div class="bg-white shadow-sm p-3 mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">Find Influencers</h4>
                        <small class="text-muted">Discover and collaborate with influencers for your campaigns</small>
                    </div>
                    <div class="col-auto">
                        <a href="campaigns.php" class="btn btn-outline-primary">
                            <i class="fas fa-bullhorn me-2"></i>Manage Campaigns
                        </a>
                        <button class="btn btn-outline-primary d-lg-none ms-2" onclick="toggleSidebar()">
                            <i class="fas fa-bars"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="container-fluid py-4">

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="campaign" class="form-label">Campaign</label>
                            <select class="form-select" id="campaign" name="campaign">
                                <option value="">All Campaigns</option>
                                <?php foreach ($brand_campaigns as $campaign): ?>
                                    <option value="<?php echo $campaign['id']; ?>" 
                                            <?php echo $campaign_filter == $campaign['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($campaign['title']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="category" class="form-label">Category</label>
                            <select class="form-select" id="category" name="category">
                                <option value="">All Categories</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['category']; ?>" 
                                            <?php echo $category_filter === $category['category'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($category['category']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="min_followers" class="form-label">Min Followers</label>
                            <input type="number" class="form-control" id="min_followers" name="min_followers" 
                                   value="<?php echo htmlspecialchars($min_followers); ?>" placeholder="1000">
                        </div>
                        <div class="col-md-2">
                            <label for="max_followers" class="form-label">Max Followers</label>
                            <input type="number" class="form-control" id="max_followers" name="max_followers" 
                                   value="<?php echo htmlspecialchars($max_followers); ?>" placeholder="100000">
                        </div>
                        <div class="col-md-2">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo htmlspecialchars($search); ?>" placeholder="Name or handle">
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Influencers Grid -->
    <div class="row">
        <?php if (empty($influencers)): ?>
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-users fa-4x text-gray-300 mb-4"></i>
                    <h4 class="text-muted">No influencers found</h4>
                    <p class="text-muted">Try adjusting your search criteria to find more influencers.</p>
                </div>
            </div>
        <?php else: ?>
            <?php foreach ($influencers as $influencer): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="flex-shrink-0">
                                    <?php if ($influencer['profile_image']): ?>
                                        <img src="../<?php echo htmlspecialchars($influencer['profile_image']); ?>" 
                                             class="rounded-circle" width="60" height="60" alt="Profile">
                                    <?php else: ?>
                                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" 
                                             style="width: 60px; height: 60px;">
                                            <i class="fas fa-user text-white fa-lg"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h5 class="mb-1"><?php echo htmlspecialchars($influencer['full_name']); ?></h5>
                                    <p class="text-muted mb-0">@<?php echo htmlspecialchars($influencer['instagram_handle']); ?></p>
                                    <?php if ($influencer['instagram_verified']): ?>
                                        <span class="badge bg-primary">
                                            <i class="fas fa-check-circle me-1"></i>Verified
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <div class="row text-center mb-3">
                                <div class="col-4">
                                    <div class="border-end">
                                        <h6 class="mb-0"><?php echo number_format($influencer['instagram_followers']); ?></h6>
                                        <small class="text-muted">Followers</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="border-end">
                                        <h6 class="mb-0">₹<?php echo number_format($influencer['rate_per_post'], 0); ?></h6>
                                        <small class="text-muted">Rate</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <h6 class="mb-0"><?php echo $influencer['completed_campaigns']; ?></h6>
                                    <small class="text-muted">Campaigns</small>
                                </div>
                            </div>
                            
                            <?php if ($influencer['category']): ?>
                                <div class="mb-3">
                                    <span class="badge bg-light text-dark">
                                        <?php echo htmlspecialchars($influencer['category']); ?>
                                    </span>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($influencer['bio']): ?>
                                <p class="text-muted small mb-3">
                                    <?php echo htmlspecialchars(substr($influencer['bio'], 0, 100)) . '...'; ?>
                                </p>
                            <?php endif; ?>
                        </div>
                        
                        <div class="card-footer bg-white border-0">
                            <div class="d-grid gap-2">
                                <button class="btn btn-primary btn-sm" 
                                        data-bs-toggle="modal" 
                                        data-bs-target="#assignModal<?php echo $influencer['id']; ?>">
                                    <i class="fas fa-handshake me-1"></i>Assign Campaign
                                </button>
                                <button class="btn btn-outline-info btn-sm" 
                                        data-bs-toggle="modal" 
                                        data-bs-target="#profileModal<?php echo $influencer['id']; ?>">
                                    <i class="fas fa-eye me-1"></i>View Profile
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Assign Campaign Modal -->
                <div class="modal fade" id="assignModal<?php echo $influencer['id']; ?>" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Assign Campaign to <?php echo htmlspecialchars($influencer['full_name']); ?></h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <form method="POST">
                                <div class="modal-body">
                                    <input type="hidden" name="influencer_id" value="<?php echo $influencer['id']; ?>">
                                    
                                    <div class="mb-3">
                                        <label for="campaign_id" class="form-label">Select Campaign *</label>
                                        <select class="form-select" name="campaign_id" required>
                                            <option value="">Choose a campaign...</option>
                                            <?php foreach ($brand_campaigns as $campaign): ?>
                                                <option value="<?php echo $campaign['id']; ?>" 
                                                        <?php echo $campaign_filter == $campaign['id'] ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($campaign['title']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="negotiated_rate" class="form-label">Negotiated Rate (₹)</label>
                                        <input type="number" class="form-control" name="negotiated_rate" 
                                               value="<?php echo $influencer['rate_per_post']; ?>" 
                                               min="100" step="50">
                                        <div class="form-text">Leave empty to use campaign default rate</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="brand_notes" class="form-label">Message to Influencer</label>
                                        <textarea class="form-control" name="brand_notes" rows="3" 
                                                  placeholder="Add any specific instructions or requirements..."></textarea>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                    <button type="submit" name="assign_influencer" class="btn btn-primary">Send Invitation</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Profile Modal -->
                <div class="modal fade" id="profileModal<?php echo $influencer['id']; ?>" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Influencer Profile</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-4 text-center">
                                        <?php if ($influencer['profile_image']): ?>
                                            <img src="../<?php echo htmlspecialchars($influencer['profile_image']); ?>" 
                                                 class="rounded-circle mb-3" width="120" height="120" alt="Profile">
                                        <?php else: ?>
                                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" 
                                                 style="width: 120px; height: 120px;">
                                                <i class="fas fa-user text-white fa-3x"></i>
                                            </div>
                                        <?php endif; ?>
                                        <h5><?php echo htmlspecialchars($influencer['full_name']); ?></h5>
                                        <p class="text-muted">@<?php echo htmlspecialchars($influencer['instagram_handle']); ?></p>
                                    </div>
                                    <div class="col-md-8">
                                        <div class="row mb-3">
                                            <div class="col-6">
                                                <strong>Followers:</strong> <?php echo number_format($influencer['instagram_followers']); ?>
                                            </div>
                                            <div class="col-6">
                                                <strong>Rate per Post:</strong> ₹<?php echo number_format($influencer['rate_per_post'], 0); ?>
                                            </div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-6">
                                                <strong>Category:</strong> <?php echo htmlspecialchars($influencer['category'] ?: 'Not specified'); ?>
                                            </div>
                                            <div class="col-6">
                                                <strong>Total Earned:</strong> ₹<?php echo number_format($influencer['total_earned'], 2); ?>
                                            </div>
                                        </div>
                                        <?php if ($influencer['bio']): ?>
                                            <div class="mb-3">
                                                <strong>Bio:</strong>
                                                <p class="mt-2"><?php echo nl2br(htmlspecialchars($influencer['bio'])); ?></p>
                                            </div>
                                        <?php endif; ?>
                                        <div class="mb-3">
                                            <strong>Campaign Stats:</strong>
                                            <ul class="list-unstyled mt-2">
                                                <li>Total Brand Campaigns: <?php echo $influencer['total_brand_campaigns']; ?></li>
                                                <li>Completed Campaigns: <?php echo $influencer['completed_campaigns']; ?></li>
                                                <li>Success Rate: <?php echo $influencer['total_brand_campaigns'] > 0 ? 
                                                    round(($influencer['completed_campaigns'] / $influencer['total_brand_campaigns']) * 100) : 0; ?>%</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                <button type="button" class="btn btn-primary" 
                                        data-bs-dismiss="modal" 
                                        data-bs-toggle="modal" 
                                        data-bs-target="#assignModal<?php echo $influencer['id']; ?>">
                                    Assign Campaign
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
function toggleSidebar() {
    document.querySelector('.sidebar').classList.toggle('show');
}
</script>

<?php include '../includes/footer.php'; ?>
