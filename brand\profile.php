<?php
require_once '../config.php';
require_login(['brand']);

$db = Database::getInstance();
$brand_id = get_user_id();

// Handle profile update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_profile'])) {
    $company_name = sanitize_input($_POST['company_name']);
    $contact_person = sanitize_input($_POST['contact_person']);
    $phone = sanitize_input($_POST['phone']);
    $description = sanitize_input($_POST['description']);
    $website = sanitize_input($_POST['website']);
    $industry = sanitize_input($_POST['industry']);
    $budget = floatval($_POST['budget']);
    $min_followers = intval($_POST['min_followers']);
    $max_followers = intval($_POST['max_followers']);
    $preferred_categories = sanitize_input($_POST['preferred_categories']);
    
    $errors = [];
    
    if (empty($company_name)) $errors[] = 'Company name is required.';
    if (empty($contact_person)) $errors[] = 'Contact person is required.';
    if ($min_followers <= 0) $errors[] = 'Minimum followers must be greater than 0.';
    if ($max_followers <= $min_followers) $errors[] = 'Maximum followers must be greater than minimum followers.';
    
    if (empty($errors)) {
        $data = [
            'company_name' => $company_name,
            'contact_person' => $contact_person,
            'phone' => $phone,
            'description' => $description,
            'website' => $website,
            'industry' => $industry,
            'budget' => $budget,
            'min_followers' => $min_followers,
            'max_followers' => $max_followers,
            'preferred_categories' => $preferred_categories
        ];
        
        // Handle logo upload
        if (isset($_FILES['logo']) && $_FILES['logo']['error'] === UPLOAD_ERR_OK) {
            try {
                $logo_path = upload_file($_FILES['logo'], 'uploads/brands/');
                $data['logo'] = $logo_path;
            } catch (Exception $e) {
                $errors[] = 'Failed to upload logo: ' . $e->getMessage();
            }
        }
        
        if (empty($errors)) {
            try {
                $db->update('brands', $data, 'id = ?', [$brand_id]);
                $_SESSION['success'] = 'Profile updated successfully!';
                redirect('profile.php');
            } catch (Exception $e) {
                $_SESSION['error'] = 'Failed to update profile.';
            }
        }
    }
    
    if (!empty($errors)) {
        $_SESSION['error'] = implode('<br>', $errors);
    }
}

// Handle password change
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['change_password'])) {
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];
    
    $errors = [];
    
    if (empty($current_password)) $errors[] = 'Current password is required.';
    if (empty($new_password)) $errors[] = 'New password is required.';
    if ($new_password !== $confirm_password) $errors[] = 'New passwords do not match.';
    if (strlen($new_password) < 6) $errors[] = 'New password must be at least 6 characters.';
    
    if (empty($errors)) {
        // Verify current password
        $brand = $db->fetch("SELECT password FROM brands WHERE id = ?", [$brand_id]);
        
        if (password_verify($current_password, $brand['password'])) {
            try {
                $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                $db->update('brands', ['password' => $hashed_password], 'id = ?', [$brand_id]);
                $_SESSION['success'] = 'Password changed successfully!';
            } catch (Exception $e) {
                $_SESSION['error'] = 'Failed to change password.';
            }
        } else {
            $_SESSION['error'] = 'Current password is incorrect.';
        }
    } else {
        $_SESSION['error'] = implode('<br>', $errors);
    }
}

// Get brand data
$brand = $db->fetch("SELECT * FROM brands WHERE id = ?", [$brand_id]);

$page_title = 'Brand Profile';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-xl-2 sidebar p-0">
            <div class="p-4">
                <h4 class="text-white mb-4">
                    <i class="fas fa-building me-2"></i>Brand Panel
                </h4>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a class="nav-link" href="campaigns.php">
                        <i class="fas fa-bullhorn"></i>Campaigns
                    </a>
                    <a class="nav-link" href="influencers.php">
                        <i class="fas fa-users"></i>Find Influencers
                    </a>
                    <a class="nav-link" href="analytics.php">
                        <i class="fas fa-chart-line"></i>Analytics
                    </a>
                    <a class="nav-link active" href="profile.php">
                        <i class="fas fa-user-edit"></i>Profile
                    </a>
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10 main-content" style="margin-left: auto;">
            <!-- Top Bar -->
            <div class="bg-white shadow-sm p-3 mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">Brand Profile</h4>
                        <small class="text-muted">Manage your brand information and preferences</small>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-outline-primary d-lg-none" onclick="toggleSidebar()">
                            <i class="fas fa-bars"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="container-fluid py-4">
                <div class="row">
                    <div class="col-lg-8">
                        <!-- Profile Information -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-building me-2"></i>Brand Information
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" enctype="multipart/form-data">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="company_name" class="form-label">Company Name *</label>
                                                <input type="text" class="form-control" id="company_name" name="company_name" 
                                                       value="<?php echo htmlspecialchars($brand['company_name']); ?>" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="contact_person" class="form-label">Contact Person *</label>
                                                <input type="text" class="form-control" id="contact_person" name="contact_person" 
                                                       value="<?php echo htmlspecialchars($brand['contact_person']); ?>" required>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="phone" class="form-label">Phone</label>
                                                <input type="tel" class="form-control" id="phone" name="phone" 
                                                       value="<?php echo htmlspecialchars($brand['phone']); ?>">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="website" class="form-label">Website</label>
                                                <input type="url" class="form-control" id="website" name="website" 
                                                       value="<?php echo htmlspecialchars($brand['website']); ?>" 
                                                       placeholder="https://yourwebsite.com">
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="industry" class="form-label">Industry</label>
                                                <select class="form-select" id="industry" name="industry">
                                                    <option value="">Select Industry</option>
                                                    <option value="Technology" <?php echo $brand['industry'] === 'Technology' ? 'selected' : ''; ?>>Technology</option>
                                                    <option value="Fashion" <?php echo $brand['industry'] === 'Fashion' ? 'selected' : ''; ?>>Fashion</option>
                                                    <option value="Beauty" <?php echo $brand['industry'] === 'Beauty' ? 'selected' : ''; ?>>Beauty</option>
                                                    <option value="Food" <?php echo $brand['industry'] === 'Food' ? 'selected' : ''; ?>>Food & Beverage</option>
                                                    <option value="Travel" <?php echo $brand['industry'] === 'Travel' ? 'selected' : ''; ?>>Travel</option>
                                                    <option value="Fitness" <?php echo $brand['industry'] === 'Fitness' ? 'selected' : ''; ?>>Fitness</option>
                                                    <option value="Gaming" <?php echo $brand['industry'] === 'Gaming' ? 'selected' : ''; ?>>Gaming</option>
                                                    <option value="Education" <?php echo $brand['industry'] === 'Education' ? 'selected' : ''; ?>>Education</option>
                                                    <option value="Finance" <?php echo $brand['industry'] === 'Finance' ? 'selected' : ''; ?>>Finance</option>
                                                    <option value="Other" <?php echo $brand['industry'] === 'Other' ? 'selected' : ''; ?>>Other</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="budget" class="form-label">Monthly Budget (₹)</label>
                                                <input type="number" class="form-control" id="budget" name="budget" 
                                                       value="<?php echo $brand['budget']; ?>" min="0" step="1000">
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="description" class="form-label">Company Description</label>
                                        <textarea class="form-control" id="description" name="description" rows="4" 
                                                  placeholder="Tell us about your company..."><?php echo htmlspecialchars($brand['description']); ?></textarea>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="logo" class="form-label">Company Logo</label>
                                        <input type="file" class="form-control" id="logo" name="logo" accept="image/*">
                                        <?php if ($brand['logo']): ?>
                                            <div class="mt-2">
                                                <img src="../<?php echo htmlspecialchars($brand['logo']); ?>" 
                                                     alt="Current Logo" class="img-thumbnail" style="max-height: 100px;">
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <h6 class="mt-4 mb-3">Influencer Preferences</h6>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="min_followers" class="form-label">Min Followers *</label>
                                                <input type="number" class="form-control" id="min_followers" name="min_followers" 
                                                       value="<?php echo $brand['min_followers']; ?>" min="1000" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="max_followers" class="form-label">Max Followers *</label>
                                                <input type="number" class="form-control" id="max_followers" name="max_followers" 
                                                       value="<?php echo $brand['max_followers']; ?>" min="1000" required>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="preferred_categories" class="form-label">Preferred Categories</label>
                                        <input type="text" class="form-control" id="preferred_categories" name="preferred_categories" 
                                               value="<?php echo htmlspecialchars($brand['preferred_categories']); ?>" 
                                               placeholder="Fashion, Tech, Lifestyle (comma separated)">
                                    </div>
                                    
                                    <div class="d-grid">
                                        <button type="submit" name="update_profile" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>Update Profile
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4">
                        <!-- Change Password -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-lock me-2"></i>Change Password
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <div class="mb-3">
                                        <label for="current_password" class="form-label">Current Password</label>
                                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="new_password" class="form-label">New Password</label>
                                        <input type="password" class="form-control" id="new_password" name="new_password" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="confirm_password" class="form-label">Confirm New Password</label>
                                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                    </div>
                                    
                                    <div class="d-grid">
                                        <button type="submit" name="change_password" class="btn btn-warning">
                                            <i class="fas fa-key me-2"></i>Change Password
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleSidebar() {
    document.querySelector('.sidebar').classList.toggle('show');
}
</script>

<?php include '../includes/footer.php'; ?>
