<?php
// RealEarners Platform Configuration
session_start();

// Database Configuration
define('DB_HOST', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', '');
define('DB_NAME', 'realearners');

// Site Configuration
define('SITE_NAME', 'RealEarners');
define('SITE_URL', 'http://localhost/realearners');
define('SITE_EMAIL', '<EMAIL>');

// File Upload Configuration
define('UPLOAD_PATH', 'uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'gif', 'pdf']);

// Security Configuration
define('ENCRYPTION_KEY', 'your-secret-encryption-key-here');
define('SESSION_TIMEOUT', 3600); // 1 hour

// Payment Configuration
define('MIN_PAYOUT_AMOUNT', 100);
define('PLATFORM_COMMISSION', 10); // 10%

// Razorpay Configuration (Add your keys)
define('RAZORPAY_KEY_ID', 'your_razorpay_key_id');
define('RAZORPAY_KEY_SECRET', 'your_razorpay_key_secret');

// Error Reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Timezone
date_default_timezone_set('Asia/Kolkata');

// Auto-create upload directories
$upload_dirs = [
    'uploads/',
    'uploads/profiles/',
    'uploads/submissions/',
    'uploads/screenshots/',
    'uploads/brands/'
];

foreach ($upload_dirs as $dir) {
    if (!file_exists($dir)) {
        mkdir($dir, 0755, true);
    }
}

// Include additional functions first
require_once 'includes/functions.php';

// Database Connection Class
class Database {
    private static $instance = null;
    private $connection;

    private function __construct() {
        try {
            $this->connection = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME,
                DB_USERNAME,
                DB_PASSWORD,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false
                ]
            );
        } catch (PDOException $e) {
            die("Database connection failed: " . $e->getMessage());
        }
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function getConnection() {
        return $this->connection;
    }

    public function query($sql, $params = []) {
        $stmt = $this->connection->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    }

    public function fetch($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }

    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }

    public function insert($table, $data) {
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        $stmt = $this->connection->prepare($sql);
        $stmt->execute($data);
        
        return $this->connection->lastInsertId();
    }

    public function update($table, $data, $where, $whereParams = []) {
        $set = [];
        $params = [];

        // Build SET clause with unique parameter names
        $i = 0;
        foreach ($data as $key => $value) {
            $paramName = "set_param_" . $i;
            $set[] = "{$key} = :{$paramName}";
            $params[$paramName] = $value;
            $i++;
        }
        $setClause = implode(', ', $set);

        // Add WHERE parameters
        $j = 0;
        $whereClauseParams = [];
        foreach ($whereParams as $value) {
            $paramName = "where_param_" . $j;
            $whereClauseParams[] = $paramName;
            $params[$paramName] = $value;
            $j++;
        }

        // Replace ? with named parameters in WHERE clause
        $whereClause = $where;
        foreach ($whereClauseParams as $paramName) {
            $whereClause = preg_replace('/\?/', ':' . $paramName, $whereClause, 1);
        }

        $sql = "UPDATE {$table} SET {$setClause} WHERE {$whereClause}";

        $stmt = $this->connection->prepare($sql);
        return $stmt->execute($params);
    }

    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        $stmt = $this->connection->prepare($sql);
        return $stmt->execute($params);
    }
}

// Initialize database connection
$db = Database::getInstance();
?>
