<?php
try {
    $pdo = new PDO('mysql:host=localhost;dbname=realearners', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Creating test influencer...<br>";
    
    // Check if influencer already exists
    $existing = $pdo->prepare('SELECT id FROM influencers WHERE email = ?');
    $existing->execute(['<EMAIL>']);
    
    if ($existing->fetch()) {
        echo "Test influencer already exists!<br>";
    } else {
        // Create test influencer
        $password = password_hash('password', PASSWORD_DEFAULT);
        
        $stmt = $pdo->prepare('
            INSERT INTO influencers (
                username, email, password, full_name, phone, 
                instagram_handle, instagram_followers, instagram_verified,
                bio, category, rate_per_post, status, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ');
        
        $result = $stmt->execute([
            'testinfluencer',
            '<EMAIL>',
            $password,
            'Test Influencer',
            '+91-9876543210',
            'testinfluencer',
            10000,
            0,
            'Test influencer for platform testing',
            'Technology',
            500.00,
            'approved'
        ]);
        
        if ($result) {
            $influencer_id = $pdo->lastInsertId();
            echo "✅ Test influencer created successfully!<br>";
            echo "ID: $influencer_id<br>";
            echo "Email: <EMAIL><br>";
            echo "Password: password<br>";
            echo "Status: approved<br>";
        } else {
            echo "❌ Failed to create test influencer<br>";
        }
    }
    
    // Show all influencers
    echo "<h3>All Influencers:</h3>";
    $influencers = $pdo->query('SELECT id, username, email, full_name, status FROM influencers')->fetchAll();
    foreach ($influencers as $inf) {
        echo "ID: {$inf['id']}, Username: {$inf['username']}, Email: {$inf['email']}, Name: {$inf['full_name']}, Status: {$inf['status']}<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}
?>
