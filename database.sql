-- =====================================================
-- RealEarners Platform - Complete Database Schema
-- =====================================================

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- Create Database
CREATE DATABASE IF NOT EXISTS `realearners` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `realearners`;

-- =====================================================
-- 1. ADMIN TABLE
-- =====================================================
CREATE TABLE `admin` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `role` enum('super_admin','admin','moderator') DEFAULT 'admin',
  `status` enum('active','inactive','suspended') DEFAULT 'active',
  `last_login` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_admin_status` (`status`),
  KEY `idx_admin_role` (`role`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 2. USERS TABLE
-- =====================================================
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `instagram_handle` varchar(100) DEFAULT NULL,
  `upi_id` varchar(100) DEFAULT NULL,
  `total_earnings` decimal(10,2) DEFAULT 0.00,
  `available_balance` decimal(10,2) DEFAULT 0.00,
  `pending_balance` decimal(10,2) DEFAULT 0.00,
  `total_tasks_completed` int(11) DEFAULT 0,
  `instagram_verified` tinyint(1) DEFAULT 0,
  `verification_status` enum('pending','verified','rejected') DEFAULT 'pending',
  `status` enum('active','inactive','suspended') DEFAULT 'active',
  `last_login` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  UNIQUE KEY `instagram_handle` (`instagram_handle`),
  KEY `idx_users_status` (`status`),
  KEY `idx_users_verification` (`verification_status`),
  KEY `idx_users_instagram_verified` (`instagram_verified`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 3. INFLUENCERS TABLE
-- =====================================================
CREATE TABLE `influencers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `instagram_handle` varchar(100) NOT NULL,
  `instagram_followers` int(11) DEFAULT 0,
  `category` varchar(50) DEFAULT NULL,
  `bio` text DEFAULT NULL,
  `upi_id` varchar(100) DEFAULT NULL,
  `total_earnings` decimal(10,2) DEFAULT 0.00,
  `available_balance` decimal(10,2) DEFAULT 0.00,
  `pending_balance` decimal(10,2) DEFAULT 0.00,
  `total_campaigns_completed` int(11) DEFAULT 0,
  `instagram_verified` tinyint(1) DEFAULT 0,
  `verification_status` enum('pending','verified','rejected') DEFAULT 'pending',
  `status` enum('pending','active','inactive','suspended','rejected') DEFAULT 'pending',
  `last_login` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  UNIQUE KEY `instagram_handle` (`instagram_handle`),
  KEY `idx_influencers_status` (`status`),
  KEY `idx_influencers_verification` (`verification_status`),
  KEY `idx_influencers_category` (`category`),
  KEY `idx_influencers_followers` (`instagram_followers`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 4. BRANDS TABLE
-- =====================================================
CREATE TABLE `brands` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `company_name` varchar(100) NOT NULL,
  `contact_person` varchar(100) NOT NULL,
  `website` varchar(255) DEFAULT NULL,
  `industry` varchar(50) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `min_followers` int(11) DEFAULT 1000,
  `max_followers` int(11) DEFAULT 100000,
  `budget` decimal(10,2) DEFAULT 0.00,
  `total_spent` decimal(10,2) DEFAULT 0.00,
  `total_campaigns` int(11) DEFAULT 0,
  `status` enum('pending','active','inactive','suspended','rejected') DEFAULT 'pending',
  `last_login` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_brands_status` (`status`),
  KEY `idx_brands_industry` (`industry`),
  KEY `idx_brands_followers_range` (`min_followers`, `max_followers`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 5. ADMIN CAMPAIGNS TABLE
-- =====================================================
CREATE TABLE `admin_campaigns` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `instructions` text NOT NULL,
  `reward_amount` decimal(8,2) NOT NULL,
  `total_budget` decimal(10,2) NOT NULL,
  `max_participants` int(11) DEFAULT NULL,
  `current_participants` int(11) DEFAULT 0,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `target_audience` text DEFAULT NULL,
  `requirements` text DEFAULT NULL,
  `status` enum('draft','active','paused','completed','cancelled') DEFAULT 'draft',
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_admin_campaigns_status` (`status`),
  KEY `idx_admin_campaigns_dates` (`start_date`, `end_date`),
  KEY `fk_admin_campaigns_created_by` (`created_by`),
  CONSTRAINT `fk_admin_campaigns_created_by` FOREIGN KEY (`created_by`) REFERENCES `admin` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 6. BRAND CAMPAIGNS TABLE
-- =====================================================
CREATE TABLE `brand_campaigns` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `brand_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `instructions` text NOT NULL,
  `reward_amount` decimal(8,2) NOT NULL,
  `total_budget` decimal(10,2) NOT NULL,
  `max_participants` int(11) DEFAULT NULL,
  `current_participants` int(11) DEFAULT 0,
  `min_followers_required` int(11) DEFAULT 1000,
  `target_categories` text DEFAULT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `requirements` text DEFAULT NULL,
  `status` enum('draft','pending','active','paused','completed','cancelled','rejected') DEFAULT 'draft',
  `admin_notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_brand_campaigns_status` (`status`),
  KEY `idx_brand_campaigns_dates` (`start_date`, `end_date`),
  KEY `fk_brand_campaigns_brand_id` (`brand_id`),
  CONSTRAINT `fk_brand_campaigns_brand_id` FOREIGN KEY (`brand_id`) REFERENCES `brands` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 7. INFLUENCER CAMPAIGNS TABLE
-- =====================================================
CREATE TABLE `influencer_campaigns` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `influencer_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `instructions` text NOT NULL,
  `reward_amount` decimal(8,2) NOT NULL,
  `total_budget` decimal(10,2) NOT NULL,
  `max_participants` int(11) DEFAULT NULL,
  `current_participants` int(11) DEFAULT 0,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `requirements` text DEFAULT NULL,
  `status` enum('draft','pending','active','paused','completed','cancelled','rejected') DEFAULT 'draft',
  `admin_notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_influencer_campaigns_status` (`status`),
  KEY `idx_influencer_campaigns_dates` (`start_date`, `end_date`),
  KEY `fk_influencer_campaigns_influencer_id` (`influencer_id`),
  CONSTRAINT `fk_influencer_campaigns_influencer_id` FOREIGN KEY (`influencer_id`) REFERENCES `influencers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 8. USER TASKS TABLE
-- =====================================================
CREATE TABLE `user_tasks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `campaign_id` int(11) DEFAULT NULL,
  `admin_campaign_id` int(11) DEFAULT NULL,
  `brand_campaign_id` int(11) DEFAULT NULL,
  `influencer_campaign_id` int(11) DEFAULT NULL,
  `assigned_by` int(11) DEFAULT NULL,
  `title` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `instructions` text NOT NULL,
  `reward_amount` decimal(8,2) NOT NULL,
  `screenshot_url` varchar(500) DEFAULT NULL,
  `submission_text` text DEFAULT NULL,
  `status` enum('assigned','in_progress','submitted','approved','rejected','paid') DEFAULT 'assigned',
  `admin_notes` text DEFAULT NULL,
  `submitted_at` timestamp NULL DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `paid_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_tasks_user_id` (`user_id`),
  KEY `idx_user_tasks_status` (`status`),
  KEY `idx_user_tasks_assigned_by` (`assigned_by`),
  KEY `fk_user_tasks_admin_campaign` (`admin_campaign_id`),
  KEY `fk_user_tasks_brand_campaign` (`brand_campaign_id`),
  KEY `fk_user_tasks_influencer_campaign` (`influencer_campaign_id`),
  CONSTRAINT `fk_user_tasks_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_user_tasks_admin_campaign` FOREIGN KEY (`admin_campaign_id`) REFERENCES `admin_campaigns` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_user_tasks_brand_campaign` FOREIGN KEY (`brand_campaign_id`) REFERENCES `brand_campaigns` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_user_tasks_influencer_campaign` FOREIGN KEY (`influencer_campaign_id`) REFERENCES `influencer_campaigns` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 9. INFLUENCER USER ASSIGNMENTS TABLE
-- =====================================================
CREATE TABLE `influencer_user_assignments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `influencer_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `campaign_id` int(11) DEFAULT NULL,
  `task_title` varchar(255) NOT NULL,
  `task_description` text NOT NULL,
  `reward_amount` decimal(8,2) NOT NULL,
  `status` enum('pending','approved','rejected') DEFAULT 'pending',
  `admin_notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_assignments_influencer_id` (`influencer_id`),
  KEY `idx_assignments_user_id` (`user_id`),
  KEY `idx_assignments_status` (`status`),
  CONSTRAINT `fk_assignments_influencer_id` FOREIGN KEY (`influencer_id`) REFERENCES `influencers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_assignments_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 10. PAYOUTS TABLE
-- =====================================================
CREATE TABLE `payouts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `influencer_id` int(11) DEFAULT NULL,
  `user_type` enum('user','influencer') NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `upi_id` varchar(100) NOT NULL,
  `transaction_id` varchar(100) DEFAULT NULL,
  `status` enum('pending','processing','completed','failed','cancelled') DEFAULT 'pending',
  `admin_notes` text DEFAULT NULL,
  `processed_by` int(11) DEFAULT NULL,
  `processed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_payouts_user_id` (`user_id`),
  KEY `idx_payouts_influencer_id` (`influencer_id`),
  KEY `idx_payouts_status` (`status`),
  KEY `idx_payouts_user_type` (`user_type`),
  KEY `fk_payouts_processed_by` (`processed_by`),
  CONSTRAINT `fk_payouts_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_payouts_influencer_id` FOREIGN KEY (`influencer_id`) REFERENCES `influencers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_payouts_processed_by` FOREIGN KEY (`processed_by`) REFERENCES `admin` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 11. ACTIVITY LOGS TABLE
-- =====================================================
CREATE TABLE `activity_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `influencer_id` int(11) DEFAULT NULL,
  `admin_id` int(11) DEFAULT NULL,
  `brand_id` int(11) DEFAULT NULL,
  `user_type` enum('user','influencer','admin','brand') NOT NULL,
  `action` varchar(100) NOT NULL,
  `description` text NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_activity_logs_user_type` (`user_type`),
  KEY `idx_activity_logs_action` (`action`),
  KEY `idx_activity_logs_created_at` (`created_at`),
  KEY `fk_activity_logs_user_id` (`user_id`),
  KEY `fk_activity_logs_influencer_id` (`influencer_id`),
  KEY `fk_activity_logs_admin_id` (`admin_id`),
  KEY `fk_activity_logs_brand_id` (`brand_id`),
  CONSTRAINT `fk_activity_logs_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_activity_logs_influencer_id` FOREIGN KEY (`influencer_id`) REFERENCES `influencers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_activity_logs_admin_id` FOREIGN KEY (`admin_id`) REFERENCES `admin` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_activity_logs_brand_id` FOREIGN KEY (`brand_id`) REFERENCES `brands` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 12. INSTAGRAM VERIFICATIONS TABLE
-- =====================================================
CREATE TABLE `instagram_verifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `influencer_id` int(11) DEFAULT NULL,
  `user_type` enum('user','influencer') NOT NULL,
  `instagram_handle` varchar(100) NOT NULL,
  `verification_type` enum('follow_check','blue_check') NOT NULL,
  `required_accounts` text DEFAULT NULL,
  `status` enum('pending','verified','failed','suspended') DEFAULT 'pending',
  `verified_at` timestamp NULL DEFAULT NULL,
  `suspended_at` timestamp NULL DEFAULT NULL,
  `last_checked` timestamp NULL DEFAULT NULL,
  `check_count` int(11) DEFAULT 0,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_instagram_verifications_user_id` (`user_id`),
  KEY `idx_instagram_verifications_influencer_id` (`influencer_id`),
  KEY `idx_instagram_verifications_status` (`status`),
  KEY `idx_instagram_verifications_type` (`verification_type`),
  CONSTRAINT `fk_instagram_verifications_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_instagram_verifications_influencer_id` FOREIGN KEY (`influencer_id`) REFERENCES `influencers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 13. SETTINGS TABLE
-- =====================================================
CREATE TABLE `settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `setting_type` enum('text','number','boolean','json','email','url') DEFAULT 'text',
  `category` varchar(50) DEFAULT 'general',
  `description` text DEFAULT NULL,
  `is_public` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`),
  KEY `idx_settings_category` (`category`),
  KEY `idx_settings_public` (`is_public`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 14. USER BADGES TABLE
-- =====================================================
CREATE TABLE `user_badges` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `badge_name` varchar(100) NOT NULL,
  `badge_description` text DEFAULT NULL,
  `badge_icon` varchar(100) DEFAULT NULL,
  `badge_color` varchar(20) DEFAULT '#007bff',
  `earned_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `awarded_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_badges_user_id` (`user_id`),
  KEY `idx_user_badges_name` (`badge_name`),
  KEY `fk_user_badges_awarded_by` (`awarded_by`),
  CONSTRAINT `fk_user_badges_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_user_badges_awarded_by` FOREIGN KEY (`awarded_by`) REFERENCES `admin` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 15. BRAND INFLUENCER COLLABORATIONS TABLE
-- =====================================================
CREATE TABLE `brand_influencer_collaborations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `brand_id` int(11) NOT NULL,
  `influencer_id` int(11) NOT NULL,
  `campaign_id` int(11) DEFAULT NULL,
  `collaboration_type` enum('campaign','direct','partnership') DEFAULT 'campaign',
  `status` enum('pending','active','completed','cancelled') DEFAULT 'pending',
  `terms` text DEFAULT NULL,
  `budget` decimal(10,2) DEFAULT 0.00,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_collaborations_brand_id` (`brand_id`),
  KEY `idx_collaborations_influencer_id` (`influencer_id`),
  KEY `idx_collaborations_status` (`status`),
  KEY `fk_collaborations_campaign_id` (`campaign_id`),
  CONSTRAINT `fk_collaborations_brand_id` FOREIGN KEY (`brand_id`) REFERENCES `brands` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_collaborations_influencer_id` FOREIGN KEY (`influencer_id`) REFERENCES `influencers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_collaborations_campaign_id` FOREIGN KEY (`campaign_id`) REFERENCES `brand_campaigns` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 16. NOTIFICATIONS TABLE
-- =====================================================
CREATE TABLE `notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `influencer_id` int(11) DEFAULT NULL,
  `admin_id` int(11) DEFAULT NULL,
  `brand_id` int(11) DEFAULT NULL,
  `user_type` enum('user','influencer','admin','brand') NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `type` enum('info','success','warning','error','task','payment','verification') DEFAULT 'info',
  `is_read` tinyint(1) DEFAULT 0,
  `action_url` varchar(500) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_notifications_user_id` (`user_id`),
  KEY `idx_notifications_influencer_id` (`influencer_id`),
  KEY `idx_notifications_admin_id` (`admin_id`),
  KEY `idx_notifications_brand_id` (`brand_id`),
  KEY `idx_notifications_type` (`type`),
  KEY `idx_notifications_read` (`is_read`),
  CONSTRAINT `fk_notifications_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_notifications_influencer_id` FOREIGN KEY (`influencer_id`) REFERENCES `influencers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_notifications_admin_id` FOREIGN KEY (`admin_id`) REFERENCES `admin` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_notifications_brand_id` FOREIGN KEY (`brand_id`) REFERENCES `brands` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- SAMPLE DATA INSERTION
-- =====================================================

-- Insert Default Admin
INSERT INTO `admin` (`username`, `full_name`, `email`, `password`, `role`, `status`) VALUES
('admin', 'System Administrator', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'super_admin', 'active');

-- Insert Default Settings
INSERT INTO `settings` (`setting_key`, `setting_value`, `setting_type`, `category`, `description`, `is_public`) VALUES
('site_name', 'RealEarners', 'text', 'general', 'Website name', 1),
('site_description', 'Earn Money by Posting Ads on Social Media', 'text', 'general', 'Website description', 1),
('contact_email', '<EMAIL>', 'email', 'general', 'Contact email address', 1),
('instagram_required_accounts', '["thesyedabubakkar", "real_earners.in"]', 'json', 'instagram', 'Required Instagram accounts to follow', 0),
('min_payout_amount', '100', 'number', 'payment', 'Minimum payout amount', 0),
('default_task_reward', '50', 'number', 'tasks', 'Default task reward amount', 0),
('instagram_verification_enabled', '1', 'boolean', 'instagram', 'Enable Instagram verification', 0),
('auto_suspend_days', '2', 'number', 'instagram', 'Days before auto-suspension for unverified users', 0),
('platform_commission', '10', 'number', 'payment', 'Platform commission percentage', 0),
('max_daily_tasks', '5', 'number', 'tasks', 'Maximum daily tasks per user', 0);

-- Sample Users
INSERT INTO `users` (`username`, `full_name`, `email`, `password`, `phone`, `instagram_handle`, `upi_id`, `status`) VALUES
('john_doe', 'John Doe', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '+91-**********', 'john_doe_insta', 'john@paytm', 'active'),
('jane_smith', 'Jane Smith', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '+91-**********', 'jane_smith_insta', 'jane@phonepe', 'active'),
('mike_wilson', 'Mike Wilson', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '+91-9876543212', 'mike_wilson_insta', 'mike@gpay', 'pending');

-- Sample Influencers
INSERT INTO `influencers` (`username`, `full_name`, `email`, `password`, `phone`, `instagram_handle`, `instagram_followers`, `category`, `bio`, `upi_id`, `status`) VALUES
('sarah_lifestyle', 'Sarah Johnson', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '+91-9876543213', 'sarah_lifestyle', 15000, 'Lifestyle', 'Lifestyle and fashion influencer', 'sarah@paytm', 'active'),
('tech_guru_raj', 'Raj Patel', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '+91-9876543214', 'tech_guru_raj', 25000, 'Technology', 'Tech reviews and tutorials', 'raj@phonepe', 'active'),
('fitness_priya', 'Priya Sharma', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '+91-9876543215', 'fitness_priya', 12000, 'Fitness', 'Fitness and wellness coach', 'priya@gpay', 'pending');

-- Sample Brands
INSERT INTO `brands` (`username`, `full_name`, `email`, `password`, `phone`, `company_name`, `contact_person`, `website`, `industry`, `description`, `min_followers`, `max_followers`, `budget`, `status`) VALUES
('fashion_brand', 'Fashion Forward', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '+91-9876543216', 'Fashion Forward Pvt Ltd', 'Amit Kumar', 'https://fashionforward.com', 'Fashion', 'Premium fashion brand', 5000, 50000, 100000.00, 'active'),
('tech_startup', 'TechNova Solutions', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '+91-9876543217', 'TechNova Solutions', 'Neha Singh', 'https://technova.com', 'Technology', 'Innovative tech solutions', 10000, 100000, 200000.00, 'active');

-- Sample Admin Campaigns
INSERT INTO `admin_campaigns` (`title`, `description`, `instructions`, `reward_amount`, `total_budget`, `max_participants`, `start_date`, `end_date`, `status`, `created_by`) VALUES
('Instagram Story Promotion', 'Promote our platform on your Instagram story', 'Post our promotional content on your Instagram story and tag @real_earners.in', 75.00, 10000.00, 100, '2024-01-01', '2024-12-31', 'active', 1),
('Product Review Campaign', 'Review and promote featured products', 'Create authentic reviews of our featured products with honest feedback', 150.00, 15000.00, 50, '2024-01-01', '2024-12-31', 'active', 1);

-- Sample User Badges
INSERT INTO `user_badges` (`user_id`, `badge_name`, `badge_description`, `badge_icon`, `badge_color`, `awarded_by`) VALUES
(1, 'Early Adopter', 'One of the first users to join the platform', 'fas fa-star', '#FFD700', 1),
(1, 'Task Master', 'Completed 10 tasks successfully', 'fas fa-trophy', '#FF6B35', 1),
(2, 'Verified User', 'Successfully verified Instagram account', 'fas fa-check-circle', '#28A745', 1);

-- =====================================================
-- AUTO INCREMENT SETTINGS
-- =====================================================
ALTER TABLE `admin` AUTO_INCREMENT = 2;
ALTER TABLE `users` AUTO_INCREMENT = 4;
ALTER TABLE `influencers` AUTO_INCREMENT = 4;
ALTER TABLE `brands` AUTO_INCREMENT = 3;
ALTER TABLE `admin_campaigns` AUTO_INCREMENT = 3;
ALTER TABLE `brand_campaigns` AUTO_INCREMENT = 1;
ALTER TABLE `influencer_campaigns` AUTO_INCREMENT = 1;
ALTER TABLE `user_tasks` AUTO_INCREMENT = 1;
ALTER TABLE `influencer_user_assignments` AUTO_INCREMENT = 1;
ALTER TABLE `payouts` AUTO_INCREMENT = 1;
ALTER TABLE `activity_logs` AUTO_INCREMENT = 1;
ALTER TABLE `instagram_verifications` AUTO_INCREMENT = 1;
ALTER TABLE `settings` AUTO_INCREMENT = 11;
ALTER TABLE `user_badges` AUTO_INCREMENT = 4;
ALTER TABLE `brand_influencer_collaborations` AUTO_INCREMENT = 1;
ALTER TABLE `notifications` AUTO_INCREMENT = 1;

-- =====================================================
-- ADDITIONAL INDEXES FOR PERFORMANCE
-- =====================================================

-- Composite indexes for common queries
CREATE INDEX `idx_users_status_verification` ON `users` (`status`, `verification_status`);
CREATE INDEX `idx_influencers_status_followers` ON `influencers` (`status`, `instagram_followers`);
CREATE INDEX `idx_user_tasks_user_status` ON `user_tasks` (`user_id`, `status`);
CREATE INDEX `idx_payouts_user_type_status` ON `payouts` (`user_type`, `status`);
CREATE INDEX `idx_activity_logs_user_type_action` ON `activity_logs` (`user_type`, `action`);

-- Date-based indexes for reporting
CREATE INDEX `idx_users_created_date` ON `users` (`created_at`);
CREATE INDEX `idx_influencers_created_date` ON `influencers` (`created_at`);
CREATE INDEX `idx_user_tasks_created_date` ON `user_tasks` (`created_at`);
CREATE INDEX `idx_payouts_created_date` ON `payouts` (`created_at`);

-- Full-text search indexes
ALTER TABLE `admin_campaigns` ADD FULLTEXT(`title`, `description`);
ALTER TABLE `brand_campaigns` ADD FULLTEXT(`title`, `description`);
ALTER TABLE `influencer_campaigns` ADD FULLTEXT(`title`, `description`);

-- =====================================================
-- VIEWS FOR COMMON QUERIES
-- =====================================================

-- User Statistics View
CREATE VIEW `user_stats` AS
SELECT
    u.id,
    u.username,
    u.full_name,
    u.email,
    u.status,
    u.verification_status,
    u.total_earnings,
    u.available_balance,
    u.total_tasks_completed,
    COUNT(ut.id) as pending_tasks,
    u.created_at
FROM users u
LEFT JOIN user_tasks ut ON u.id = ut.user_id AND ut.status IN ('assigned', 'in_progress', 'submitted')
GROUP BY u.id;

-- Influencer Statistics View
CREATE VIEW `influencer_stats` AS
SELECT
    i.id,
    i.username,
    i.full_name,
    i.email,
    i.status,
    i.verification_status,
    i.instagram_followers,
    i.category,
    i.total_earnings,
    i.total_campaigns_completed,
    COUNT(ic.id) as active_campaigns,
    i.created_at
FROM influencers i
LEFT JOIN influencer_campaigns ic ON i.id = ic.influencer_id AND ic.status = 'active'
GROUP BY i.id;

-- Campaign Performance View
CREATE VIEW `campaign_performance` AS
SELECT
    'admin' as campaign_type,
    ac.id as campaign_id,
    ac.title,
    ac.reward_amount,
    ac.total_budget,
    ac.max_participants,
    ac.current_participants,
    ac.status,
    COUNT(ut.id) as total_tasks,
    SUM(CASE WHEN ut.status = 'completed' THEN 1 ELSE 0 END) as completed_tasks,
    SUM(CASE WHEN ut.status = 'approved' THEN ut.reward_amount ELSE 0 END) as total_paid,
    ac.created_at
FROM admin_campaigns ac
LEFT JOIN user_tasks ut ON ac.id = ut.admin_campaign_id
GROUP BY ac.id

UNION ALL

SELECT
    'brand' as campaign_type,
    bc.id as campaign_id,
    bc.title,
    bc.reward_amount,
    bc.total_budget,
    bc.max_participants,
    bc.current_participants,
    bc.status,
    COUNT(ut.id) as total_tasks,
    SUM(CASE WHEN ut.status = 'completed' THEN 1 ELSE 0 END) as completed_tasks,
    SUM(CASE WHEN ut.status = 'approved' THEN ut.reward_amount ELSE 0 END) as total_paid,
    bc.created_at
FROM brand_campaigns bc
LEFT JOIN user_tasks ut ON bc.id = ut.brand_campaign_id
GROUP BY bc.id;

-- =====================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- =====================================================

-- Update user earnings when task is approved
DELIMITER $$
CREATE TRIGGER `update_user_earnings`
AFTER UPDATE ON `user_tasks`
FOR EACH ROW
BEGIN
    IF NEW.status = 'approved' AND OLD.status != 'approved' THEN
        UPDATE users
        SET
            pending_balance = pending_balance + NEW.reward_amount,
            total_tasks_completed = total_tasks_completed + 1
        WHERE id = NEW.user_id;
    END IF;

    IF NEW.status = 'paid' AND OLD.status = 'approved' THEN
        UPDATE users
        SET
            pending_balance = pending_balance - NEW.reward_amount,
            available_balance = available_balance + NEW.reward_amount,
            total_earnings = total_earnings + NEW.reward_amount
        WHERE id = NEW.user_id;
    END IF;
END$$
DELIMITER ;

-- Update campaign participants count
DELIMITER $$
CREATE TRIGGER `update_campaign_participants`
AFTER INSERT ON `user_tasks`
FOR EACH ROW
BEGIN
    IF NEW.admin_campaign_id IS NOT NULL THEN
        UPDATE admin_campaigns
        SET current_participants = current_participants + 1
        WHERE id = NEW.admin_campaign_id;
    END IF;

    IF NEW.brand_campaign_id IS NOT NULL THEN
        UPDATE brand_campaigns
        SET current_participants = current_participants + 1
        WHERE id = NEW.brand_campaign_id;
    END IF;

    IF NEW.influencer_campaign_id IS NOT NULL THEN
        UPDATE influencer_campaigns
        SET current_participants = current_participants + 1
        WHERE id = NEW.influencer_campaign_id;
    END IF;
END$$
DELIMITER ;

-- =====================================================
-- COMPLETION
-- =====================================================

COMMIT;

-- =====================================================
-- DATABASE SETUP COMPLETE
-- =====================================================
--
-- Default Admin Login:
-- Username: admin
-- Password: password
--
-- This database includes:
-- - 16 Complete Tables with proper relationships
-- - Sample data for testing
-- - Performance indexes
-- - Useful views for reporting
-- - Automatic triggers for data consistency
-- - Full foreign key constraints
-- - Proper character encoding (UTF8MB4)
--
-- Total Tables: 16
-- Total Views: 3
-- Total Triggers: 2
-- Total Indexes: 50+
--
-- Ready for production use!
-- =====================================================
