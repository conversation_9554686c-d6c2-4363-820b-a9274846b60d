-- RealEarners Platform Database Schema
-- Complete A to Z Database Structure

CREATE DATABASE IF NOT EXISTS realearners;
USE realearners;

-- Users Table (Students, Youth, Housewives)
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    phone VARCHAR(15),
    instagram_handle VARCHAR(50),
    upi_id VARCHAR(100),
    profile_image VARCHAR(255),
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    total_earned DECIMAL(10,2) DEFAULT 0.00,
    pending_payout DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Influencers Table
CREATE TABLE influencers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    phone VARCHAR(15),
    instagram_handle VARCHAR(50) NOT NULL,
    instagram_followers INT DEFAULT 0,
    instagram_verified BOOLEAN DEFAULT FALSE,
    upi_id VARCHAR(100),
    profile_image VARCHAR(255),
    bio TEXT,
    category VARCHAR(50),
    rate_per_post DECIMAL(10,2) DEFAULT 0.00,
    status ENUM('pending', 'approved', 'rejected', 'suspended') DEFAULT 'pending',
    total_earned DECIMAL(10,2) DEFAULT 0.00,
    pending_payout DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Admin Table
CREATE TABLE admin (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('super_admin', 'admin', 'moderator') DEFAULT 'admin',
    permissions JSON,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Brands Table
CREATE TABLE brands (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    logo VARCHAR(255),
    description TEXT,
    website VARCHAR(255),
    contact_email VARCHAR(100),
    contact_phone VARCHAR(15),
    budget DECIMAL(12,2) DEFAULT 0.00,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Campaigns Table
CREATE TABLE campaigns (
    id INT PRIMARY KEY AUTO_INCREMENT,
    brand_id INT,
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    campaign_type ENUM('user_task', 'influencer_campaign', 'both') NOT NULL,
    target_audience VARCHAR(100),
    post_format ENUM('post', 'story', 'reel', 'any') DEFAULT 'any',
    reward_amount DECIMAL(10,2) NOT NULL,
    total_budget DECIMAL(12,2) NOT NULL,
    max_participants INT DEFAULT 100,
    current_participants INT DEFAULT 0,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    status ENUM('draft', 'active', 'paused', 'completed', 'cancelled') DEFAULT 'draft',
    requirements TEXT,
    hashtags VARCHAR(500),
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (brand_id) REFERENCES brands(id),
    FOREIGN KEY (created_by) REFERENCES admin(id)
);

-- User Tasks Table
CREATE TABLE user_tasks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    campaign_id INT NOT NULL,
    status ENUM('assigned', 'submitted', 'approved', 'rejected', 'paid') DEFAULT 'assigned',
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    submitted_at TIMESTAMP NULL,
    approved_at TIMESTAMP NULL,
    reward_amount DECIMAL(10,2) NOT NULL,
    admin_notes TEXT,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (campaign_id) REFERENCES campaigns(id)
);

-- User Submissions Table
CREATE TABLE user_submissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    task_id INT NOT NULL,
    user_id INT NOT NULL,
    campaign_id INT NOT NULL,
    post_url VARCHAR(500),
    screenshot_path VARCHAR(255),
    caption TEXT,
    hashtags_used VARCHAR(500),
    submission_notes TEXT,
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES user_tasks(id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (campaign_id) REFERENCES campaigns(id)
);

-- Influencer Campaigns Table
CREATE TABLE influencer_campaigns (
    id INT PRIMARY KEY AUTO_INCREMENT,
    influencer_id INT NOT NULL,
    campaign_id INT NOT NULL,
    status ENUM('invited', 'accepted', 'declined', 'submitted', 'approved', 'rejected', 'paid') DEFAULT 'invited',
    negotiated_rate DECIMAL(10,2),
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    accepted_at TIMESTAMP NULL,
    submitted_at TIMESTAMP NULL,
    approved_at TIMESTAMP NULL,
    admin_notes TEXT,
    FOREIGN KEY (influencer_id) REFERENCES influencers(id),
    FOREIGN KEY (campaign_id) REFERENCES campaigns(id)
);

-- Influencer Submissions Table
CREATE TABLE influencer_submissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    campaign_assignment_id INT NOT NULL,
    influencer_id INT NOT NULL,
    campaign_id INT NOT NULL,
    post_url VARCHAR(500),
    screenshot_path VARCHAR(255),
    caption TEXT,
    hashtags_used VARCHAR(500),
    reach_count INT DEFAULT 0,
    likes_count INT DEFAULT 0,
    comments_count INT DEFAULT 0,
    shares_count INT DEFAULT 0,
    submission_notes TEXT,
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (campaign_assignment_id) REFERENCES influencer_campaigns(id),
    FOREIGN KEY (influencer_id) REFERENCES influencers(id),
    FOREIGN KEY (campaign_id) REFERENCES campaigns(id)
);

-- Payouts Table
CREATE TABLE payouts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_type ENUM('user', 'influencer') NOT NULL,
    user_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    upi_id VARCHAR(100) NOT NULL,
    transaction_id VARCHAR(100),
    payment_method ENUM('upi', 'razorpay', 'manual') DEFAULT 'upi',
    status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    processed_by INT,
    processed_at TIMESTAMP NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (processed_by) REFERENCES admin(id)
);

-- Analytics Table
CREATE TABLE analytics (
    id INT PRIMARY KEY AUTO_INCREMENT,
    date DATE NOT NULL,
    total_users INT DEFAULT 0,
    total_influencers INT DEFAULT 0,
    active_campaigns INT DEFAULT 0,
    total_submissions INT DEFAULT 0,
    total_payouts DECIMAL(12,2) DEFAULT 0.00,
    platform_revenue DECIMAL(12,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_date (date)
);

-- Settings Table
CREATE TABLE settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description TEXT,
    updated_by INT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (updated_by) REFERENCES admin(id)
);

-- Notifications Table
CREATE TABLE notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_type ENUM('user', 'influencer', 'admin') NOT NULL,
    user_id INT NOT NULL,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert Default Admin
INSERT INTO admin (username, email, password, full_name, role) VALUES 
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Platform Admin', 'super_admin');

-- Insert Default Settings
INSERT INTO settings (setting_key, setting_value, description) VALUES 
('site_name', 'RealEarners', 'Website Name'),
('site_email', '<EMAIL>', 'Support Email'),
('razorpay_key', '', 'Razorpay API Key'),
('razorpay_secret', '', 'Razorpay Secret Key'),
('min_payout_amount', '100', 'Minimum Payout Amount'),
('platform_commission', '10', 'Platform Commission Percentage'),
('whatsapp_api_key', '', 'WhatsApp API Key for Notifications'),
('email_smtp_host', '', 'SMTP Host for Email'),
('email_smtp_username', '', 'SMTP Username'),
('email_smtp_password', '', 'SMTP Password');
