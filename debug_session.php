<?php
session_start();

echo "<h2>Session Debug Information</h2>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h2>Database Check</h2>";
try {
    $pdo = new PDO('mysql:host=localhost;dbname=realearners', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check if influencers table exists
    $result = $pdo->query('SHOW TABLES LIKE "influencers"');
    if ($result->rowCount() > 0) {
        echo "✅ Influencers table exists<br>";
        
        // Check influencers count
        $count = $pdo->query('SELECT COUNT(*) as count FROM influencers')->fetch()['count'];
        echo "📊 Total influencers: $count<br>";
        
        if ($count > 0) {
            echo "<h3>Sample Influencers:</h3>";
            $influencers = $pdo->query('SELECT id, username, full_name, status FROM influencers LIMIT 5')->fetchAll();
            foreach ($influencers as $inf) {
                echo "ID: {$inf['id']}, Username: {$inf['username']}, Name: {$inf['full_name']}, Status: {$inf['status']}<br>";
            }
        }
    } else {
        echo "❌ Influencers table does not exist<br>";
    }
    
    // Check if user is logged in as influencer
    if (isset($_SESSION['user_type']) && $_SESSION['user_type'] === 'influencer') {
        $user_id = $_SESSION['user_id'];
        echo "<h3>Current Session User ID: $user_id</h3>";
        
        $influencer = $pdo->prepare('SELECT * FROM influencers WHERE id = ?');
        $influencer->execute([$user_id]);
        $inf_data = $influencer->fetch();
        
        if ($inf_data) {
            echo "✅ Influencer found in database<br>";
            echo "Status: {$inf_data['status']}<br>";
            echo "Name: {$inf_data['full_name']}<br>";
        } else {
            echo "❌ Influencer not found with ID: $user_id<br>";
        }
    } else {
        echo "ℹ️ Not logged in as influencer<br>";
        echo "Current user type: " . ($_SESSION['user_type'] ?? 'not set') . "<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}
?>
