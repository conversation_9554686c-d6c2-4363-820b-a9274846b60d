<?php
// Admin Navigation Component
// This file contains the standard admin navigation that can be included in all admin pages

function render_admin_navigation($current_page = '') {
    ?>
    <nav class="nav flex-column">
        <a class="nav-link <?php echo $current_page === 'dashboard' ? 'active' : ''; ?>" href="dashboard.php">
            <i class="fas fa-tachometer-alt"></i>Dashboard
        </a>
        <a class="nav-link <?php echo $current_page === 'users' ? 'active' : ''; ?>" href="users.php">
            <i class="fas fa-users"></i>Users
        </a>
        <a class="nav-link <?php echo $current_page === 'influencers' ? 'active' : ''; ?>" href="influencers.php">
            <i class="fas fa-star"></i>Influencers
        </a>
        <a class="nav-link <?php echo $current_page === 'brands' ? 'active' : ''; ?>" href="brands.php">
            <i class="fas fa-building"></i>Brands
        </a>
        <a class="nav-link <?php echo $current_page === 'campaigns' ? 'active' : ''; ?>" href="campaigns.php">
            <i class="fas fa-bullhorn"></i>Campaigns
        </a>
        <a class="nav-link <?php echo $current_page === 'submissions' ? 'active' : ''; ?>" href="submissions.php">
            <i class="fas fa-clipboard-check"></i>Submissions
        </a>
        <a class="nav-link <?php echo $current_page === 'assignments' ? 'active' : ''; ?>" href="assignments.php">
            <i class="fas fa-user-check"></i>Assignments
        </a>
        <a class="nav-link <?php echo $current_page === 'payouts' ? 'active' : ''; ?>" href="payouts.php">
            <i class="fas fa-money-bill-wave"></i>Payouts
        </a>
        
        <!-- Instagram & Verification Section -->
        <hr class="my-2 text-light">
        <small class="text-light px-3 mb-2">Instagram & Verification</small>
        
        <a class="nav-link <?php echo $current_page === 'instagram_verifications' ? 'active' : ''; ?>" href="instagram_verifications.php">
            <i class="fab fa-instagram"></i>Instagram Verifications
        </a>
        <a class="nav-link <?php echo $current_page === 'influencer_follow_check' ? 'active' : ''; ?>" href="influencer_follow_check.php">
            <i class="fas fa-user-check"></i>Follow Status Check
        </a>
        <a class="nav-link <?php echo $current_page === 'instagram_blue_check' ? 'active' : ''; ?>" href="instagram_blue_check.php">
            <i class="fas fa-check-circle"></i>Blue Check Management
        </a>
        <a class="nav-link <?php echo $current_page === 'user_verification_status' ? 'active' : ''; ?>" href="user_verification_status.php">
            <i class="fas fa-clipboard-check"></i>User Follow Status
        </a>
        <a class="nav-link <?php echo $current_page === 'suspended_users' ? 'active' : ''; ?>" href="suspended_users.php">
            <i class="fas fa-ban"></i>Suspended Users
        </a>
        
        <!-- Badge Management Section -->
        <hr class="my-2 text-light">
        <small class="text-light px-3 mb-2">Badge Management</small>
        
        <a class="nav-link <?php echo $current_page === 'badge_management' ? 'active' : ''; ?>" href="badge_management.php">
            <i class="fas fa-award"></i>Badge Management
        </a>
        <a class="nav-link <?php echo $current_page === 'user_badge_assignment' ? 'active' : ''; ?>" href="user_badge_assignment.php">
            <i class="fas fa-user-tag"></i>Assign Badges
        </a>
        
        <!-- System Section -->
        <hr class="my-2 text-light">
        <small class="text-light px-3 mb-2">System</small>
        
        <a class="nav-link <?php echo $current_page === 'settings' ? 'active' : ''; ?>" href="settings.php">
            <i class="fas fa-cog"></i>Settings
        </a>
        <a class="nav-link" href="../auth/logout.php">
            <i class="fas fa-sign-out-alt"></i>Logout
        </a>
    </nav>
    <?php
}

// Function to get admin sidebar HTML
function get_admin_sidebar($current_page = '') {
    ob_start();
    ?>
    <div class="col-lg-3 col-xl-2 sidebar p-0">
        <div class="p-3">
            <h5 class="text-white mb-4">
                <i class="fas fa-shield-alt me-2"></i>Admin Panel
            </h5>
            
            <?php render_admin_navigation($current_page); ?>
        </div>
    </div>
    <?php
    return ob_get_clean();
}

// Function to get admin top bar HTML
function get_admin_topbar($page_title, $page_description = '', $action_button = '') {
    ob_start();
    ?>
    <div class="bg-white shadow-sm p-3 mb-4">
        <div class="row align-items-center">
            <div class="col">
                <h4 class="mb-0"><?php echo htmlspecialchars($page_title); ?></h4>
                <?php if ($page_description): ?>
                    <small class="text-muted"><?php echo htmlspecialchars($page_description); ?></small>
                <?php endif; ?>
            </div>
            <?php if ($action_button): ?>
                <div class="col-auto">
                    <?php echo $action_button; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
    <?php
    return ob_get_clean();
}

// Function to display success/error messages
function display_admin_messages() {
    if (isset($_SESSION['success'])) {
        echo '<div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i>' . $_SESSION['success'] . '
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
              </div>';
        unset($_SESSION['success']);
    }
    
    if (isset($_SESSION['error'])) {
        echo '<div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-circle me-2"></i>' . $_SESSION['error'] . '
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
              </div>';
        unset($_SESSION['error']);
    }
    
    if (isset($_SESSION['warning'])) {
        echo '<div class="alert alert-warning alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i>' . $_SESSION['warning'] . '
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
              </div>';
        unset($_SESSION['warning']);
    }
    
    if (isset($_SESSION['info'])) {
        echo '<div class="alert alert-info alert-dismissible fade show">
                <i class="fas fa-info-circle me-2"></i>' . $_SESSION['info'] . '
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
              </div>';
        unset($_SESSION['info']);
    }
}
?>
