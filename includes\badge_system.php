<?php

class BadgeSystem {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Get all available badges
     */
    public function getAllBadges($active_only = true) {
        $where = $active_only ? "WHERE is_active = 1" : "";
        return $this->db->fetchAll("
            SELECT * FROM badges 
            $where 
            ORDER BY badge_type, name
        ");
    }
    
    /**
     * Get badges by type
     */
    public function getBadgesByType($type, $active_only = true) {
        $where = $active_only ? "AND is_active = 1" : "";
        return $this->db->fetchAll("
            SELECT * FROM badges 
            WHERE badge_type = ? $where 
            ORDER BY name
        ", [$type]);
    }
    
    /**
     * Get user's badges
     */
    public function getUserBadges($user_id, $user_type, $active_only = true) {
        $where = $active_only ? "AND ub.is_active = 1" : "";
        return $this->db->fetchAll("
            SELECT b.*, ub.assigned_at, ub.notes, ub.assigned_by
            FROM user_badges ub
            JOIN badges b ON ub.badge_id = b.id
            WHERE ub.user_id = ? AND ub.user_type = ? $where
            ORDER BY ub.assigned_at DESC
        ", [$user_id, $user_type]);
    }
    
    /**
     * Assign badge to user
     */
    public function assignBadge($user_id, $user_type, $badge_id, $assigned_by, $notes = '') {
        try {
            // Check if badge already assigned
            $existing = $this->db->fetch("
                SELECT id FROM user_badges 
                WHERE user_id = ? AND user_type = ? AND badge_id = ?
            ", [$user_id, $user_type, $badge_id]);
            
            if ($existing) {
                // Reactivate if exists but inactive
                $this->db->update('user_badges', [
                    'is_active' => 1,
                    'assigned_by' => $assigned_by,
                    'assigned_at' => date('Y-m-d H:i:s'),
                    'notes' => $notes
                ], 'id = ?', [$existing['id']]);
                return true;
            }
            
            // Insert new badge assignment
            $this->db->insert('user_badges', [
                'user_id' => $user_id,
                'user_type' => $user_type,
                'badge_id' => $badge_id,
                'assigned_by' => $assigned_by,
                'notes' => $notes
            ]);
            
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Remove badge from user
     */
    public function removeBadge($user_id, $user_type, $badge_id) {
        try {
            $this->db->update('user_badges', [
                'is_active' => 0
            ], 'user_id = ? AND user_type = ? AND badge_id = ?', [$user_id, $user_type, $badge_id]);
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Get badge by ID
     */
    public function getBadge($badge_id) {
        return $this->db->fetch("SELECT * FROM badges WHERE id = ?", [$badge_id]);
    }
    
    /**
     * Create new badge
     */
    public function createBadge($name, $description, $icon, $color, $badge_type) {
        try {
            $this->db->insert('badges', [
                'name' => $name,
                'description' => $description,
                'icon' => $icon,
                'color' => $color,
                'badge_type' => $badge_type
            ]);
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Update badge
     */
    public function updateBadge($badge_id, $name, $description, $icon, $color, $badge_type, $is_active = 1) {
        try {
            $this->db->update('badges', [
                'name' => $name,
                'description' => $description,
                'icon' => $icon,
                'color' => $color,
                'badge_type' => $badge_type,
                'is_active' => $is_active,
                'updated_at' => date('Y-m-d H:i:s')
            ], 'id = ?', [$badge_id]);
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Delete badge
     */
    public function deleteBadge($badge_id) {
        try {
            // First remove all user assignments
            $this->db->delete('user_badges', 'badge_id = ?', [$badge_id]);
            // Then delete the badge
            $this->db->delete('badges', 'id = ?', [$badge_id]);
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Get users with specific badge
     */
    public function getUsersWithBadge($badge_id, $user_type = null) {
        $where = $user_type ? "AND ub.user_type = ?" : "";
        $params = $user_type ? [$badge_id, $user_type] : [$badge_id];
        
        return $this->db->fetchAll("
            SELECT ub.*, b.name as badge_name, b.icon, b.color,
                   CASE 
                       WHEN ub.user_type = 'user' THEN u.full_name
                       WHEN ub.user_type = 'influencer' THEN i.full_name
                       WHEN ub.user_type = 'brand' THEN br.company_name
                   END as user_name,
                   CASE 
                       WHEN ub.user_type = 'user' THEN u.username
                       WHEN ub.user_type = 'influencer' THEN i.username
                       WHEN ub.user_type = 'brand' THEN br.username
                   END as username
            FROM user_badges ub
            JOIN badges b ON ub.badge_id = b.id
            LEFT JOIN users u ON ub.user_id = u.id AND ub.user_type = 'user'
            LEFT JOIN influencers i ON ub.user_id = i.id AND ub.user_type = 'influencer'
            LEFT JOIN brands br ON ub.user_id = br.id AND ub.user_type = 'brand'
            WHERE ub.badge_id = ? AND ub.is_active = 1 $where
            ORDER BY ub.assigned_at DESC
        ", $params);
    }
    
    /**
     * Get badge statistics
     */
    public function getBadgeStats() {
        return [
            'total_badges' => $this->db->fetch("SELECT COUNT(*) as count FROM badges WHERE is_active = 1")['count'],
            'total_assignments' => $this->db->fetch("SELECT COUNT(*) as count FROM user_badges WHERE is_active = 1")['count'],
            'users_with_badges' => $this->db->fetch("SELECT COUNT(DISTINCT user_id, user_type) as count FROM user_badges WHERE is_active = 1")['count'],
            'by_type' => $this->db->fetchAll("
                SELECT badge_type, COUNT(*) as count 
                FROM badges 
                WHERE is_active = 1 
                GROUP BY badge_type
            ")
        ];
    }
    
    /**
     * Render badge HTML
     */
    public function renderBadge($badge, $size = 'sm') {
        $size_class = $size === 'lg' ? 'badge-lg' : ($size === 'md' ? 'badge-md' : '');
        return sprintf(
            '<span class="badge bg-%s %s" title="%s"><i class="%s me-1"></i>%s</span>',
            $badge['color'],
            $size_class,
            htmlspecialchars($badge['description']),
            $badge['icon'],
            htmlspecialchars($badge['name'])
        );
    }
    
    /**
     * Get available badge types
     */
    public function getBadgeTypes() {
        return [
            'achievement' => 'Achievement',
            'status' => 'Status',
            'special' => 'Special',
            'verification' => 'Verification',
            'tier' => 'Tier'
        ];
    }
    
    /**
     * Get available badge colors
     */
    public function getBadgeColors() {
        return [
            'primary' => 'Primary (Blue)',
            'secondary' => 'Secondary (Gray)',
            'success' => 'Success (Green)',
            'danger' => 'Danger (Red)',
            'warning' => 'Warning (Yellow)',
            'info' => 'Info (Cyan)',
            'light' => 'Light',
            'dark' => 'Dark'
        ];
    }
}

// Helper function to format badge display
function format_badge($badge, $size = 'sm') {
    $badge_system = new BadgeSystem();
    return $badge_system->renderBadge($badge, $size);
}
?>
