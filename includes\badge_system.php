<?php
/**
 * Badge System for RealEarners
 * Manages user badges and achievements
 */

class BadgeSystem {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Award a badge to a user
     */
    public function awardBadge($user_id, $user_type, $badge_type, $awarded_by = null) {
        $badge_config = $this->getBadgeConfig($badge_type);
        
        if (!$badge_config) {
            return false;
        }
        
        // Check if user already has this badge
        $existing = $this->db->fetch(
            "SELECT id FROM user_badges WHERE user_id = ? AND user_type = ? AND badge_type = ?",
            [$user_id, $user_type, $badge_type]
        );
        
        if ($existing) {
            return false; // Badge already awarded
        }
        
        try {
            $this->db->insert('user_badges', [
                'user_id' => $user_id,
                'user_type' => $user_type,
                'badge_type' => $badge_type,
                'badge_name' => $badge_config['name'],
                'badge_description' => $badge_config['description'],
                'badge_icon' => $badge_config['icon'],
                'badge_color' => $badge_config['color'],
                'awarded_by' => $awarded_by
            ]);
            
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Get user's badges
     */
    public function getUserBadges($user_id, $user_type) {
        return $this->db->fetchAll(
            "SELECT * FROM user_badges WHERE user_id = ? AND user_type = ? ORDER BY earned_at DESC",
            [$user_id, $user_type]
        );
    }
    
    /**
     * Get badge configuration
     */
    private function getBadgeConfig($badge_type) {
        $badges = [
            'verified_follower' => [
                'name' => 'Verified Follower',
                'description' => 'Successfully followed both required Instagram accounts',
                'icon' => 'fab fa-instagram',
                'color' => '#E4405F'
            ],
            'top_earner' => [
                'name' => 'Top Earner',
                'description' => 'Earned over ₹10,000 in total',
                'icon' => 'fas fa-trophy',
                'color' => '#FFD700'
            ],
            'early_adopter' => [
                'name' => 'Early Adopter',
                'description' => 'One of the first 100 users to join RealEarners',
                'icon' => 'fas fa-rocket',
                'color' => '#6366F1'
            ],
            'loyal_member' => [
                'name' => 'Loyal Member',
                'description' => 'Active member for over 6 months',
                'icon' => 'fas fa-heart',
                'color' => '#EF4444'
            ],
            'high_performer' => [
                'name' => 'High Performer',
                'description' => 'Completed 50+ tasks with 95%+ approval rate',
                'icon' => 'fas fa-star',
                'color' => '#10B981'
            ]
        ];
        
        return $badges[$badge_type] ?? null;
    }
    
    /**
     * Check and award automatic badges
     */
    public function checkAutomaticBadges($user_id, $user_type) {
        // Check for top earner badge
        if ($user_type === 'user') {
            $user = $this->db->fetch("SELECT total_earned FROM users WHERE id = ?", [$user_id]);
            if ($user && $user['total_earned'] >= 10000) {
                $this->awardBadge($user_id, $user_type, 'top_earner');
            }
            
            // Check for high performer badge
            $stats = $this->db->fetch("
                SELECT 
                    COUNT(*) as total_tasks,
                    SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_tasks
                FROM user_tasks 
                WHERE user_id = ?
            ", [$user_id]);
            
            if ($stats && $stats['total_tasks'] >= 50) {
                $approval_rate = ($stats['approved_tasks'] / $stats['total_tasks']) * 100;
                if ($approval_rate >= 95) {
                    $this->awardBadge($user_id, $user_type, 'high_performer');
                }
            }
        }
        
        // Check for loyal member badge
        $user_table = $user_type === 'user' ? 'users' : ($user_type === 'influencer' ? 'influencers' : 'brands');
        $user_data = $this->db->fetch("SELECT created_at FROM {$user_table} WHERE id = ?", [$user_id]);
        
        if ($user_data) {
            $months_active = (time() - strtotime($user_data['created_at'])) / (30 * 24 * 60 * 60);
            if ($months_active >= 6) {
                $this->awardBadge($user_id, $user_type, 'loyal_member');
            }
        }
    }
    
    /**
     * Render badge HTML
     */
    public function renderBadge($badge, $size = 'normal') {
        $size_class = $size === 'small' ? 'badge-sm' : ($size === 'large' ? 'badge-lg' : '');
        
        return sprintf(
            '<span class="badge-custom %s" style="background-color: %s; color: white;" title="%s">
                <i class="%s me-1"></i>%s
            </span>',
            $size_class,
            htmlspecialchars($badge['badge_color']),
            htmlspecialchars($badge['badge_description']),
            htmlspecialchars($badge['badge_icon']),
            htmlspecialchars($badge['badge_name'])
        );
    }
    
    /**
     * Get badge statistics
     */
    public function getBadgeStats() {
        return $this->db->fetchAll("
            SELECT 
                badge_type,
                badge_name,
                COUNT(*) as count
            FROM user_badges 
            GROUP BY badge_type, badge_name
            ORDER BY count DESC
        ");
    }
}

/**
 * Helper function to get user badges
 */
function get_user_badges($user_id, $user_type) {
    $badge_system = new BadgeSystem();
    return $badge_system->getUserBadges($user_id, $user_type);
}

/**
 * Helper function to render badges
 */
function render_user_badges($user_id, $user_type, $size = 'normal') {
    $badges = get_user_badges($user_id, $user_type);
    $badge_system = new BadgeSystem();
    
    $html = '';
    foreach ($badges as $badge) {
        $html .= $badge_system->renderBadge($badge, $size) . ' ';
    }
    
    return $html;
}
?>
