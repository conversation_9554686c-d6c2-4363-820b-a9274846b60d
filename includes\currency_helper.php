<?php
/**
 * Currency Helper Functions
 * Handles currency formatting and conversion for RealEarners platform
 */

class CurrencyHelper {
    private static $settings = null;
    private static $currencies = null;
    
    /**
     * Load currency settings from database
     */
    private static function loadSettings() {
        if (self::$settings === null) {
            global $conn;
            
            // Load general currency settings
            $stmt = $conn->prepare("SELECT setting_key, setting_value FROM settings WHERE category = 'payment' AND setting_key LIKE 'currency_%'");
            $stmt->execute();
            $result = $stmt->get_result();
            
            self::$settings = [];
            while ($row = $result->fetch_assoc()) {
                self::$settings[$row['setting_key']] = $row['setting_value'];
            }
            
            // Set defaults if not found
            if (empty(self::$settings)) {
                self::$settings = [
                    'currency_code' => 'INR',
                    'currency_symbol' => '₹',
                    'currency_name' => 'Indian Rupee',
                    'currency_position' => 'before',
                    'decimal_places' => '2',
                    'thousand_separator' => ',',
                    'decimal_separator' => '.',
                    'currency_format' => '{symbol}{amount}'
                ];
            }
        }
    }
    
    /**
     * Load currency data from database
     */
    private static function loadCurrencies() {
        if (self::$currencies === null) {
            global $conn;
            
            $stmt = $conn->prepare("SELECT * FROM currency_settings WHERE is_active = 1 ORDER BY is_default DESC, currency_name ASC");
            $stmt->execute();
            $result = $stmt->get_result();
            
            self::$currencies = [];
            while ($row = $result->fetch_assoc()) {
                self::$currencies[$row['currency_code']] = $row;
            }
        }
    }
    
    /**
     * Format amount with currency symbol
     */
    public static function format($amount, $currency_code = null) {
        self::loadSettings();
        self::loadCurrencies();
        
        // Use default currency if not specified
        if ($currency_code === null) {
            $currency_code = self::$settings['currency_code'];
        }
        
        // Get currency data
        $currency = isset(self::$currencies[$currency_code]) ? self::$currencies[$currency_code] : null;
        
        if ($currency) {
            $symbol = $currency['currency_symbol'];
            $decimal_places = $currency['decimal_places'];
            $thousand_sep = $currency['thousand_separator'];
            $decimal_sep = $currency['decimal_separator'];
            $position = $currency['symbol_position'];
        } else {
            // Fallback to settings
            $symbol = self::$settings['currency_symbol'];
            $decimal_places = (int)self::$settings['decimal_places'];
            $thousand_sep = self::$settings['thousand_separator'];
            $decimal_sep = self::$settings['decimal_separator'];
            $position = self::$settings['currency_position'];
        }
        
        // Format the number
        $formatted_amount = number_format($amount, $decimal_places, $decimal_sep, $thousand_sep);
        
        // Add currency symbol
        if ($position === 'before') {
            return $symbol . $formatted_amount;
        } else {
            return $formatted_amount . $symbol;
        }
    }
    
    /**
     * Format amount for display in tables/cards
     */
    public static function formatShort($amount, $currency_code = null) {
        if ($amount >= 10000000) { // 1 Crore
            return self::format($amount / 10000000, $currency_code) . 'Cr';
        } elseif ($amount >= 100000) { // 1 Lakh
            return self::format($amount / 100000, $currency_code) . 'L';
        } elseif ($amount >= 1000) { // 1 Thousand
            return self::format($amount / 1000, $currency_code) . 'K';
        } else {
            return self::format($amount, $currency_code);
        }
    }
    
    /**
     * Get currency symbol only
     */
    public static function getSymbol($currency_code = null) {
        self::loadSettings();
        self::loadCurrencies();
        
        if ($currency_code === null) {
            $currency_code = self::$settings['currency_code'];
        }
        
        $currency = isset(self::$currencies[$currency_code]) ? self::$currencies[$currency_code] : null;
        return $currency ? $currency['currency_symbol'] : self::$settings['currency_symbol'];
    }
    
    /**
     * Get currency code
     */
    public static function getCode() {
        self::loadSettings();
        return self::$settings['currency_code'];
    }
    
    /**
     * Get currency name
     */
    public static function getName($currency_code = null) {
        self::loadCurrencies();
        
        if ($currency_code === null) {
            self::loadSettings();
            $currency_code = self::$settings['currency_code'];
        }
        
        $currency = isset(self::$currencies[$currency_code]) ? self::$currencies[$currency_code] : null;
        return $currency ? $currency['currency_name'] : 'Indian Rupee';
    }
    
    /**
     * Get all active currencies
     */
    public static function getAllCurrencies() {
        self::loadCurrencies();
        return self::$currencies;
    }
    
    /**
     * Convert amount between currencies
     */
    public static function convert($amount, $from_currency, $to_currency) {
        self::loadCurrencies();
        
        if ($from_currency === $to_currency) {
            return $amount;
        }
        
        $from = isset(self::$currencies[$from_currency]) ? self::$currencies[$from_currency] : null;
        $to = isset(self::$currencies[$to_currency]) ? self::$currencies[$to_currency] : null;
        
        if (!$from || !$to) {
            return $amount; // Cannot convert
        }
        
        // Convert to USD first, then to target currency
        $usd_amount = $amount / $from['exchange_rate_usd'];
        $converted_amount = $usd_amount * $to['exchange_rate_usd'];
        
        return round($converted_amount, $to['decimal_places']);
    }
    
    /**
     * Format amount for input fields (no currency symbol)
     */
    public static function formatInput($amount, $currency_code = null) {
        self::loadSettings();
        self::loadCurrencies();
        
        if ($currency_code === null) {
            $currency_code = self::$settings['currency_code'];
        }
        
        $currency = isset(self::$currencies[$currency_code]) ? self::$currencies[$currency_code] : null;
        $decimal_places = $currency ? $currency['decimal_places'] : (int)self::$settings['decimal_places'];
        
        return number_format($amount, $decimal_places, '.', '');
    }
    
    /**
     * Parse amount from input (remove formatting)
     */
    public static function parseInput($input, $currency_code = null) {
        // Remove all non-numeric characters except decimal point
        $cleaned = preg_replace('/[^0-9.]/', '', $input);
        return floatval($cleaned);
    }
    
    /**
     * Get minimum payout amount
     */
    public static function getMinPayoutAmount() {
        global $conn;
        
        $stmt = $conn->prepare("SELECT setting_value FROM settings WHERE setting_key = 'min_payout_amount'");
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        
        return $row ? floatval($row['setting_value']) : 100;
    }
    
    /**
     * Get default task reward amount
     */
    public static function getDefaultTaskReward() {
        global $conn;
        
        $stmt = $conn->prepare("SELECT setting_value FROM settings WHERE setting_key = 'default_task_reward'");
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        
        return $row ? floatval($row['setting_value']) : 50;
    }
    
    /**
     * Update currency settings
     */
    public static function updateSettings($settings) {
        global $conn;
        
        foreach ($settings as $key => $value) {
            $stmt = $conn->prepare("UPDATE settings SET setting_value = ? WHERE setting_key = ?");
            $stmt->bind_param("ss", $value, $key);
            $stmt->execute();
        }
        
        // Clear cache
        self::$settings = null;
        self::$currencies = null;
    }
    
    /**
     * Add or update currency
     */
    public static function updateCurrency($currency_data) {
        global $conn;
        
        $stmt = $conn->prepare("INSERT INTO currency_settings (currency_code, currency_name, currency_symbol, symbol_position, decimal_places, thousand_separator, decimal_separator, exchange_rate_usd, is_active, is_default, country_code) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE currency_name = VALUES(currency_name), currency_symbol = VALUES(currency_symbol), symbol_position = VALUES(symbol_position), decimal_places = VALUES(decimal_places), thousand_separator = VALUES(thousand_separator), decimal_separator = VALUES(decimal_separator), exchange_rate_usd = VALUES(exchange_rate_usd), is_active = VALUES(is_active), is_default = VALUES(is_default), country_code = VALUES(country_code)");
        
        $stmt->bind_param("ssssississi", 
            $currency_data['currency_code'],
            $currency_data['currency_name'],
            $currency_data['currency_symbol'],
            $currency_data['symbol_position'],
            $currency_data['decimal_places'],
            $currency_data['thousand_separator'],
            $currency_data['decimal_separator'],
            $currency_data['exchange_rate_usd'],
            $currency_data['is_active'],
            $currency_data['is_default'],
            $currency_data['country_code']
        );
        
        $stmt->execute();
        
        // Clear cache
        self::$currencies = null;
    }
    
    /**
     * Set default currency
     */
    public static function setDefaultCurrency($currency_code) {
        global $conn;
        
        // Remove default from all currencies
        $stmt = $conn->prepare("UPDATE currency_settings SET is_default = 0");
        $stmt->execute();
        
        // Set new default
        $stmt = $conn->prepare("UPDATE currency_settings SET is_default = 1 WHERE currency_code = ?");
        $stmt->bind_param("s", $currency_code);
        $stmt->execute();
        
        // Update settings table
        $stmt = $conn->prepare("UPDATE settings SET setting_value = ? WHERE setting_key = 'currency_code'");
        $stmt->bind_param("s", $currency_code);
        $stmt->execute();
        
        // Clear cache
        self::$settings = null;
        self::$currencies = null;
    }
}

// Helper functions for easy access
function formatCurrency($amount, $currency_code = null) {
    return CurrencyHelper::format($amount, $currency_code);
}

function formatCurrencyShort($amount, $currency_code = null) {
    return CurrencyHelper::formatShort($amount, $currency_code);
}

function getCurrencySymbol($currency_code = null) {
    return CurrencyHelper::getSymbol($currency_code);
}

function getCurrencyCode() {
    return CurrencyHelper::getCode();
}

function getCurrencyName($currency_code = null) {
    return CurrencyHelper::getName($currency_code);
}
?>
