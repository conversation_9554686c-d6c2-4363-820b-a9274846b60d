    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <!-- Chart.js for Analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Mobile Responsive JavaScript -->
    <script src="<?php echo SITE_URL; ?>/assets/js/mobile-responsive.js"></script>

    <!-- Custom JavaScript -->
    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
        
        // Mobile sidebar toggle (enhanced with new responsive system)
        function toggleSidebar() {
            if (window.mobileResponsive) {
                window.mobileResponsive.toggleSidebar();
            } else {
                // Fallback for legacy support
                $('.sidebar').toggleClass('show');
            }
        }

        // Enhanced mobile sidebar handling
        $(document).click(function(e) {
            if ($(window).width() <= 1023) {
                if (!$(e.target).closest('.sidebar, .mobile-nav-toggle, .sidebar-overlay').length) {
                    if (window.mobileResponsive) {
                        window.mobileResponsive.closeSidebar();
                    } else {
                        $('.sidebar').removeClass('show');
                    }
                }
            }
        });

        // Ensure mobile responsive is initialized
        if (typeof window.mobileResponsive === 'undefined') {
            // Fallback initialization if mobile-responsive.js hasn't loaded yet
            setTimeout(function() {
                if (typeof MobileResponsive !== 'undefined') {
                    window.mobileResponsive = new MobileResponsive();
                }
            }, 100);
        }

        // Fallback mobile toggle creation for mobile devices
        setTimeout(function() {
            if ($(window).width() <= 1023 && $('body').hasClass('has-sidebar') && !$('.mobile-nav-toggle').length) {
                const toggle = $('<button class="mobile-nav-toggle" type="button" aria-label="Toggle navigation"><i class="fas fa-bars"></i></button>');
                $('body').prepend(toggle);

                toggle.on('click', function(e) {
                    e.preventDefault();
                    if (window.mobileResponsive) {
                        window.mobileResponsive.toggleSidebar();
                    }
                });
            }
        }, 200);
        
        // Confirm delete actions
        $('.delete-btn').click(function(e) {
            if (!confirm('Are you sure you want to delete this item? This action cannot be undone.')) {
                e.preventDefault();
            }
        });
        
        // Auto-refresh notifications count
        function updateNotificationCount() {
            if (typeof updateNotifications === 'function') {
                updateNotifications();
            }
        }
        
        // Refresh every 30 seconds
        setInterval(updateNotificationCount, 30000);
        
        // Form validation
        $('.needs-validation').on('submit', function(e) {
            if (!this.checkValidity()) {
                e.preventDefault();
                e.stopPropagation();
            }
            $(this).addClass('was-validated');
        });
        
        // File upload preview
        $('.file-input').change(function() {
            const file = this.files[0];
            const preview = $(this).siblings('.file-preview');
            
            if (file && file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.html('<img src="' + e.target.result + '" class="img-thumbnail" style="max-width: 200px;">');
                };
                reader.readAsDataURL(file);
            } else {
                preview.html('<p class="text-muted">File selected: ' + (file ? file.name : 'None') + '</p>');
            }
        });
        
        // Copy to clipboard functionality
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                showToast('Copied to clipboard!', 'success');
            });
        }
        
        // Show toast notification
        function showToast(message, type = 'info') {
            const toastHtml = `
                <div class="toast align-items-center text-white bg-${type} border-0 position-fixed" 
                     style="top: 20px; right: 20px; z-index: 1060;" role="alert">
                    <div class="d-flex">
                        <div class="toast-body">${message}</div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" 
                                data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `;
            
            $('body').append(toastHtml);
            const toast = new bootstrap.Toast($('.toast').last());
            toast.show();
            
            // Remove toast element after it's hidden
            $('.toast').last().on('hidden.bs.toast', function() {
                $(this).remove();
            });
        }
        
        // AJAX form submission
        $('.ajax-form').on('submit', function(e) {
            e.preventDefault();
            
            const form = $(this);
            const submitBtn = form.find('button[type="submit"]');
            const originalText = submitBtn.html();
            
            // Show loading state
            submitBtn.html('<i class="fas fa-spinner fa-spin"></i> Processing...').prop('disabled', true);
            
            $.ajax({
                url: form.attr('action'),
                method: form.attr('method') || 'POST',
                data: new FormData(this),
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        showToast(response.message, 'success');
                        if (response.redirect) {
                            setTimeout(() => window.location.href = response.redirect, 1500);
                        }
                        if (response.reload) {
                            setTimeout(() => window.location.reload(), 1500);
                        }
                    } else {
                        showToast(response.message || 'An error occurred', 'danger');
                    }
                },
                error: function() {
                    showToast('Network error. Please try again.', 'danger');
                },
                complete: function() {
                    // Restore button state
                    submitBtn.html(originalText).prop('disabled', false);
                }
            });
        });
        
        // Data tables initialization
        if (typeof $.fn.DataTable !== 'undefined') {
            $('.data-table').DataTable({
                responsive: true,
                pageLength: 25,
                order: [[0, 'desc']],
                language: {
                    search: "Search:",
                    lengthMenu: "Show _MENU_ entries",
                    info: "Showing _START_ to _END_ of _TOTAL_ entries",
                    paginate: {
                        first: "First",
                        last: "Last",
                        next: "Next",
                        previous: "Previous"
                    }
                }
            });
        }
        
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
        
        // Initialize popovers
        var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });
        
        // Auto-save form data to localStorage
        $('.auto-save').on('input change', function() {
            const formId = $(this).closest('form').attr('id');
            if (formId) {
                const formData = {};
                $(this).closest('form').find('input, textarea, select').each(function() {
                    if ($(this).attr('name')) {
                        formData[$(this).attr('name')] = $(this).val();
                    }
                });
                localStorage.setItem('form_' + formId, JSON.stringify(formData));
            }
        });
        
        // Restore form data from localStorage
        $('form[id]').each(function() {
            const formId = $(this).attr('id');
            const savedData = localStorage.getItem('form_' + formId);
            
            if (savedData) {
                try {
                    const formData = JSON.parse(savedData);
                    const form = $(this);
                    
                    Object.keys(formData).forEach(function(name) {
                        form.find('[name="' + name + '"]').val(formData[name]);
                    });
                } catch (e) {
                    console.log('Error restoring form data:', e);
                }
            }
        });
        
        // Clear saved form data on successful submission
        $('.auto-save').closest('form').on('submit', function() {
            const formId = $(this).attr('id');
            if (formId) {
                localStorage.removeItem('form_' + formId);
            }
        });
    </script>
    
    <?php if (isset($additional_js)): ?>
        <?php echo $additional_js; ?>
    <?php endif; ?>
    
</body>
</html>
