<?php
// Additional helper functions

/**
 * Sanitize input data
 */
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

/**
 * Redirect to URL
 */
function redirect($url) {
    header("Location: " . $url);
    exit();
}

/**
 * Check if user is logged in
 */
function is_logged_in() {
    return isset($_SESSION['user_id']) && isset($_SESSION['user_type']);
}

/**
 * Get current user type
 */
function get_user_type() {
    return $_SESSION['user_type'] ?? null;
}

/**
 * Get current user ID
 */
function get_user_id() {
    return $_SESSION['user_id'] ?? null;
}

/**
 * Require login and check user type
 */
function require_login($allowed_types = []) {
    if (!is_logged_in()) {
        redirect('auth/login.php');
    }

    if (!empty($allowed_types) && !in_array(get_user_type(), $allowed_types)) {
        redirect('index.php');
    }
}

/**
 * Format currency
 */
function format_currency($amount) {
    return '₹' . number_format($amount, 2);
}

/**
 * Time ago function
 */
function time_ago($datetime) {
    $time = time() - strtotime($datetime);

    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' minutes ago';
    if ($time < 86400) return floor($time/3600) . ' hours ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';
    if ($time < 31536000) return floor($time/2592000) . ' months ago';
    return floor($time/31536000) . ' years ago';
}

/**
 * Upload file function
 */
function upload_file($file, $directory = 'uploads/') {
    if (!isset($file['error']) || is_array($file['error'])) {
        throw new RuntimeException('Invalid parameters.');
    }

    switch ($file['error']) {
        case UPLOAD_ERR_OK:
            break;
        case UPLOAD_ERR_NO_FILE:
            throw new RuntimeException('No file sent.');
        case UPLOAD_ERR_INI_SIZE:
        case UPLOAD_ERR_FORM_SIZE:
            throw new RuntimeException('Exceeded filesize limit.');
        default:
            throw new RuntimeException('Unknown errors.');
    }

    if ($file['size'] > MAX_FILE_SIZE) {
        throw new RuntimeException('Exceeded filesize limit.');
    }

    $finfo = new finfo(FILEINFO_MIME_TYPE);
    $ext = array_search(
        $finfo->file($file['tmp_name']),
        [
            'jpg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'pdf' => 'application/pdf'
        ],
        true
    );

    if (false === $ext) {
        throw new RuntimeException('Invalid file format.');
    }

    $filename = sprintf('%s.%s', sha1_file($file['tmp_name']), $ext);
    $filepath = $directory . $filename;

    if (!move_uploaded_file($file['tmp_name'], $filepath)) {
        throw new RuntimeException('Failed to move uploaded file.');
    }

    return $filepath;
}

/**
 * Send email notification
 */
function send_email($to, $subject, $message, $from_name = 'RealEarners') {
    $headers = [
        'MIME-Version: 1.0',
        'Content-type: text/html; charset=UTF-8',
        'From: ' . $from_name . ' <' . SITE_EMAIL . '>',
        'Reply-To: ' . SITE_EMAIL,
        'X-Mailer: PHP/' . phpversion()
    ];
    
    return mail($to, $subject, $message, implode("\r\n", $headers));
}

/**
 * Generate random string
 */
function generate_random_string($length = 10) {
    return substr(str_shuffle(str_repeat($x='0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', ceil($length/strlen($x)) )),1,$length);
}

/**
 * Validate Instagram URL
 */
function validate_instagram_url($url) {
    return preg_match('/^https?:\/\/(www\.)?instagram\.com\/p\/[a-zA-Z0-9_-]+\/?$/', $url);
}

/**
 * Get user's IP address
 */
function get_user_ip() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        return $_SERVER['REMOTE_ADDR'];
    }
}

/**
 * Log activity
 */
function log_activity($user_type, $user_id, $action, $details = '') {
    $db = Database::getInstance();

    try {
        // Check if activity_logs table exists, create if not
        $table_exists = $db->fetch("SHOW TABLES LIKE 'activity_logs'");

        if (!$table_exists) {
            // Create the activity_logs table
            $db->query("
                CREATE TABLE activity_logs (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    user_type ENUM('user', 'influencer', 'admin', 'brand') NOT NULL,
                    user_id INT NOT NULL,
                    action VARCHAR(100) NOT NULL,
                    details TEXT,
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ");
        }

        $db->insert('activity_logs', [
            'user_type' => $user_type,
            'user_id' => $user_id,
            'action' => $action,
            'details' => $details,
            'ip_address' => get_user_ip(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
    } catch (Exception $e) {
        // Log error but don't break the application
        error_log('Failed to log activity: ' . $e->getMessage());
    }
}

/**
 * Check if user can perform action (rate limiting)
 */
function check_rate_limit($action, $limit = 5, $window = 3600) {
    $db = Database::getInstance();
    $ip = get_user_ip();

    try {
        $count = $db->fetch("
            SELECT COUNT(*) as count
            FROM activity_logs
            WHERE ip_address = ?
            AND action = ?
            AND created_at > DATE_SUB(NOW(), INTERVAL ? SECOND)
        ", [$ip, $action, $window])['count'];

        return $count < $limit;
    } catch (Exception $e) {
        // If table doesn't exist or other error, allow the action
        return true;
    }
}

/**
 * Generate CSRF token
 */
function generate_csrf_token() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Verify CSRF token
 */
function verify_csrf_token($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Get setting value
 */
function get_setting($key, $default = '') {
    static $settings = null;
    
    if ($settings === null) {
        $db = Database::getInstance();
        $results = $db->fetchAll("SELECT setting_key, setting_value FROM settings");
        $settings = [];
        foreach ($results as $row) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }
    }
    
    return $settings[$key] ?? $default;
}

/**
 * Update setting value
 */
function update_setting($key, $value) {
    $db = Database::getInstance();
    
    try {
        $existing = $db->fetch("SELECT id FROM settings WHERE setting_key = ?", [$key]);
        
        if ($existing) {
            $db->update('settings', ['setting_value' => $value], 'setting_key = ?', [$key]);
        } else {
            $db->insert('settings', [
                'setting_key' => $key,
                'setting_value' => $value
            ]);
        }
        
        return true;
    } catch (Exception $e) {
        return false;
    }
}

/**
 * Send WhatsApp notification (if configured)
 */
function send_whatsapp_notification($phone, $message) {
    $api_key = get_setting('whatsapp_api_key');
    $api_url = get_setting('whatsapp_api_url');
    
    if (empty($api_key) || empty($api_url)) {
        return false;
    }
    
    // Implementation depends on WhatsApp API provider
    // This is a placeholder for the actual implementation
    
    return true;
}

/**
 * Compress and resize image
 */
function compress_image($source, $destination, $quality = 80, $max_width = 800, $max_height = 600) {
    $info = getimagesize($source);
    
    if ($info === false) {
        return false;
    }
    
    $mime = $info['mime'];
    
    switch ($mime) {
        case 'image/jpeg':
            $image = imagecreatefromjpeg($source);
            break;
        case 'image/png':
            $image = imagecreatefrompng($source);
            break;
        case 'image/gif':
            $image = imagecreatefromgif($source);
            break;
        default:
            return false;
    }
    
    $width = imagesx($image);
    $height = imagesy($image);
    
    // Calculate new dimensions
    $ratio = min($max_width / $width, $max_height / $height);
    
    if ($ratio < 1) {
        $new_width = $width * $ratio;
        $new_height = $height * $ratio;
        
        $new_image = imagecreatetruecolor($new_width, $new_height);
        
        // Preserve transparency for PNG and GIF
        if ($mime == 'image/png' || $mime == 'image/gif') {
            imagecolortransparent($new_image, imagecolorallocatealpha($new_image, 0, 0, 0, 127));
            imagealphablending($new_image, false);
            imagesavealpha($new_image, true);
        }
        
        imagecopyresampled($new_image, $image, 0, 0, 0, 0, $new_width, $new_height, $width, $height);
        imagedestroy($image);
        $image = $new_image;
    }
    
    // Save compressed image
    switch ($mime) {
        case 'image/jpeg':
            return imagejpeg($image, $destination, $quality);
        case 'image/png':
            return imagepng($image, $destination, 9 - ($quality / 10));
        case 'image/gif':
            return imagegif($image, $destination);
    }
    
    imagedestroy($image);
    return false;
}

/**
 * Generate thumbnail
 */
function generate_thumbnail($source, $destination, $width = 150, $height = 150) {
    return compress_image($source, $destination, 80, $width, $height);
}

/**
 * Clean old files
 */
function clean_old_files($directory, $days = 30) {
    $files = glob($directory . '/*');
    $now = time();
    $deleted = 0;
    
    foreach ($files as $file) {
        if (is_file($file)) {
            if ($now - filemtime($file) >= 60 * 60 * 24 * $days) {
                if (unlink($file)) {
                    $deleted++;
                }
            }
        }
    }
    
    return $deleted;
}

/**
 * Get file size in human readable format
 */
function human_filesize($size, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB', 'PB');
    
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    
    return round($size, $precision) . ' ' . $units[$i];
}

/**
 * Validate phone number
 */
function validate_phone($phone) {
    // Remove all non-digit characters
    $phone = preg_replace('/[^0-9]/', '', $phone);
    
    // Check if it's a valid Indian mobile number
    return preg_match('/^[6-9]\d{9}$/', $phone);
}

/**
 * Format phone number
 */
function format_phone($phone) {
    $phone = preg_replace('/[^0-9]/', '', $phone);
    
    if (strlen($phone) == 10) {
        return '+91' . $phone;
    }
    
    return $phone;
}

/**
 * Check if user is online
 */
function is_user_online($user_id, $user_type = 'user') {
    $db = Database::getInstance();
    
    $last_activity = $db->fetch("
        SELECT last_activity 
        FROM " . ($user_type === 'user' ? 'users' : ($user_type === 'influencer' ? 'influencers' : 'admin')) . " 
        WHERE id = ?
    ", [$user_id]);
    
    if ($last_activity && $last_activity['last_activity']) {
        $last_time = strtotime($last_activity['last_activity']);
        $current_time = time();
        
        // Consider user online if last activity was within 5 minutes
        return ($current_time - $last_time) < 300;
    }
    
    return false;
}

/**
 * Update user last activity
 */
function update_last_activity($user_id, $user_type = 'user') {
    $db = Database::getInstance();
    
    try {
        $table = $user_type === 'user' ? 'users' : ($user_type === 'influencer' ? 'influencers' : 'admin');
        $db->update($table, ['last_activity' => date('Y-m-d H:i:s')], 'id = ?', [$user_id]);
    } catch (Exception $e) {
        // Ignore errors
    }
}

/**
 * Get platform statistics
 */
function get_platform_stats() {
    $db = Database::getInstance();
    
    return [
        'total_users' => $db->fetch("SELECT COUNT(*) as count FROM users")['count'],
        'total_influencers' => $db->fetch("SELECT COUNT(*) as count FROM influencers")['count'],
        'total_campaigns' => $db->fetch("SELECT COUNT(*) as count FROM campaigns")['count'],
        'total_earnings' => $db->fetch("SELECT SUM(total_earned) as total FROM users")['total'] ?? 0,
        'total_payouts' => $db->fetch("SELECT SUM(amount) as total FROM payouts WHERE status = 'completed'")['total'] ?? 0
    ];
}

/**
 * Generate QR code for UPI payment
 */
function generate_upi_qr($upi_id, $amount, $name = 'RealEarners') {
    $upi_url = "upi://pay?pa={$upi_id}&pn={$name}&am={$amount}&cu=INR";
    
    // You can use a QR code library here
    // For now, return the UPI URL
    return $upi_url;
}

/**
 * Backup database
 */
function backup_database() {
    $backup_file = 'backups/backup_' . date('Y-m-d_H-i-s') . '.sql';
    
    if (!is_dir('backups')) {
        mkdir('backups', 0755, true);
    }
    
    $command = "mysqldump --user=" . DB_USERNAME . " --password=" . DB_PASSWORD . " --host=" . DB_HOST . " " . DB_NAME . " > " . $backup_file;
    
    exec($command, $output, $return_var);
    
    return $return_var === 0 ? $backup_file : false;
}
?>
