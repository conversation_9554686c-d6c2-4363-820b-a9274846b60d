<?php
/**
 * Instagram API Integration for RealEarners
 * Fetches live Instagram posts and profile data
 */

class InstagramAPI {
    private $access_token;
    private $cache_duration = 3600; // 1 hour cache
    
    public function __construct() {
        // Instagram Basic Display API Access Token
        // You need to get this from Facebook Developers Console
        $this->access_token = 'YOUR_INSTAGRAM_ACCESS_TOKEN_HERE';
    }
    
    /**
     * Get Instagram profile information
     */
    public function getProfile($username) {
        $cache_key = "instagram_profile_" . $username;
        $cached_data = $this->getCache($cache_key);
        
        if ($cached_data) {
            return $cached_data;
        }
        
        // For demo purposes, return mock data
        // In production, replace with actual API calls
        $profile_data = $this->getMockProfileData($username);
        
        $this->setCache($cache_key, $profile_data);
        return $profile_data;
    }
    
    /**
     * Get Instagram posts for a user
     */
    public function getPosts($username, $limit = 6) {
        $cache_key = "instagram_posts_" . $username . "_" . $limit;
        $cached_data = $this->getCache($cache_key);
        
        if ($cached_data) {
            return $cached_data;
        }
        
        // For demo purposes, return mock data
        // In production, replace with actual API calls
        $posts_data = $this->getMockPostsData($username, $limit);
        
        $this->setCache($cache_key, $posts_data);
        return $posts_data;
    }
    
    /**
     * Mock profile data (replace with real API calls)
     */
    private function getMockProfileData($username) {
        $profiles = [
            'thesyedabubakkar' => [
                'username' => 'thesyedabubakkar',
                'full_name' => 'Syed Abu Bakkar',
                'bio' => 'Entrepreneur 🚀 | Building RealEarners 💰 | Helping people earn through social media 📱 | Follow for business tips & success stories ✨',
                'followers_count' => 125000,
                'following_count' => 1250,
                'media_count' => 2500,
                'profile_picture' => 'https://via.placeholder.com/150x150/667eea/ffffff?text=SA',
                'is_verified' => true,
                'website' => 'https://real_earners.in'
            ],
            'real_earners.in' => [
                'username' => 'real_earners.in',
                'full_name' => 'RealEarners Official',
                'bio' => '💰 Earn Money Online | 📱 Social Media Marketing | 🎯 Join 10K+ Earners | 🚀 Start Your Journey Today | 💸 Daily Payouts',
                'followers_count' => 85000,
                'following_count' => 500,
                'media_count' => 1800,
                'profile_picture' => 'https://via.placeholder.com/150x150/f093fb/ffffff?text=RE',
                'is_verified' => false,
                'website' => 'https://real_earners.in'
            ]
        ];
        
        return $profiles[$username] ?? null;
    }
    
    /**
     * Mock posts data (replace with real API calls)
     */
    private function getMockPostsData($username, $limit) {
        $posts = [];
        
        if ($username === 'thesyedabubakkar') {
            $posts = [
                [
                    'id' => '1',
                    'media_type' => 'IMAGE',
                    'media_url' => 'https://via.placeholder.com/400x400/667eea/ffffff?text=Success+Story+%F0%9F%8E%89',
                    'caption' => 'Another incredible success story! 🎉 One of our RealEarners users just earned ₹25,000 this month! 💰 This is what happens when you stay consistent and follow the strategies. Who\'s ready to be next? 🚀\n\n#RealEarners #SuccessStory #EarnMoney #OnlineEarning #Motivation #BusinessTips',
                    'like_count' => 3247,
                    'comments_count' => 127,
                    'timestamp' => date('c', strtotime('-2 hours')),
                    'permalink' => 'https://instagram.com/p/example1'
                ],
                [
                    'id' => '2',
                    'media_type' => 'IMAGE',
                    'media_url' => 'https://via.placeholder.com/400x400/764ba2/ffffff?text=Pro+Tips+%F0%9F%92%A1',
                    'caption' => '💡 PRO TIP: Post your ads during peak hours (7-9 PM) for maximum engagement! This simple trick can increase your earnings by 40%! 📈\n\nTime your posts right and watch your income grow! ⏰💰\n\n#ProTips #MaximizeEarnings #SocialMediaTips #RealEarners #OnlineSuccess',
                    'like_count' => 7156,
                    'comments_count' => 89,
                    'timestamp' => date('c', strtotime('-1 day')),
                    'permalink' => 'https://instagram.com/p/example2'
                ],
                [
                    'id' => '3',
                    'media_type' => 'IMAGE',
                    'media_url' => 'https://via.placeholder.com/400x400/667eea/ffffff?text=Behind+The+Scenes+%F0%9F%8E%AC',
                    'caption' => 'Behind the scenes at RealEarners HQ! 🎬 Working late to bring you the best earning opportunities. The grind never stops when you\'re building something amazing! 💪\n\n#BehindTheScenes #Entrepreneur #HardWork #RealEarners #BuildingDreams',
                    'like_count' => 4892,
                    'comments_count' => 156,
                    'timestamp' => date('c', strtotime('-2 days')),
                    'permalink' => 'https://instagram.com/p/example3'
                ]
            ];
        } elseif ($username === 'real_earners.in') {
            $posts = [
                [
                    'id' => '4',
                    'media_type' => 'IMAGE',
                    'media_url' => 'https://via.placeholder.com/400x400/f093fb/ffffff?text=New+Campaign+%F0%9F%9A%80',
                    'caption' => '🚀 NEW CAMPAIGN ALERT! Earn ₹200 per post for fashion brand promotion! Limited slots available - only 50 spots left! 👗✨\n\nRequirements:\n✅ 1000+ followers\n✅ Active engagement\n✅ Fashion content\n\nApply now! Link in bio 🔗\n\n#NewCampaign #FashionBrand #EarnNow #LimitedSlots #RealEarners',
                    'like_count' => 5892,
                    'comments_count' => 234,
                    'timestamp' => date('c', strtotime('-5 hours')),
                    'permalink' => 'https://instagram.com/p/example4'
                ],
                [
                    'id' => '5',
                    'media_type' => 'IMAGE',
                    'media_url' => 'https://via.placeholder.com/400x400/667eea/ffffff?text=Payment+Proof+%F0%9F%92%B8',
                    'caption' => '💸 PAYMENT PROOF FRIDAY! Here are this week\'s top earners who received their payments instantly! 🏆\n\n🥇 @user1 - ₹15,000\n🥈 @user2 - ₹12,500\n🥉 @user3 - ₹10,800\n\nYour turn next week? Join now! 🚀\n\n#PaymentProof #TopEarners #InstantPayments #RealEarners #WeeklyWinners',
                    'like_count' => 8234,
                    'comments_count' => 445,
                    'timestamp' => date('c', strtotime('-1 day')),
                    'permalink' => 'https://instagram.com/p/example5'
                ],
                [
                    'id' => '6',
                    'media_type' => 'IMAGE',
                    'media_url' => 'https://via.placeholder.com/400x400/764ba2/ffffff?text=Tutorial+%F0%9F%93%9A',
                    'caption' => '📚 TUTORIAL TUESDAY: How to create the perfect ad post that gets approved every time! 💯\n\n1️⃣ Use high-quality images\n2️⃣ Write engaging captions\n3️⃣ Include required hashtags\n4️⃣ Post at optimal times\n5️⃣ Engage with comments\n\nSave this post for reference! 📌\n\n#TutorialTuesday #AdTips #GetApproved #RealEarners #SocialMediaTips',
                    'like_count' => 6547,
                    'comments_count' => 178,
                    'timestamp' => date('c', strtotime('-3 days')),
                    'permalink' => 'https://instagram.com/p/example6'
                ]
            ];
        }
        
        return array_slice($posts, 0, $limit);
    }
    
    /**
     * Cache management
     */
    private function getCache($key) {
        $cache_file = sys_get_temp_dir() . '/instagram_cache_' . md5($key) . '.json';
        
        if (file_exists($cache_file) && (time() - filemtime($cache_file)) < $this->cache_duration) {
            return json_decode(file_get_contents($cache_file), true);
        }
        
        return null;
    }
    
    private function setCache($key, $data) {
        $cache_file = sys_get_temp_dir() . '/instagram_cache_' . md5($key) . '.json';
        file_put_contents($cache_file, json_encode($data));
    }
    
    /**
     * Format numbers for display
     */
    public function formatNumber($number) {
        if ($number >= 1000000) {
            return round($number / 1000000, 1) . 'M';
        } elseif ($number >= 1000) {
            return round($number / 1000, 1) . 'K';
        }
        return $number;
    }
    
    /**
     * Format timestamp for display
     */
    public function formatTime($timestamp) {
        $time = strtotime($timestamp);
        $diff = time() - $time;
        
        if ($diff < 3600) {
            return floor($diff / 60) . ' minutes ago';
        } elseif ($diff < 86400) {
            return floor($diff / 3600) . ' hours ago';
        } elseif ($diff < 604800) {
            return floor($diff / 86400) . ' days ago';
        } else {
            return date('M j, Y', $time);
        }
    }
}
