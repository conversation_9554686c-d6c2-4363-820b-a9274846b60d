<?php
/**
 * Instagram Follow Verification System
 * Ensures users follow required Instagram accounts before accessing panels
 */

class InstagramVerification {
    private $required_accounts = [
        'thesyedabubakkar',
        'real_earners.in'
    ];
    
    /**
     * Check if user has verified Instagram follows
     */
    public function isVerified($user_id, $user_type) {
        $db = Database::getInstance();

        // Check if user has verified Instagram follows
        $verification = $db->fetch(
            "SELECT * FROM instagram_verifications WHERE user_id = ? AND user_type = ? AND verified = 1",
            [$user_id, $user_type]
        );

        return $verification !== null && $verification !== false;
    }
    
    /**
     * Get verification status for user
     */
    public function getVerificationStatus($user_id, $user_type) {
        $db = Database::getInstance();
        
        $verification = $db->fetch(
            "SELECT * FROM instagram_verifications WHERE user_id = ? AND user_type = ?",
            [$user_id, $user_type]
        );
        
        if (!$verification) {
            return [
                'verified' => false,
                'accounts_verified' => [],
                'pending_accounts' => $this->required_accounts,
                'verification_date' => null
            ];
        }
        
        $accounts_verified = json_decode($verification['accounts_verified'], true) ?: [];
        $pending_accounts = array_diff($this->required_accounts, $accounts_verified);
        
        return [
            'verified' => $verification['verified'] == 1,
            'accounts_verified' => $accounts_verified,
            'pending_accounts' => $pending_accounts,
            'verification_date' => $verification['verified_at']
        ];
    }
    
    /**
     * Submit Instagram username for verification
     */
    public function submitVerification($user_id, $user_type, $instagram_username) {
        $db = Database::getInstance();
        
        // Check if verification record exists
        $existing = $db->fetch(
            "SELECT id FROM instagram_verifications WHERE user_id = ? AND user_type = ?",
            [$user_id, $user_type]
        );
        
        if ($existing) {
            // Update existing record
            $db->update(
                'instagram_verifications',
                [
                    'instagram_username' => $instagram_username,
                    'verification_status' => 'pending',
                    'submitted_at' => date('Y-m-d H:i:s')
                ],
                'id = ?',
                [$existing['id']]
            );
        } else {
            // Create new record
            $db->insert('instagram_verifications', [
                'user_id' => $user_id,
                'user_type' => $user_type,
                'instagram_username' => $instagram_username,
                'verification_status' => 'pending',
                'verified' => 0,
                'accounts_verified' => json_encode([]),
                'submitted_at' => date('Y-m-d H:i:s')
            ]);
        }
        
        return true;
    }
    
    /**
     * Verify Instagram follows (admin function)
     */
    public function verifyFollows($verification_id, $verified_accounts) {
        $db = Database::getInstance();
        
        $all_verified = count($verified_accounts) >= count($this->required_accounts);
        
        $db->update(
            'instagram_verifications',
            [
                'accounts_verified' => json_encode($verified_accounts),
                'verified' => $all_verified ? 1 : 0,
                'verification_status' => $all_verified ? 'verified' : 'partial',
                'verified_at' => $all_verified ? date('Y-m-d H:i:s') : null
            ],
            'id = ?',
            [$verification_id]
        );
        
        return $all_verified;
    }
    
    /**
     * Get required Instagram accounts
     */
    public function getRequiredAccounts() {
        return $this->required_accounts;
    }
    
    /**
     * Check if user can access panel
     */
    public function canAccessPanel($user_type) {
        if (!is_logged_in()) {
            return false;
        }

        $user_id = get_user_id();
        return $this->isVerified($user_id, $user_type);
    }
    
    /**
     * Redirect to verification page if not verified
     */
    public function requireVerification($user_type) {
        if (!$this->canAccessPanel($user_type)) {
            // Get the base URL for the redirect
            $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
            $host = $_SERVER['HTTP_HOST'];
            $base_path = dirname(dirname($_SERVER['SCRIPT_NAME']));
            $redirect_url = $protocol . '://' . $host . $base_path . '/auth/instagram_verification.php';

            header('Location: ' . $redirect_url);
            exit();
        }
    }
}

/**
 * Helper function to check Instagram verification
 */
function require_instagram_verification($user_type) {
    $verification = new InstagramVerification();
    $verification->requireVerification($user_type);
}

/**
 * Helper function to get verification status
 */
function get_instagram_verification_status($user_id, $user_type) {
    $verification = new InstagramVerification();
    return $verification->getVerificationStatus($user_id, $user_type);
}
