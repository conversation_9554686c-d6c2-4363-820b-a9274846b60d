<?php
/**
 * Instagram Follow Verification System
 * Ensures users follow required Instagram accounts before accessing panels
 */

class InstagramVerification {
    private $required_accounts = [
        'thesyedabubakkar',
        'real_earners.in'
    ];
    
    /**
     * Check if user has verified Instagram follows
     */
    public function isVerified($user_id, $user_type) {
        $db = Database::getInstance();

        // Check if user has verified Instagram follows and is not suspended
        $verification = $db->fetch(
            "SELECT * FROM instagram_verifications WHERE user_id = ? AND user_type = ? AND verified = 1 AND verification_status != 'suspended'",
            [$user_id, $user_type]
        );

        return $verification !== null && $verification !== false;
    }

    /**
     * Check if user has submitted verification (regardless of status)
     */
    public function hasSubmittedVerification($user_id, $user_type) {
        $db = Database::getInstance();

        $verification = $db->fetch(
            "SELECT * FROM instagram_verifications WHERE user_id = ? AND user_type = ?",
            [$user_id, $user_type]
        );

        return $verification !== null && $verification !== false;
    }

    /**
     * Check if user is suspended
     */
    public function isSuspended($user_id, $user_type) {
        $db = Database::getInstance();

        $verification = $db->fetch(
            "SELECT * FROM instagram_verifications WHERE user_id = ? AND user_type = ? AND verification_status = 'suspended'",
            [$user_id, $user_type]
        );

        return $verification !== null && $verification !== false;
    }

    /**
     * Check if user should be auto-suspended (only for users who submitted verification)
     */
    public function shouldAutoSuspend($user_id, $user_type) {
        $db = Database::getInstance();

        // Only check users who have submitted verification
        $verification = $db->fetch(
            "SELECT * FROM instagram_verifications WHERE user_id = ? AND user_type = ? AND verification_status = 'pending'",
            [$user_id, $user_type]
        );

        if (!$verification) {
            return false; // No verification submitted, don't suspend
        }

        // Check if it's been more than 2 days since submission
        $days_since_submission = (time() - strtotime($verification['submitted_at'])) / (24 * 60 * 60);

        return $days_since_submission >= 2;
    }

    /**
     * Check if suspended user has now followed required accounts and auto-unsuspend them
     */
    public function checkAndUnsuspendUser($user_id, $user_type, $instagram_username) {
        $db = Database::getInstance();

        // Check if user is currently suspended
        $verification = $db->fetch(
            "SELECT * FROM instagram_verifications WHERE user_id = ? AND user_type = ? AND verification_status = 'suspended'",
            [$user_id, $user_type]
        );

        if (!$verification) {
            return false; // User is not suspended
        }

        // Here you would implement actual Instagram API check
        // For now, we'll create a manual verification process
        // Admin can check and unsuspend manually

        return false;
    }

    /**
     * Manually unsuspend a user (for admin use)
     */
    public function unsuspendUser($verification_id, $admin_id, $notes = '') {
        $db = Database::getInstance();

        try {
            // Get verification details
            $verification = $db->fetch("SELECT * FROM instagram_verifications WHERE id = ?", [$verification_id]);

            if (!$verification || $verification['verification_status'] !== 'suspended') {
                return false;
            }

            // Update verification status back to pending
            $db->update(
                'instagram_verifications',
                [
                    'verification_status' => 'pending',
                    'suspension_date' => null,
                    'suspension_reason' => null,
                    'verified_by' => $admin_id,
                    'notes' => $notes
                ],
                'id = ?',
                [$verification_id]
            );

            return true;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Get verification status for user
     */
    public function getVerificationStatus($user_id, $user_type) {
        $db = Database::getInstance();
        
        $verification = $db->fetch(
            "SELECT * FROM instagram_verifications WHERE user_id = ? AND user_type = ?",
            [$user_id, $user_type]
        );
        
        if (!$verification) {
            return [
                'verified' => false,
                'accounts_verified' => [],
                'pending_accounts' => $this->required_accounts,
                'verification_date' => null,
                'verification_status' => 'not_submitted',
                'instagram_username' => null,
                'submitted_at' => null
            ];
        }

        $accounts_verified = json_decode($verification['accounts_verified'], true) ?: [];
        $pending_accounts = array_diff($this->required_accounts, $accounts_verified);

        return [
            'verified' => $verification['verified'] == 1,
            'accounts_verified' => $accounts_verified,
            'pending_accounts' => $pending_accounts,
            'verification_date' => $verification['verified_at'],
            'verification_status' => $verification['verification_status'],
            'instagram_username' => $verification['instagram_username'],
            'submitted_at' => $verification['submitted_at']
        ];
    }
    
    /**
     * Submit Instagram username for verification
     */
    public function submitVerification($user_id, $user_type, $instagram_username) {
        $db = Database::getInstance();
        
        // Check if verification record exists
        $existing = $db->fetch(
            "SELECT id FROM instagram_verifications WHERE user_id = ? AND user_type = ?",
            [$user_id, $user_type]
        );
        
        if ($existing) {
            // Update existing record
            $db->update(
                'instagram_verifications',
                [
                    'instagram_username' => $instagram_username,
                    'verification_status' => 'pending',
                    'submitted_at' => date('Y-m-d H:i:s')
                ],
                'id = ?',
                [$existing['id']]
            );
        } else {
            // Create new record
            $db->insert('instagram_verifications', [
                'user_id' => $user_id,
                'user_type' => $user_type,
                'instagram_username' => $instagram_username,
                'verification_status' => 'pending',
                'verified' => 0,
                'accounts_verified' => json_encode([]),
                'submitted_at' => date('Y-m-d H:i:s')
            ]);
        }
        
        return true;
    }
    
    /**
     * Verify Instagram follows (admin function)
     */
    public function verifyFollows($verification_id, $verified_accounts) {
        $db = Database::getInstance();
        
        $all_verified = count($verified_accounts) >= count($this->required_accounts);
        
        $db->update(
            'instagram_verifications',
            [
                'accounts_verified' => json_encode($verified_accounts),
                'verified' => $all_verified ? 1 : 0,
                'verification_status' => $all_verified ? 'verified' : 'partial',
                'verified_at' => $all_verified ? date('Y-m-d H:i:s') : null
            ],
            'id = ?',
            [$verification_id]
        );
        
        return $all_verified;
    }
    
    /**
     * Get required Instagram accounts
     */
    public function getRequiredAccounts() {
        return $this->required_accounts;
    }
    
    /**
     * Check if user can access panel
     */
    public function canAccessPanel($user_type) {
        if (!is_logged_in()) {
            return false;
        }

        $user_id = get_user_id();

        // Allow access if user is verified OR has submitted verification (pending)
        return $this->isVerified($user_id, $user_type) || $this->hasSubmittedVerification($user_id, $user_type);
    }
    
    /**
     * Redirect to verification page if not verified
     */
    public function requireVerification($user_type) {
        if (!is_logged_in()) {
            return;
        }

        $user_id = get_user_id();

        // Check if user is suspended first
        if ($this->isSuspended($user_id, $user_type)) {
            $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
            $host = $_SERVER['HTTP_HOST'];
            $base_path = dirname(dirname($_SERVER['SCRIPT_NAME']));
            $redirect_url = $protocol . '://' . $host . $base_path . '/auth/account_suspended.php';

            header('Location: ' . $redirect_url);
            exit();
        }

        // Check if user should be auto-suspended (only if they submitted verification)
        if ($this->shouldAutoSuspend($user_id, $user_type)) {
            // Auto-suspend the user
            $this->autoSuspendUser($user_id, $user_type);

            // Redirect to suspension page
            $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
            $host = $_SERVER['HTTP_HOST'];
            $base_path = dirname(dirname($_SERVER['SCRIPT_NAME']));
            $redirect_url = $protocol . '://' . $host . $base_path . '/auth/account_suspended.php';

            header('Location: ' . $redirect_url);
            exit();
        }

        // Then check if user needs to submit verification
        if (!$this->hasSubmittedVerification($user_id, $user_type)) {
            // User hasn't submitted verification yet, redirect to verification page
            $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
            $host = $_SERVER['HTTP_HOST'];
            $base_path = dirname(dirname($_SERVER['SCRIPT_NAME']));
            $redirect_url = $protocol . '://' . $host . $base_path . '/auth/instagram_verification.php';

            header('Location: ' . $redirect_url);
            exit();
        }

        // If user has submitted verification (pending or verified), they can access the panel
    }

    /**
     * Auto-suspend a user who hasn't followed within 2 days
     */
    private function autoSuspendUser($user_id, $user_type) {
        $db = Database::getInstance();

        $db->update(
            'instagram_verifications',
            [
                'verification_status' => 'suspended',
                'suspension_date' => date('Y-m-d H:i:s'),
                'suspension_reason' => 'Auto-suspended: Failed to follow required Instagram accounts within 2 days of submission'
            ],
            'user_id = ? AND user_type = ? AND verification_status = ?',
            [$user_id, $user_type, 'pending']
        );
    }
}

/**
 * Helper function to check Instagram verification
 */
function require_instagram_verification($user_type) {
    $verification = new InstagramVerification();
    $verification->requireVerification($user_type);
}

/**
 * Helper function to get verification status
 */
function get_instagram_verification_status($user_id, $user_type) {
    $verification = new InstagramVerification();
    return $verification->getVerificationStatus($user_id, $user_type);
}
