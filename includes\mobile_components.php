<?php
/**
 * Mobile-Optimized Component Templates
 * Responsive components for all device sizes
 */

/**
 * Mobile-friendly table component
 */
function render_mobile_table($data, $headers, $options = []) {
    $table_id = $options['id'] ?? 'mobile-table-' . uniqid();
    $responsive_class = $options['responsive_class'] ?? 'table-responsive';
    $mobile_card_class = $options['mobile_card_class'] ?? 'mobile-table-cards';
    
    ob_start();
    ?>
    <div class="<?php echo $responsive_class; ?>">
        <!-- Desktop Table -->
        <table class="table table-striped table-hover d-none d-md-table" id="<?php echo $table_id; ?>">
            <thead class="table-dark">
                <tr>
                    <?php foreach ($headers as $header): ?>
                        <th><?php echo htmlspecialchars($header); ?></th>
                    <?php endforeach; ?>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($data as $row): ?>
                    <tr>
                        <?php foreach ($row as $cell): ?>
                            <td><?php echo $cell; ?></td>
                        <?php endforeach; ?>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        
        <!-- Mobile Cards -->
        <div class="<?php echo $mobile_card_class; ?> d-block d-md-none">
            <?php foreach ($data as $row_index => $row): ?>
                <div class="mobile-card">
                    <div class="mobile-card-header">
                        <?php echo isset($options['card_title']) ? $options['card_title']($row, $row_index) : "Item " . ($row_index + 1); ?>
                    </div>
                    <?php foreach ($row as $cell_index => $cell): ?>
                        <?php if (isset($headers[$cell_index])): ?>
                            <div class="mobile-card-row">
                                <div class="mobile-card-label"><?php echo htmlspecialchars($headers[$cell_index]); ?></div>
                                <div class="mobile-card-value"><?php echo $cell; ?></div>
                            </div>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php
    return ob_get_clean();
}

/**
 * Mobile-friendly button group
 */
function render_mobile_button_group($buttons, $options = []) {
    $group_class = $options['class'] ?? 'btn-group-mobile';
    $button_class = $options['button_class'] ?? 'btn btn-primary';
    
    ob_start();
    ?>
    <div class="<?php echo $group_class; ?>">
        <?php foreach ($buttons as $button): ?>
            <a href="<?php echo $button['url'] ?? '#'; ?>" 
               class="<?php echo $button_class; ?> <?php echo $button['class'] ?? ''; ?>"
               <?php echo isset($button['onclick']) ? 'onclick="' . $button['onclick'] . '"' : ''; ?>
               <?php echo isset($button['target']) ? 'target="' . $button['target'] . '"' : ''; ?>>
                <?php if (isset($button['icon'])): ?>
                    <i class="<?php echo $button['icon']; ?> me-2"></i>
                <?php endif; ?>
                <?php echo $button['text']; ?>
            </a>
        <?php endforeach; ?>
    </div>
    <?php
    return ob_get_clean();
}

/**
 * Mobile-optimized stats cards
 */
function render_mobile_stats_cards($stats, $options = []) {
    $cols_mobile = $options['cols_mobile'] ?? 1;
    $cols_tablet = $options['cols_tablet'] ?? 2;
    $cols_desktop = $options['cols_desktop'] ?? 4;
    
    $col_class = "col-12 col-sm-" . (12 / $cols_mobile) . " col-md-" . (12 / $cols_tablet) . " col-lg-" . (12 / $cols_desktop);
    
    ob_start();
    ?>
    <div class="row mb-4">
        <?php foreach ($stats as $stat): ?>
            <div class="<?php echo $col_class; ?> mb-3">
                <div class="stats-card <?php echo $stat['class'] ?? ''; ?>">
                    <div class="card-body text-center">
                        <?php if (isset($stat['icon'])): ?>
                            <i class="<?php echo $stat['icon']; ?> fa-2x mb-2"></i>
                        <?php endif; ?>
                        <h3 class="fw-bold"><?php echo $stat['value']; ?></h3>
                        <p class="mb-0"><?php echo $stat['label']; ?></p>
                        <?php if (isset($stat['subtitle'])): ?>
                            <small class="text-muted"><?php echo $stat['subtitle']; ?></small>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
    <?php
    return ob_get_clean();
}

/**
 * Mobile-friendly form layout
 */
function render_mobile_form($form_config) {
    $form_id = $form_config['id'] ?? 'mobile-form-' . uniqid();
    $form_action = $form_config['action'] ?? '';
    $form_method = $form_config['method'] ?? 'POST';
    $form_class = $form_config['class'] ?? 'needs-validation';
    
    ob_start();
    ?>
    <form id="<?php echo $form_id; ?>" 
          action="<?php echo $form_action; ?>" 
          method="<?php echo $form_method; ?>" 
          class="<?php echo $form_class; ?>" 
          novalidate>
        
        <?php foreach ($form_config['fields'] as $field): ?>
            <div class="mb-3">
                <?php if ($field['type'] === 'text' || $field['type'] === 'email' || $field['type'] === 'password' || $field['type'] === 'number'): ?>
                    <label for="<?php echo $field['name']; ?>" class="form-label">
                        <?php echo $field['label']; ?>
                        <?php if ($field['required'] ?? false): ?>
                            <span class="text-danger">*</span>
                        <?php endif; ?>
                    </label>
                    <input type="<?php echo $field['type']; ?>" 
                           class="form-control" 
                           id="<?php echo $field['name']; ?>" 
                           name="<?php echo $field['name']; ?>" 
                           placeholder="<?php echo $field['placeholder'] ?? ''; ?>"
                           value="<?php echo $field['value'] ?? ''; ?>"
                           <?php echo ($field['required'] ?? false) ? 'required' : ''; ?>
                           <?php echo isset($field['min']) ? 'min="' . $field['min'] . '"' : ''; ?>
                           <?php echo isset($field['max']) ? 'max="' . $field['max'] . '"' : ''; ?>>
                    
                <?php elseif ($field['type'] === 'textarea'): ?>
                    <label for="<?php echo $field['name']; ?>" class="form-label">
                        <?php echo $field['label']; ?>
                        <?php if ($field['required'] ?? false): ?>
                            <span class="text-danger">*</span>
                        <?php endif; ?>
                    </label>
                    <textarea class="form-control" 
                              id="<?php echo $field['name']; ?>" 
                              name="<?php echo $field['name']; ?>" 
                              rows="<?php echo $field['rows'] ?? 3; ?>"
                              placeholder="<?php echo $field['placeholder'] ?? ''; ?>"
                              <?php echo ($field['required'] ?? false) ? 'required' : ''; ?>><?php echo $field['value'] ?? ''; ?></textarea>
                    
                <?php elseif ($field['type'] === 'select'): ?>
                    <label for="<?php echo $field['name']; ?>" class="form-label">
                        <?php echo $field['label']; ?>
                        <?php if ($field['required'] ?? false): ?>
                            <span class="text-danger">*</span>
                        <?php endif; ?>
                    </label>
                    <select class="form-select" 
                            id="<?php echo $field['name']; ?>" 
                            name="<?php echo $field['name']; ?>"
                            <?php echo ($field['required'] ?? false) ? 'required' : ''; ?>>
                        <?php if (isset($field['placeholder'])): ?>
                            <option value=""><?php echo $field['placeholder']; ?></option>
                        <?php endif; ?>
                        <?php foreach ($field['options'] as $value => $text): ?>
                            <option value="<?php echo $value; ?>" 
                                    <?php echo ($field['value'] ?? '') === $value ? 'selected' : ''; ?>>
                                <?php echo $text; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    
                <?php elseif ($field['type'] === 'checkbox'): ?>
                    <div class="form-check">
                        <input class="form-check-input" 
                               type="checkbox" 
                               id="<?php echo $field['name']; ?>" 
                               name="<?php echo $field['name']; ?>" 
                               value="1"
                               <?php echo ($field['checked'] ?? false) ? 'checked' : ''; ?>
                               <?php echo ($field['required'] ?? false) ? 'required' : ''; ?>>
                        <label class="form-check-label" for="<?php echo $field['name']; ?>">
                            <?php echo $field['label']; ?>
                            <?php if ($field['required'] ?? false): ?>
                                <span class="text-danger">*</span>
                            <?php endif; ?>
                        </label>
                    </div>
                    
                <?php elseif ($field['type'] === 'file'): ?>
                    <label for="<?php echo $field['name']; ?>" class="form-label">
                        <?php echo $field['label']; ?>
                        <?php if ($field['required'] ?? false): ?>
                            <span class="text-danger">*</span>
                        <?php endif; ?>
                    </label>
                    <input type="file" 
                           class="form-control file-input" 
                           id="<?php echo $field['name']; ?>" 
                           name="<?php echo $field['name']; ?>"
                           accept="<?php echo $field['accept'] ?? ''; ?>"
                           <?php echo ($field['required'] ?? false) ? 'required' : ''; ?>>
                    <div class="file-preview mt-2"></div>
                <?php endif; ?>
                
                <?php if (isset($field['help'])): ?>
                    <div class="form-text"><?php echo $field['help']; ?></div>
                <?php endif; ?>
                
                <div class="invalid-feedback">
                    <?php echo $field['error_message'] ?? 'Please provide a valid value.'; ?>
                </div>
            </div>
        <?php endforeach; ?>
        
        <div class="d-grid gap-2">
            <?php if (isset($form_config['buttons'])): ?>
                <?php foreach ($form_config['buttons'] as $button): ?>
                    <button type="<?php echo $button['type'] ?? 'button'; ?>" 
                            class="btn <?php echo $button['class'] ?? 'btn-primary'; ?>">
                        <?php if (isset($button['icon'])): ?>
                            <i class="<?php echo $button['icon']; ?> me-2"></i>
                        <?php endif; ?>
                        <?php echo $button['text']; ?>
                    </button>
                <?php endforeach; ?>
            <?php else: ?>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>Submit
                </button>
            <?php endif; ?>
        </div>
    </form>
    <?php
    return ob_get_clean();
}

/**
 * Mobile-optimized navigation breadcrumb
 */
function render_mobile_breadcrumb($items, $options = []) {
    $show_home = $options['show_home'] ?? true;
    $home_url = $options['home_url'] ?? '/';
    $home_text = $options['home_text'] ?? 'Home';
    
    ob_start();
    ?>
    <nav aria-label="breadcrumb" class="mb-3">
        <ol class="breadcrumb">
            <?php if ($show_home): ?>
                <li class="breadcrumb-item">
                    <a href="<?php echo $home_url; ?>">
                        <i class="fas fa-home me-1"></i>
                        <span class="d-none d-sm-inline"><?php echo $home_text; ?></span>
                    </a>
                </li>
            <?php endif; ?>
            
            <?php foreach ($items as $index => $item): ?>
                <?php if ($index === count($items) - 1): ?>
                    <li class="breadcrumb-item active" aria-current="page">
                        <?php echo $item['text']; ?>
                    </li>
                <?php else: ?>
                    <li class="breadcrumb-item">
                        <a href="<?php echo $item['url']; ?>"><?php echo $item['text']; ?></a>
                    </li>
                <?php endif; ?>
            <?php endforeach; ?>
        </ol>
    </nav>
    <?php
    return ob_get_clean();
}

/**
 * Mobile-friendly alert component
 */
function render_mobile_alert($message, $type = 'info', $options = []) {
    $dismissible = $options['dismissible'] ?? true;
    $icon = $options['icon'] ?? null;
    $title = $options['title'] ?? null;
    
    if (!$icon) {
        $icons = [
            'success' => 'fas fa-check-circle',
            'danger' => 'fas fa-exclamation-circle',
            'warning' => 'fas fa-exclamation-triangle',
            'info' => 'fas fa-info-circle'
        ];
        $icon = $icons[$type] ?? 'fas fa-info-circle';
    }
    
    ob_start();
    ?>
    <div class="alert alert-<?php echo $type; ?> <?php echo $dismissible ? 'alert-dismissible' : ''; ?> fade show" role="alert">
        <div class="d-flex align-items-start">
            <i class="<?php echo $icon; ?> me-2 mt-1"></i>
            <div class="flex-grow-1">
                <?php if ($title): ?>
                    <h6 class="alert-heading mb-1"><?php echo $title; ?></h6>
                <?php endif; ?>
                <?php echo $message; ?>
            </div>
            <?php if ($dismissible): ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            <?php endif; ?>
        </div>
    </div>
    <?php
    return ob_get_clean();
}
?>
