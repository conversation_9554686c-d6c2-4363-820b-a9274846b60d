<?php
/**
 * Settings Manager Class
 * Provides easy access to platform settings with caching
 */

require_once 'currency_helper.php';
class SettingsManager {
    private static $instance = null;
    private $db;
    private $settings_cache = [];
    private $cache_loaded = false;
    
    private function __construct() {
        $this->db = Database::getInstance();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Load all settings into cache
     */
    private function loadSettings() {
        if (!$this->cache_loaded) {
            $settings = $this->db->fetchAll("SELECT setting_key, setting_value FROM settings");
            foreach ($settings as $setting) {
                $this->settings_cache[$setting['setting_key']] = $setting['setting_value'];
            }
            $this->cache_loaded = true;
        }
    }
    
    /**
     * Get a setting value
     */
    public function get($key, $default = null) {
        $this->loadSettings();
        return isset($this->settings_cache[$key]) ? $this->settings_cache[$key] : $default;
    }
    
    /**
     * Get a boolean setting value
     */
    public function getBool($key, $default = false) {
        $value = $this->get($key, $default);
        return (bool) $value;
    }
    
    /**
     * Get an integer setting value
     */
    public function getInt($key, $default = 0) {
        $value = $this->get($key, $default);
        return (int) $value;
    }
    
    /**
     * Get a float setting value
     */
    public function getFloat($key, $default = 0.0) {
        $value = $this->get($key, $default);
        return (float) $value;
    }
    
    /**
     * Set a setting value
     */
    public function set($key, $value) {
        try {
            // Check if setting exists
            $existing = $this->db->fetch("SELECT id FROM settings WHERE setting_key = ?", [$key]);
            
            if ($existing) {
                // Update existing setting
                $this->db->update('settings', [
                    'setting_value' => $value,
                    'updated_at' => date('Y-m-d H:i:s')
                ], 'setting_key = ?', [$key]);
            } else {
                // Insert new setting
                $this->db->insert('settings', [
                    'setting_key' => $key,
                    'setting_value' => $value,
                    'description' => 'Auto-generated setting',
                    'category' => 'general'
                ]);
            }
            
            // Update cache
            $this->settings_cache[$key] = $value;
            
            return true;
        } catch (Exception $e) {
            error_log("Settings Manager Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get all settings by category
     */
    public function getByCategory($category) {
        $settings = $this->db->fetchAll("SELECT * FROM settings WHERE category = ? ORDER BY setting_key", [$category]);
        $result = [];
        foreach ($settings as $setting) {
            $result[$setting['setting_key']] = $setting['setting_value'];
        }
        return $result;
    }
    
    /**
     * Get all settings
     */
    public function getAll() {
        $this->loadSettings();
        return $this->settings_cache;
    }
    
    /**
     * Clear settings cache
     */
    public function clearCache() {
        $this->settings_cache = [];
        $this->cache_loaded = false;
    }
    
    /**
     * Get payment settings
     */
    public function getPaymentSettings() {
        return [
            'razorpay_key' => $this->get('razorpay_key'),
            'razorpay_secret' => $this->get('razorpay_secret'),
            'min_payout_amount' => $this->getFloat('min_payout_amount', 100),
            'max_payout_amount' => $this->getFloat('max_payout_amount', 50000),
            'platform_commission' => $this->getFloat('platform_commission', 10),
            'payout_processing_fee' => $this->getFloat('payout_processing_fee', 5),
            'auto_payout_enabled' => $this->getBool('auto_payout_enabled'),
            'payout_schedule' => $this->get('payout_schedule', 'weekly')
        ];
    }

    /**
     * Get currency settings
     */
    public function getCurrencySettings() {
        return [
            'currency_code' => $this->get('currency_code', 'INR'),
            'currency_symbol' => $this->get('currency_symbol', '₹'),
            'currency_name' => $this->get('currency_name', 'Indian Rupee'),
            'currency_position' => $this->get('currency_position', 'before'),
            'decimal_places' => $this->getInt('decimal_places', 2),
            'thousand_separator' => $this->get('thousand_separator', ','),
            'decimal_separator' => $this->get('decimal_separator', '.'),
            'currency_format' => $this->get('currency_format', '{symbol}{amount}'),
            'exchange_rate_usd' => $this->getFloat('exchange_rate_usd', 83.50),
            'payment_gateway' => $this->get('payment_gateway', 'razorpay'),
            'enable_international_payments' => $this->getBool('enable_international_payments', false)
        ];
    }

    /**
     * Update currency settings
     */
    public function updateCurrencySettings($settings) {
        try {
            foreach ($settings as $key => $value) {
                if (strpos($key, 'currency_') === 0 || in_array($key, ['min_payout_amount', 'default_task_reward', 'platform_commission', 'exchange_rate_usd', 'payment_gateway', 'enable_international_payments'])) {
                    $this->set($key, $value);
                }
            }
            return true;
        } catch (Exception $e) {
            error_log("Currency Settings Update Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Format currency amount
     */
    public function formatCurrency($amount, $currency_code = null) {
        return CurrencyHelper::format($amount, $currency_code);
    }

    /**
     * Get currency symbol
     */
    public function getCurrencySymbol($currency_code = null) {
        return CurrencyHelper::getSymbol($currency_code);
    }

    /**
     * Get currency code
     */
    public function getCurrencyCode() {
        return CurrencyHelper::getCode();
    }
    
    /**
     * Get email settings
     */
    public function getEmailSettings() {
        return [
            'smtp_host' => $this->get('email_smtp_host'),
            'smtp_port' => $this->getInt('email_smtp_port', 587),
            'smtp_username' => $this->get('email_smtp_username'),
            'smtp_password' => $this->get('email_smtp_password'),
            'smtp_encryption' => $this->get('email_smtp_encryption', 'tls'),
            'from_name' => $this->get('email_from_name', 'RealEarners'),
            'from_address' => $this->get('email_from_address'),
            'notifications_enabled' => $this->getBool('email_notifications_enabled', true)
        ];
    }
    
    /**
     * Get Instagram settings
     */
    public function getInstagramSettings() {
        return [
            'account_1' => $this->get('instagram_account_1', 'thesyedabubakkar'),
            'account_2' => $this->get('instagram_account_2', 'real_earners.in'),
            'verification_required' => $this->getBool('instagram_verification_required', true),
            'grace_period_days' => $this->getInt('instagram_grace_period_days', 2),
            'auto_suspend' => $this->getBool('instagram_auto_suspend', true),
            'api_key' => $this->get('instagram_api_key'),
            'api_secret' => $this->get('instagram_api_secret')
        ];
    }
    
    /**
     * Get security settings
     */
    public function getSecuritySettings() {
        return [
            'session_timeout' => $this->getInt('session_timeout', 3600),
            'max_login_attempts' => $this->getInt('max_login_attempts', 5),
            'login_lockout_duration' => $this->getInt('login_lockout_duration', 900),
            'password_min_length' => $this->getInt('password_min_length', 8),
            'require_email_verification' => $this->getBool('require_email_verification', true),
            'two_factor_auth_enabled' => $this->getBool('two_factor_auth_enabled'),
            'ip_whitelist_enabled' => $this->getBool('ip_whitelist_enabled'),
            'admin_ip_whitelist' => $this->get('admin_ip_whitelist')
        ];
    }
    
    /**
     * Get site settings
     */
    public function getSiteSettings() {
        return [
            'name' => $this->get('site_name', 'RealEarners'),
            'description' => $this->get('site_description', 'Earn Real Money with Real Opportunities'),
            'url' => $this->get('site_url', 'https://realearners.in'),
            'logo' => $this->get('site_logo', '/assets/images/logo.png'),
            'favicon' => $this->get('site_favicon', '/assets/images/favicon.ico'),
            'email' => $this->get('site_email', '<EMAIL>'),
            'admin_email' => $this->get('admin_email', '<EMAIL>'),
            'contact_phone' => $this->get('contact_phone'),
            'contact_address' => $this->get('contact_address')
        ];
    }
    
    /**
     * Check if maintenance mode is enabled
     */
    public function isMaintenanceMode() {
        return $this->getBool('maintenance_mode');
    }
    
    /**
     * Get maintenance message
     */
    public function getMaintenanceMessage() {
        return $this->get('maintenance_message', 'We are currently performing scheduled maintenance. Please check back soon.');
    }
    
    /**
     * Check if a feature is enabled
     */
    public function isFeatureEnabled($feature) {
        return $this->getBool($feature . '_enabled', false);
    }
    
    /**
     * Get upload settings
     */
    public function getUploadSettings() {
        return [
            'max_file_size_mb' => $this->getInt('max_file_size_mb', 10),
            'allowed_file_types' => explode(',', $this->get('allowed_file_types', 'jpg,jpeg,png,gif,pdf,doc,docx')),
            'upload_path' => $this->get('upload_path', '/uploads/'),
            'image_compression_quality' => $this->getInt('image_compression_quality', 80)
        ];
    }
    
    /**
     * Export all settings as array
     */
    public function exportSettings() {
        return $this->db->fetchAll("SELECT * FROM settings ORDER BY category, setting_key");
    }
    
    /**
     * Import settings from array
     */
    public function importSettings($settings) {
        try {
            foreach ($settings as $setting) {
                $this->set($setting['setting_key'], $setting['setting_value']);
            }
            $this->clearCache();
            return true;
        } catch (Exception $e) {
            error_log("Settings Import Error: " . $e->getMessage());
            return false;
        }
    }
}

// Global helper functions (only declare if not already defined)
if (!function_exists('get_platform_setting')) {
    function get_platform_setting($key, $default = null) {
        return SettingsManager::getInstance()->get($key, $default);
    }
}

if (!function_exists('set_platform_setting')) {
    function set_platform_setting($key, $value) {
        return SettingsManager::getInstance()->set($key, $value);
    }
}

if (!function_exists('get_site_setting')) {
    function get_site_setting($key, $default = null) {
        $site_settings = SettingsManager::getInstance()->getSiteSettings();
        return isset($site_settings[$key]) ? $site_settings[$key] : $default;
    }
}

if (!function_exists('is_feature_enabled')) {
    function is_feature_enabled($feature) {
        return SettingsManager::getInstance()->isFeatureEnabled($feature);
    }
}

if (!function_exists('is_maintenance_mode')) {
    function is_maintenance_mode() {
        return SettingsManager::getInstance()->isMaintenanceMode();
    }
}

// Currency helper functions
if (!function_exists('format_currency')) {
    function format_currency($amount, $currency_code = null) {
        return SettingsManager::getInstance()->formatCurrency($amount, $currency_code);
    }
}

if (!function_exists('get_currency_symbol')) {
    function get_currency_symbol($currency_code = null) {
        return SettingsManager::getInstance()->getCurrencySymbol($currency_code);
    }
}

if (!function_exists('get_currency_code')) {
    function get_currency_code() {
        return SettingsManager::getInstance()->getCurrencyCode();
    }
}

if (!function_exists('format_currency_short')) {
    function format_currency_short($amount, $currency_code = null) {
        return CurrencyHelper::formatShort($amount, $currency_code);
    }
}
?>
