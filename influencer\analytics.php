<?php
require_once '../config.php';
require_once '../includes/instagram_verification.php';
require_login(['influencer']);
require_instagram_verification('influencer');

$db = Database::getInstance();
$influencer_id = get_user_id();

// Get influencer data
$influencer = $db->fetch("SELECT * FROM influencers WHERE id = ?", [$influencer_id]);

// Get verification status
$verification_system = new InstagramVerification();
$verification_status = $verification_system->getVerificationStatus($influencer_id, 'influencer');

// Get analytics data
$analytics = [
    'total_campaigns' => $db->fetch("SELECT COUNT(*) as count FROM influencer_campaigns WHERE influencer_id = ?", [$influencer_id])['count'],
    'active_campaigns' => $db->fetch("SELECT COUNT(*) as count FROM influencer_campaigns WHERE influencer_id = ? AND status IN ('accepted', 'submitted')", [$influencer_id])['count'],
    'completed_campaigns' => $db->fetch("SELECT COUNT(*) as count FROM influencer_campaigns WHERE influencer_id = ? AND status = 'approved'", [$influencer_id])['count'],
    'total_earned' => $influencer['total_earned'],
    'pending_payout' => $influencer['pending_payout']
];

// Get monthly performance data
$monthly_data = $db->fetchAll("
    SELECT 
        DATE_FORMAT(ic.approved_at, '%Y-%m') as month,
        COUNT(*) as campaigns_count,
        SUM(COALESCE(ic.negotiated_rate, c.reward_amount)) as earnings
    FROM influencer_campaigns ic
    JOIN campaigns c ON ic.campaign_id = c.id
    WHERE ic.influencer_id = ? AND ic.status = 'approved'
    AND ic.approved_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
    GROUP BY DATE_FORMAT(ic.approved_at, '%Y-%m')
    ORDER BY month DESC
", [$influencer_id]);

// Get campaign performance by category
$category_performance = $db->fetchAll("
    SELECT 
        c.target_audience as category,
        COUNT(*) as campaigns_count,
        SUM(COALESCE(ic.negotiated_rate, c.reward_amount)) as total_earnings,
        AVG(COALESCE(ic.negotiated_rate, c.reward_amount)) as avg_earnings
    FROM influencer_campaigns ic
    JOIN campaigns c ON ic.campaign_id = c.id
    WHERE ic.influencer_id = ? AND ic.status = 'approved'
    GROUP BY c.target_audience
    ORDER BY total_earnings DESC
", [$influencer_id]);

// Get recent campaign performance
$recent_campaigns = $db->fetchAll("
    SELECT 
        ic.*,
        c.title,
        c.description,
        c.reward_amount,
        c.target_audience,
        b.company_name as brand_name,
        COALESCE(ic.negotiated_rate, c.reward_amount) as final_amount
    FROM influencer_campaigns ic
    JOIN campaigns c ON ic.campaign_id = c.id
    LEFT JOIN brands b ON c.brand_id = b.id
    WHERE ic.influencer_id = ?
    ORDER BY ic.assigned_at DESC
    LIMIT 10
", [$influencer_id]);

// Calculate engagement metrics
$engagement_rate = 0; // This would need to be calculated from post data
$follower_growth = 0; // This would need historical data
$campaign_success_rate = $analytics['total_campaigns'] > 0 ?
    round(($analytics['completed_campaigns'] / $analytics['total_campaigns']) * 100, 1) : 0;

$page_title = 'Analytics';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-xl-2 sidebar p-0">
            <div class="p-3">
                <h5 class="text-white mb-3">
                    <i class="fas fa-star me-2"></i>Influencer Panel
                </h5>
                
                <!-- Verification Status in Sidebar -->
                <?php if ($verification_status['verified']): ?>
                    <div class="alert alert-success py-2 px-3 mb-3" style="font-size: 0.875rem;">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>Verified</strong>
                    </div>
                <?php elseif ($verification_status['verification_status'] === 'pending'): ?>
                    <div class="alert alert-warning py-2 px-3 mb-3" style="font-size: 0.875rem;">
                        <i class="fas fa-clock me-2"></i>
                        <strong>Verification Pending</strong>
                    </div>
                <?php else: ?>
                    <div class="alert alert-danger py-2 px-3 mb-3" style="font-size: 0.875rem;">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Verification Required</strong>
                    </div>
                <?php endif; ?>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a class="nav-link" href="campaigns.php">
                        <i class="fas fa-bullhorn"></i>Campaigns
                    </a>
                    <a class="nav-link" href="collaborations.php">
                        <i class="fas fa-handshake"></i>Collaborations
                    </a>
                    <a class="nav-link" href="assign_users.php">
                        <i class="fas fa-user-plus"></i>Assign to Users
                    </a>
                    <a class="nav-link active" href="analytics.php">
                        <i class="fas fa-chart-line"></i>Analytics
                    </a>
                    <a class="nav-link" href="wallet.php">
                        <i class="fas fa-wallet"></i>Wallet
                    </a>
                    <a class="nav-link" href="profile.php">
                        <i class="fas fa-user-edit"></i>Profile
                    </a>
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10 main-content" style="margin-left: auto;">
            <!-- Success Message for New Verification Submission -->
            <?php if (isset($_SESSION['verification_submitted']) && $_SESSION['verification_submitted']): ?>
                <div class="alert alert-success alert-dismissible fade show mb-4" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-check-circle fa-2x me-3 text-success"></i>
                        <div>
                            <h6 class="alert-heading mb-1">Verification Submitted Successfully!</h6>
                            <p class="mb-0">
                                Your Instagram verification request has been submitted. You now have access to your dashboard 
                                while our team verifies your follows. This usually takes up to 24 hours.
                            </p>
                        </div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php unset($_SESSION['verification_submitted']); ?>
            <?php endif; ?>

            <!-- Instagram Verification Status Banner -->
            <?php if (!$verification_status['verified']): ?>
                <div class="alert alert-warning alert-dismissible fade show mb-4" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fab fa-instagram fa-2x me-3"></i>
                        <div class="flex-grow-1">
                            <?php if ($verification_status['verification_status'] === 'pending'): ?>
                                <h6 class="alert-heading mb-1">
                                    <i class="fas fa-clock me-2"></i>Instagram Verification Pending
                                </h6>
                                <p class="mb-2">
                                    Your Instagram verification is being reviewed. You have limited access until verification is complete.
                                </p>
                                <small class="text-muted">
                                    <strong>Submitted:</strong> <?php echo date('M j, Y g:i A', strtotime($verification_status['submitted_at'])); ?>
                                    | <strong>Instagram:</strong> @<?php echo htmlspecialchars($verification_status['instagram_username']); ?>
                                </small>
                            <?php else: ?>
                                <h6 class="alert-heading mb-1">
                                    <i class="fas fa-exclamation-triangle me-2"></i>Instagram Verification Required
                                </h6>
                                <p class="mb-2">
                                    You need to follow our Instagram accounts to unlock all features.
                                </p>
                                <a href="../auth/instagram_verification.php" class="btn btn-warning btn-sm">
                                    <i class="fab fa-instagram me-1"></i>Complete Verification
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Top Bar -->
            <div class="bg-white shadow-sm p-3 mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">Analytics Dashboard</h4>
                        <small class="text-muted">Track your performance and earnings insights</small>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-outline-primary d-lg-none" onclick="toggleSidebar()">
                            <i class="fas fa-bars"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="p-3">
                <!-- Key Metrics -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <i class="fas fa-bullhorn fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $analytics['total_campaigns']; ?></h3>
                                <p class="mb-0">Total Campaigns</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card success">
                            <div class="card-body text-center">
                                <i class="fas fa-percentage fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $campaign_success_rate; ?>%</h3>
                                <p class="mb-0">Success Rate</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card info">
                            <div class="card-body text-center">
                                <i class="fas fa-heart fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo number_format($engagement_rate, 1); ?>%</h3>
                                <p class="mb-0">Engagement Rate</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card warning">
                            <div class="card-body text-center">
                                <i class="fas fa-rupee-sign fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo format_currency($analytics['total_earned']); ?></h3>
                                <p class="mb-0">Total Earned</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- Monthly Performance Chart -->
                    <div class="col-lg-8 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-line me-2"></i>Monthly Performance
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($monthly_data)): ?>
                                    <div class="text-center py-5">
                                        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                                        <h6 class="text-muted">No Performance Data Yet</h6>
                                        <p class="text-muted">Complete some campaigns to see your monthly performance trends.</p>
                                        <a href="campaigns.php" class="btn btn-primary">
                                            <i class="fas fa-search me-1"></i>Find Campaigns
                                        </a>
                                    </div>
                                <?php else: ?>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Month</th>
                                                    <th>Campaigns</th>
                                                    <th>Earnings</th>
                                                    <th>Avg per Campaign</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($monthly_data as $month): ?>
                                                <tr>
                                                    <td>
                                                        <strong><?php echo date('M Y', strtotime($month['month'] . '-01')); ?></strong>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-primary"><?php echo $month['campaigns_count']; ?></span>
                                                    </td>
                                                    <td>
                                                        <span class="text-success fw-bold"><?php echo format_currency($month['earnings']); ?></span>
                                                    </td>
                                                    <td>
                                                        <?php echo format_currency($month['earnings'] / $month['campaigns_count']); ?>
                                                    </td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Instagram Metrics -->
                    <div class="col-lg-4 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fab fa-instagram me-2"></i>Instagram Metrics
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-12 mb-3">
                                        <h4 class="text-primary"><?php echo number_format($influencer['instagram_followers'] ?? 0); ?></h4>
                                        <small class="text-muted">Followers</small>
                                    </div>
                                    <div class="col-6">
                                        <h5 class="text-success"><?php echo format_currency($influencer['rate_per_post'] ?? 0); ?></h5>
                                        <small class="text-muted">Rate per Post</small>
                                    </div>
                                    <div class="col-6">
                                        <h5 class="text-info"><?php echo $analytics['total_campaigns']; ?></h5>
                                        <small class="text-muted">Total Campaigns</small>
                                    </div>
                                </div>

                                <hr>

                                <div class="text-center">
                                    <h6 class="fw-bold mb-2">Profile Performance</h6>
                                    <div class="row">
                                        <div class="col-6">
                                            <small class="text-muted">Category</small>
                                            <p class="fw-bold"><?php echo ucfirst($influencer['category'] ?? 'Not set'); ?></p>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">Verified</small>
                                            <p class="fw-bold">
                                                <?php if ($influencer['instagram_verified'] ?? 0): ?>
                                                    <i class="fas fa-check-circle text-success"></i> Yes
                                                <?php else: ?>
                                                    <i class="fas fa-times-circle text-danger"></i> No
                                                <?php endif; ?>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- Category Performance -->
                    <div class="col-lg-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-tags me-2"></i>Performance by Category
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($category_performance)): ?>
                                    <div class="text-center py-3">
                                        <i class="fas fa-tags fa-2x text-muted mb-2"></i>
                                        <p class="text-muted small">No category data available</p>
                                    </div>
                                <?php else: ?>
                                    <?php foreach ($category_performance as $category): ?>
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <div>
                                                <h6 class="mb-1"><?php echo ucfirst($category['category']); ?></h6>
                                                <small class="text-muted"><?php echo $category['campaigns_count']; ?> campaigns</small>
                                            </div>
                                            <div class="text-end">
                                                <h6 class="mb-0 text-success"><?php echo format_currency($category['total_earnings']); ?></h6>
                                                <small class="text-muted">Avg: <?php echo format_currency($category['avg_earnings']); ?></small>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Campaign Performance -->
                    <div class="col-lg-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-history me-2"></i>Recent Campaigns
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recent_campaigns)): ?>
                                    <div class="text-center py-3">
                                        <i class="fas fa-bullhorn fa-2x text-muted mb-2"></i>
                                        <p class="text-muted small">No campaigns yet</p>
                                        <a href="campaigns.php" class="btn btn-primary btn-sm">
                                            Find Campaigns
                                        </a>
                                    </div>
                                <?php else: ?>
                                    <?php foreach (array_slice($recent_campaigns, 0, 5) as $campaign): ?>
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <div>
                                                <h6 class="mb-1 small"><?php echo htmlspecialchars($campaign['title']); ?></h6>
                                                <small class="text-muted">
                                                    <?php echo htmlspecialchars($campaign['brand_name'] ?? 'RealEarners'); ?>
                                                </small>
                                            </div>
                                            <div class="text-end">
                                                <?php
                                                $status_class = '';
                                                switch ($campaign['status']) {
                                                    case 'accepted':
                                                        $status_class = 'bg-info';
                                                        break;
                                                    case 'submitted':
                                                        $status_class = 'bg-warning';
                                                        break;
                                                    case 'approved':
                                                        $status_class = 'bg-success';
                                                        break;
                                                    case 'rejected':
                                                        $status_class = 'bg-danger';
                                                        break;
                                                    default:
                                                        $status_class = 'bg-secondary';
                                                }
                                                ?>
                                                <span class="badge <?php echo $status_class; ?> small">
                                                    <?php echo ucfirst($campaign['status']); ?>
                                                </span>
                                                <br>
                                                <small class="text-success"><?php echo format_currency($campaign['final_amount']); ?></small>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>

                                    <div class="text-center mt-3">
                                        <a href="collaborations.php" class="btn btn-sm btn-outline-primary">View All</a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
