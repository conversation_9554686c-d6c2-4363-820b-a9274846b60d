<?php
require_once '../config.php';
require_login(['influencer']);

$db = Database::getInstance();
$influencer_id = get_user_id();

// Check if influencer is approved
$influencer = $db->fetch("SELECT * FROM influencers WHERE id = ? AND status = 'approved'", [$influencer_id]);
if (!$influencer) {
    redirect('dashboard.php');
}

// Handle user assignment
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['assign_user'])) {
    $user_id = intval($_POST['user_id']);
    $campaign_id = intval($_POST['campaign_id']);
    $brand_campaign_id = intval($_POST['brand_campaign_id']);
    $assignment_type = sanitize_input($_POST['assignment_type']);
    $reward_amount = floatval($_POST['reward_amount']);
    $influencer_notes = sanitize_input($_POST['influencer_notes']);
    
    $errors = [];
    
    if (empty($user_id)) $errors[] = 'Please select a user.';
    if (empty($assignment_type)) $errors[] = 'Please select campaign type.';
    if ($reward_amount <= 0) $errors[] = 'Reward amount must be greater than 0.';
    
    // Verify campaign belongs to influencer or is available
    if ($assignment_type === 'admin_campaign' && $campaign_id) {
        $campaign = $db->fetch("
            SELECT c.* FROM campaigns c
            JOIN influencer_campaigns ic ON c.id = ic.campaign_id
            WHERE c.id = ? AND ic.influencer_id = ? AND ic.status = 'accepted'
        ", [$campaign_id, $influencer_id]);
        
        if (!$campaign) {
            $errors[] = 'Invalid campaign selection.';
        }
    } elseif ($assignment_type === 'brand_campaign' && $brand_campaign_id) {
        $brand_campaign = $db->fetch("
            SELECT bc.* FROM brand_campaigns bc
            JOIN brand_influencer_assignments bia ON bc.id = bia.brand_campaign_id
            WHERE bc.id = ? AND bia.influencer_id = ? AND bia.status = 'accepted'
        ", [$brand_campaign_id, $influencer_id]);
        
        if (!$brand_campaign) {
            $errors[] = 'Invalid brand campaign selection.';
        }
    } else {
        $errors[] = 'Please select a valid campaign.';
    }
    
    // Check if user is already assigned
    $existing = $db->fetch("
        SELECT id FROM influencer_user_assignments 
        WHERE influencer_id = ? AND user_id = ? AND 
        ((assignment_type = 'admin_campaign' AND campaign_id = ?) OR 
         (assignment_type = 'brand_campaign' AND brand_campaign_id = ?))
    ", [$influencer_id, $user_id, $campaign_id, $brand_campaign_id]);
    
    if ($existing) {
        $errors[] = 'User is already assigned to this campaign.';
    }
    
    if (empty($errors)) {
        try {
            $db->insert('influencer_user_assignments', [
                'influencer_id' => $influencer_id,
                'user_id' => $user_id,
                'campaign_id' => $assignment_type === 'admin_campaign' ? $campaign_id : null,
                'brand_campaign_id' => $assignment_type === 'brand_campaign' ? $brand_campaign_id : null,
                'assignment_type' => $assignment_type,
                'reward_amount' => $reward_amount,
                'status' => 'pending_admin_approval',
                'influencer_notes' => $influencer_notes
            ]);
            
            $_SESSION['success'] = 'User assigned successfully! Waiting for admin approval.';
            redirect('assign_users.php');
        } catch (Exception $e) {
            $_SESSION['error'] = 'Failed to assign user.';
        }
    } else {
        $_SESSION['error'] = implode('<br>', $errors);
    }
}

// Get available campaigns for influencer
$admin_campaigns = $db->fetchAll("
    SELECT c.*, ic.status as assignment_status
    FROM campaigns c
    JOIN influencer_campaigns ic ON c.id = ic.campaign_id
    WHERE ic.influencer_id = ? AND ic.status = 'accepted' AND c.status = 'active'
", [$influencer_id]);

$brand_campaigns = $db->fetchAll("
    SELECT bc.*, bia.status as assignment_status
    FROM brand_campaigns bc
    JOIN brand_influencer_assignments bia ON bc.id = bia.brand_campaign_id
    WHERE bia.influencer_id = ? AND bia.status = 'accepted' AND bc.status = 'active'
", [$influencer_id]);

// Get available users
$users = $db->fetchAll("
    SELECT u.*, COUNT(iua.id) as assigned_campaigns
    FROM users u
    LEFT JOIN influencer_user_assignments iua ON u.id = iua.user_id
    WHERE u.status = 'active'
    GROUP BY u.id
    ORDER BY u.full_name ASC
");

// Get recent assignments
$recent_assignments = $db->fetchAll("
    SELECT iua.*, u.full_name, u.username, u.instagram_handle,
           c.title as admin_campaign_title,
           bc.title as brand_campaign_title
    FROM influencer_user_assignments iua
    JOIN users u ON iua.user_id = u.id
    LEFT JOIN campaigns c ON iua.campaign_id = c.id
    LEFT JOIN brand_campaigns bc ON iua.brand_campaign_id = bc.id
    WHERE iua.influencer_id = ?
    ORDER BY iua.assigned_by_influencer_at DESC
    LIMIT 10
", [$influencer_id]);

$page_title = 'Assign Campaigns to Users';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-xl-2 sidebar p-0">
            <div class="p-3">
                <h5 class="text-white mb-4">
                    <i class="fas fa-star me-2"></i>Influencer Panel
                </h5>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a class="nav-link" href="campaigns.php">
                        <i class="fas fa-bullhorn"></i>Campaigns
                    </a>
                    <a class="nav-link" href="collaborations.php">
                        <i class="fas fa-handshake"></i>Collaborations
                    </a>
                    <a class="nav-link active" href="assign_users.php">
                        <i class="fas fa-user-plus"></i>Assign to Users
                    </a>
                    <a class="nav-link" href="analytics.php">
                        <i class="fas fa-chart-line"></i>Analytics
                    </a>
                    <a class="nav-link" href="wallet.php">
                        <i class="fas fa-wallet"></i>Wallet
                    </a>
                    <a class="nav-link" href="profile.php">
                        <i class="fas fa-user-edit"></i>Profile
                    </a>
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10 main-content" style="margin-left: auto;">
            <!-- Top Bar -->
            <div class="bg-white shadow-sm p-3 mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">Assign Campaigns to Users</h4>
                        <small class="text-muted">Assign your accepted campaigns to users (requires admin approval)</small>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#assignUserModal">
                            <i class="fas fa-plus me-2"></i>Assign User
                        </button>
                        <button class="btn btn-outline-primary d-lg-none ms-2" onclick="toggleSidebar()">
                            <i class="fas fa-bars"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="container-fluid py-4">
                <div class="row">
                    <!-- Recent Assignments -->
                    <div class="col-lg-8 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-list me-2"></i>Recent Assignments
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recent_assignments)): ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-user-plus fa-3x text-gray-300 mb-3"></i>
                                        <h6 class="text-muted">No assignments yet</h6>
                                        <p class="text-muted">Start assigning campaigns to users to see them here.</p>
                                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#assignUserModal">
                                            <i class="fas fa-plus me-2"></i>Assign First User
                                        </button>
                                    </div>
                                <?php else: ?>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>User</th>
                                                    <th>Campaign</th>
                                                    <th>Type</th>
                                                    <th>Reward</th>
                                                    <th>Status</th>
                                                    <th>Assigned</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($recent_assignments as $assignment): ?>
                                                    <tr>
                                                        <td>
                                                            <div>
                                                                <strong><?php echo htmlspecialchars($assignment['full_name']); ?></strong>
                                                                <br>
                                                                <small class="text-muted">@<?php echo htmlspecialchars($assignment['username']); ?></small>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <?php 
                                                            $campaign_title = $assignment['assignment_type'] === 'admin_campaign' ? 
                                                                $assignment['admin_campaign_title'] : $assignment['brand_campaign_title'];
                                                            echo htmlspecialchars($campaign_title);
                                                            ?>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-<?php echo $assignment['assignment_type'] === 'admin_campaign' ? 'primary' : 'info'; ?>">
                                                                <?php echo $assignment['assignment_type'] === 'admin_campaign' ? 'Admin' : 'Brand'; ?>
                                                            </span>
                                                        </td>
                                                        <td>₹<?php echo number_format($assignment['reward_amount'], 2); ?></td>
                                                        <td>
                                                            <span class="badge bg-<?php 
                                                                echo $assignment['status'] === 'approved' ? 'success' : 
                                                                    ($assignment['status'] === 'rejected' ? 'danger' : 'warning'); 
                                                            ?>">
                                                                <?php echo ucfirst(str_replace('_', ' ', $assignment['status'])); ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <small class="text-muted">
                                                                <?php echo time_ago($assignment['assigned_by_influencer_at']); ?>
                                                            </small>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Available Campaigns -->
                    <div class="col-lg-4 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-bullhorn me-2"></i>Available Campaigns
                                </h5>
                            </div>
                            <div class="card-body">
                                <h6 class="text-primary">Admin Campaigns</h6>
                                <?php if (empty($admin_campaigns)): ?>
                                    <p class="text-muted small">No admin campaigns available.</p>
                                <?php else: ?>
                                    <?php foreach ($admin_campaigns as $campaign): ?>
                                        <div class="border rounded p-2 mb-2">
                                            <strong class="small"><?php echo htmlspecialchars($campaign['title']); ?></strong>
                                            <br>
                                            <small class="text-success">₹<?php echo number_format($campaign['reward_amount'], 2); ?></small>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>

                                <h6 class="text-info mt-3">Brand Campaigns</h6>
                                <?php if (empty($brand_campaigns)): ?>
                                    <p class="text-muted small">No brand campaigns available.</p>
                                <?php else: ?>
                                    <?php foreach ($brand_campaigns as $campaign): ?>
                                        <div class="border rounded p-2 mb-2">
                                            <strong class="small"><?php echo htmlspecialchars($campaign['title']); ?></strong>
                                            <br>
                                            <small class="text-success">₹<?php echo number_format($campaign['reward_amount'], 2); ?></small>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Assign User Modal -->
<div class="modal fade" id="assignUserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Assign Campaign to User</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="user_id" class="form-label">Select User *</label>
                                <select class="form-select" id="user_id" name="user_id" required>
                                    <option value="">Choose a user...</option>
                                    <?php foreach ($users as $user): ?>
                                        <option value="<?php echo $user['id']; ?>">
                                            <?php echo htmlspecialchars($user['full_name']); ?> 
                                            (@<?php echo htmlspecialchars($user['username']); ?>)
                                            - <?php echo $user['assigned_campaigns']; ?> campaigns
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="assignment_type" class="form-label">Campaign Type *</label>
                                <select class="form-select" id="assignment_type" name="assignment_type" required onchange="toggleCampaigns()">
                                    <option value="">Select type...</option>
                                    <option value="admin_campaign">Admin Campaign</option>
                                    <option value="brand_campaign">Brand Campaign</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3" id="admin_campaign_div" style="display: none;">
                        <label for="campaign_id" class="form-label">Admin Campaign</label>
                        <select class="form-select" id="campaign_id" name="campaign_id">
                            <option value="">Choose campaign...</option>
                            <?php foreach ($admin_campaigns as $campaign): ?>
                                <option value="<?php echo $campaign['id']; ?>" data-reward="<?php echo $campaign['reward_amount']; ?>">
                                    <?php echo htmlspecialchars($campaign['title']); ?> - ₹<?php echo number_format($campaign['reward_amount'], 2); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="mb-3" id="brand_campaign_div" style="display: none;">
                        <label for="brand_campaign_id" class="form-label">Brand Campaign</label>
                        <select class="form-select" id="brand_campaign_id" name="brand_campaign_id">
                            <option value="">Choose campaign...</option>
                            <?php foreach ($brand_campaigns as $campaign): ?>
                                <option value="<?php echo $campaign['id']; ?>" data-reward="<?php echo $campaign['reward_amount']; ?>">
                                    <?php echo htmlspecialchars($campaign['title']); ?> - ₹<?php echo number_format($campaign['reward_amount'], 2); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="reward_amount" class="form-label">Reward Amount (₹) *</label>
                        <input type="number" class="form-control" id="reward_amount" name="reward_amount" 
                               min="50" step="10" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="influencer_notes" class="form-label">Instructions for User</label>
                        <textarea class="form-control" id="influencer_notes" name="influencer_notes" rows="3" 
                                  placeholder="Provide specific instructions or requirements for the user..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="assign_user" class="btn btn-primary">Assign User</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function toggleSidebar() {
    document.querySelector('.sidebar').classList.toggle('show');
}

function toggleCampaigns() {
    const assignmentType = document.getElementById('assignment_type').value;
    const adminDiv = document.getElementById('admin_campaign_div');
    const brandDiv = document.getElementById('brand_campaign_div');
    
    if (assignmentType === 'admin_campaign') {
        adminDiv.style.display = 'block';
        brandDiv.style.display = 'none';
        document.getElementById('brand_campaign_id').value = '';
    } else if (assignmentType === 'brand_campaign') {
        adminDiv.style.display = 'none';
        brandDiv.style.display = 'block';
        document.getElementById('campaign_id').value = '';
    } else {
        adminDiv.style.display = 'none';
        brandDiv.style.display = 'none';
        document.getElementById('campaign_id').value = '';
        document.getElementById('brand_campaign_id').value = '';
    }
}

// Auto-fill reward amount when campaign is selected
document.getElementById('campaign_id').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    if (selectedOption.dataset.reward) {
        document.getElementById('reward_amount').value = selectedOption.dataset.reward;
    }
});

document.getElementById('brand_campaign_id').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    if (selectedOption.dataset.reward) {
        document.getElementById('reward_amount').value = selectedOption.dataset.reward;
    }
});
</script>

<?php include '../includes/footer.php'; ?>
