<?php
require_once '../config.php';
require_once '../includes/instagram_verification.php';
require_login(['influencer']);
require_instagram_verification('influencer');

$db = Database::getInstance();
$influencer_id = get_user_id();

// Check if influencer is approved
$influencer = $db->fetch("SELECT status FROM influencers WHERE id = ?", [$influencer_id]);
if ($influencer['status'] !== 'approved') {
    redirect('dashboard.php');
}

// Handle campaign application
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['apply_campaign'])) {
    $campaign_id = intval($_POST['campaign_id']);
    $proposal = sanitize_input($_POST['proposal']);
    
    // Check if campaign exists and is available
    $campaign = $db->fetch("
        SELECT c.*, (c.max_participants - c.current_participants) as slots_remaining
        FROM campaigns c
        WHERE c.id = ? AND c.status = 'active' 
        AND c.campaign_type IN ('influencer_campaign', 'both')
        AND c.end_date >= CURDATE()
        AND c.current_participants < c.max_participants
    ", [$campaign_id]);
    
    if ($campaign && $campaign['slots_remaining'] > 0) {
        // Check if influencer already applied
        $existing_application = $db->fetch("SELECT id FROM influencer_campaigns WHERE influencer_id = ? AND campaign_id = ?", [$influencer_id, $campaign_id]);
        
        if (!$existing_application) {
            try {
                // Create campaign application
                $application_id = $db->insert('influencer_campaigns', [
                    'influencer_id' => $influencer_id,
                    'campaign_id' => $campaign_id,
                    'status' => 'pending',
                    'proposal' => $proposal,
                    'reward_amount' => $campaign['reward_amount']
                ]);
                
                $_SESSION['success'] = 'Application submitted successfully! You will be notified once reviewed.';
                redirect('collaborations.php');
            } catch (Exception $e) {
                $_SESSION['error'] = 'Failed to submit application. Please try again.';
            }
        } else {
            $_SESSION['warning'] = 'You have already applied for this campaign.';
        }
    } else {
        $_SESSION['error'] = 'Campaign is no longer available.';
    }
}

// Get filter parameters
$category = $_GET['category'] ?? '';
$min_reward = $_GET['min_reward'] ?? '';
$max_reward = $_GET['max_reward'] ?? '';
$search = $_GET['search'] ?? '';

// Build query conditions
$conditions = [
    "c.status = 'active'",
    "c.campaign_type IN ('influencer_campaign', 'both')",
    "c.end_date >= CURDATE()",
    "c.current_participants < c.max_participants",
    "c.id NOT IN (SELECT campaign_id FROM influencer_campaigns WHERE influencer_id = ?)"
];
$params = [$influencer_id];

if ($search) {
    $conditions[] = "(c.title LIKE ? OR c.description LIKE ? OR b.company_name LIKE ?)";
    $search_param = "%{$search}%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

if ($min_reward) {
    $conditions[] = "c.reward_amount >= ?";
    $params[] = floatval($min_reward);
}

if ($max_reward) {
    $conditions[] = "c.reward_amount <= ?";
    $params[] = floatval($max_reward);
}

$where_clause = implode(' AND ', $conditions);

// Get available campaigns
$campaigns = $db->fetchAll("
    SELECT c.*, b.company_name as brand_name, b.logo as brand_logo,
           (c.max_participants - c.current_participants) as slots_remaining
    FROM campaigns c
    LEFT JOIN brands b ON c.brand_id = b.id
    WHERE {$where_clause}
    ORDER BY c.created_at DESC
", $params);

$page_title = 'Available Campaigns';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-xl-2 sidebar p-0">
            <div class="p-3">
                <h5 class="text-white mb-4">
                    <i class="fas fa-star me-2"></i>Influencer Panel
                </h5>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a class="nav-link active" href="campaigns.php">
                        <i class="fas fa-bullhorn"></i>Campaigns
                    </a>
                    <a class="nav-link" href="collaborations.php">
                        <i class="fas fa-handshake"></i>Collaborations
                    </a>
                    <a class="nav-link" href="analytics.php">
                        <i class="fas fa-chart-line"></i>Analytics
                    </a>
                    <a class="nav-link" href="wallet.php">
                        <i class="fas fa-wallet"></i>Wallet
                    </a>
                    <a class="nav-link" href="profile.php">
                        <i class="fas fa-user-edit"></i>Profile
                    </a>
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10 main-content" style="margin-left: auto;">
            <!-- Top Bar -->
            <div class="bg-white shadow-sm p-3 mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">Available Campaigns</h4>
                        <small class="text-muted">Discover brand collaboration opportunities</small>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-outline-primary d-lg-none" onclick="toggleSidebar()">
                            <i class="fas fa-bars"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="p-3">
                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-filter me-2"></i>Filter Campaigns
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="<?php echo htmlspecialchars($search); ?>" 
                                       placeholder="Search campaigns...">
                            </div>
                            
                            <div class="col-md-2">
                                <label for="min_reward" class="form-label">Min Reward</label>
                                <input type="number" class="form-control" id="min_reward" name="min_reward" 
                                       value="<?php echo htmlspecialchars($min_reward); ?>" 
                                       placeholder="₹500">
                            </div>
                            
                            <div class="col-md-2">
                                <label for="max_reward" class="form-label">Max Reward</label>
                                <input type="number" class="form-control" id="max_reward" name="max_reward" 
                                       value="<?php echo htmlspecialchars($max_reward); ?>" 
                                       placeholder="₹10000">
                            </div>
                            
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                <a href="campaigns.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Campaign Results -->
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="mb-0">
                                <i class="fas fa-bullhorn me-2"></i>
                                Available Campaigns (<?php echo count($campaigns); ?>)
                            </h5>
                        </div>
                        
                        <?php if (empty($campaigns)): ?>
                            <div class="card">
                                <div class="card-body text-center py-5">
                                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">No campaigns found</h5>
                                    <p class="text-muted">Try adjusting your filters or check back later for new opportunities.</p>
                                    <a href="campaigns.php" class="btn btn-primary">
                                        <i class="fas fa-refresh me-2"></i>Clear Filters
                                    </a>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="row">
                                <?php foreach ($campaigns as $campaign): ?>
                                    <div class="col-lg-6 col-xl-4 mb-4">
                                        <div class="card h-100 border-0 shadow-sm campaign-card">
                                            <div class="card-header bg-white border-0 pb-0">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div class="d-flex align-items-center">
                                                        <?php if ($campaign['brand_logo']): ?>
                                                            <img src="<?php echo htmlspecialchars($campaign['brand_logo']); ?>" 
                                                                 alt="Brand Logo" class="rounded-circle me-2" 
                                                                 style="width: 40px; height: 40px; object-fit: cover;">
                                                        <?php else: ?>
                                                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-2" 
                                                                 style="width: 40px; height: 40px;">
                                                                <i class="fas fa-building text-white"></i>
                                                            </div>
                                                        <?php endif; ?>
                                                        <div>
                                                            <h6 class="mb-0"><?php echo htmlspecialchars($campaign['title']); ?></h6>
                                                            <?php if ($campaign['brand_name']): ?>
                                                                <small class="text-muted"><?php echo htmlspecialchars($campaign['brand_name']); ?></small>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                    <span class="badge bg-success fs-6"><?php echo format_currency($campaign['reward_amount']); ?></span>
                                                </div>
                                            </div>
                                            
                                            <div class="card-body">
                                                <p class="text-muted mb-3"><?php echo substr(htmlspecialchars($campaign['description']), 0, 120); ?>...</p>
                                                
                                                <div class="row text-center mb-3">
                                                    <div class="col-4">
                                                        <small class="text-muted d-block">Format</small>
                                                        <span class="badge bg-info"><?php echo ucfirst($campaign['post_format']); ?></span>
                                                    </div>
                                                    <div class="col-4">
                                                        <small class="text-muted d-block">Slots Left</small>
                                                        <strong class="text-warning"><?php echo $campaign['slots_remaining']; ?></strong>
                                                    </div>
                                                    <div class="col-4">
                                                        <small class="text-muted d-block">Deadline</small>
                                                        <strong class="text-danger"><?php echo date('M j', strtotime($campaign['end_date'])); ?></strong>
                                                    </div>
                                                </div>
                                                
                                                <?php if ($campaign['target_audience']): ?>
                                                    <div class="mb-3">
                                                        <small class="text-muted">Target: </small>
                                                        <span class="badge bg-light text-dark"><?php echo htmlspecialchars($campaign['target_audience']); ?></span>
                                                    </div>
                                                <?php endif; ?>
                                                
                                                <?php if ($campaign['hashtags']): ?>
                                                    <div class="mb-3">
                                                        <small class="text-muted d-block">Required Hashtags:</small>
                                                        <small class="text-primary"><?php echo htmlspecialchars($campaign['hashtags']); ?></small>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            
                                            <div class="card-footer bg-white border-0">
                                                <button class="btn btn-primary w-100" data-bs-toggle="modal" 
                                                        data-bs-target="#applyModal<?php echo $campaign['id']; ?>">
                                                    <i class="fas fa-hand-paper me-2"></i>Apply for Campaign
                                                </button>
                                                
                                                <div class="text-center mt-2">
                                                    <button class="btn btn-link btn-sm" data-bs-toggle="modal" 
                                                            data-bs-target="#campaignModal<?php echo $campaign['id']; ?>">
                                                        <i class="fas fa-info-circle me-1"></i>View Details
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Apply Modal -->
                                    <div class="modal fade" id="applyModal<?php echo $campaign['id']; ?>" tabindex="-1">
                                        <div class="modal-dialog modal-lg">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title">Apply for Campaign</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                </div>
                                                <form method="POST">
                                                    <div class="modal-body">
                                                        <div class="mb-3">
                                                            <h6><?php echo htmlspecialchars($campaign['title']); ?></h6>
                                                            <p class="text-muted"><?php echo htmlspecialchars($campaign['description']); ?></p>
                                                            <p><strong>Reward:</strong> <span class="text-success"><?php echo format_currency($campaign['reward_amount']); ?></span></p>
                                                        </div>
                                                        
                                                        <div class="mb-3">
                                                            <label for="proposal<?php echo $campaign['id']; ?>" class="form-label">Your Proposal</label>
                                                            <textarea class="form-control" id="proposal<?php echo $campaign['id']; ?>" 
                                                                      name="proposal" rows="4" required
                                                                      placeholder="Describe how you plan to promote this campaign, your content strategy, expected reach, etc."></textarea>
                                                            <div class="form-text">Explain why you're the perfect fit for this campaign</div>
                                                        </div>
                                                        
                                                        <input type="hidden" name="campaign_id" value="<?php echo $campaign['id']; ?>">
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                        <button type="submit" name="apply_campaign" class="btn btn-primary">
                                                            <i class="fas fa-paper-plane me-2"></i>Submit Application
                                                        </button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Campaign Details Modal -->
                                    <div class="modal fade" id="campaignModal<?php echo $campaign['id']; ?>" tabindex="-1">
                                        <div class="modal-dialog modal-lg">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title"><?php echo htmlspecialchars($campaign['title']); ?></h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="row mb-3">
                                                        <div class="col-md-6">
                                                            <strong>Brand:</strong> <?php echo htmlspecialchars($campaign['brand_name'] ?? 'RealEarners'); ?>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <strong>Reward:</strong> <span class="text-success"><?php echo format_currency($campaign['reward_amount']); ?></span>
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="row mb-3">
                                                        <div class="col-md-6">
                                                            <strong>Post Format:</strong> <?php echo ucfirst($campaign['post_format']); ?>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <strong>Deadline:</strong> <?php echo date('F j, Y', strtotime($campaign['end_date'])); ?>
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="mb-3">
                                                        <strong>Description:</strong>
                                                        <p class="mt-2"><?php echo nl2br(htmlspecialchars($campaign['description'])); ?></p>
                                                    </div>
                                                    
                                                    <?php if ($campaign['requirements']): ?>
                                                        <div class="mb-3">
                                                            <strong>Requirements:</strong>
                                                            <p class="mt-2"><?php echo nl2br(htmlspecialchars($campaign['requirements'])); ?></p>
                                                        </div>
                                                    <?php endif; ?>
                                                    
                                                    <?php if ($campaign['hashtags']): ?>
                                                        <div class="mb-3">
                                                            <strong>Required Hashtags:</strong>
                                                            <p class="mt-2 text-primary"><?php echo htmlspecialchars($campaign['hashtags']); ?></p>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal" 
                                                            data-bs-toggle="modal" data-bs-target="#applyModal<?php echo $campaign['id']; ?>">
                                                        <i class="fas fa-hand-paper me-2"></i>Apply Now
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
