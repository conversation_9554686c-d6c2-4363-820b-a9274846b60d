<?php
require_once '../config.php';
require_login(['influencer']);

$db = Database::getInstance();
$influencer_id = get_user_id();

// Check if influencer is approved
$influencer = $db->fetch("SELECT * FROM influencers WHERE id = ?", [$influencer_id]);
if ($influencer['status'] !== 'approved') {
    redirect('dashboard.php');
}

// Handle submission upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_work'])) {
    $campaign_assignment_id = intval($_POST['campaign_assignment_id']);
    $post_url = sanitize_input($_POST['post_url']);
    $caption = sanitize_input($_POST['caption']);
    $hashtags_used = sanitize_input($_POST['hashtags_used']);
    $reach_count = intval($_POST['reach_count']);
    $likes_count = intval($_POST['likes_count']);
    $comments_count = intval($_POST['comments_count']);
    $shares_count = intval($_POST['shares_count']);
    $submission_notes = sanitize_input($_POST['submission_notes']);
    
    $errors = [];
    
    if (empty($post_url)) {
        $errors[] = 'Post URL is required.';
    }
    
    if (empty($caption)) {
        $errors[] = 'Caption is required.';
    }
    
    // Handle screenshot upload
    $screenshot_path = '';
    if (isset($_FILES['screenshot']) && $_FILES['screenshot']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = 'uploads/submissions/';
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }
        
        $file_extension = strtolower(pathinfo($_FILES['screenshot']['name'], PATHINFO_EXTENSION));
        $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];
        
        if (in_array($file_extension, $allowed_extensions)) {
            $filename = 'submission_' . $campaign_assignment_id . '_' . time() . '.' . $file_extension;
            $screenshot_path = $upload_dir . $filename;
            
            if (!move_uploaded_file($_FILES['screenshot']['tmp_name'], $screenshot_path)) {
                $errors[] = 'Failed to upload screenshot.';
            }
        } else {
            $errors[] = 'Invalid file type. Please upload JPG, PNG, or GIF files only.';
        }
    }
    
    if (empty($errors)) {
        try {
            // Create submission
            $submission_id = $db->insert('influencer_submissions', [
                'campaign_assignment_id' => $campaign_assignment_id,
                'influencer_id' => $influencer_id,
                'campaign_id' => $_POST['campaign_id'],
                'post_url' => $post_url,
                'screenshot_path' => $screenshot_path,
                'caption' => $caption,
                'hashtags_used' => $hashtags_used,
                'reach_count' => $reach_count,
                'likes_count' => $likes_count,
                'comments_count' => $comments_count,
                'shares_count' => $shares_count,
                'submission_notes' => $submission_notes
            ]);
            
            // Update campaign assignment status
            $db->update('influencer_campaigns', [
                'status' => 'submitted',
                'submitted_at' => date('Y-m-d H:i:s')
            ], 'id = ?', [$campaign_assignment_id]);
            
            $_SESSION['success'] = 'Work submitted successfully! Your submission is now under review.';
            redirect('collaborations.php');
        } catch (Exception $e) {
            $_SESSION['error'] = 'Failed to submit work. Please try again.';
        }
    } else {
        $_SESSION['error'] = implode('<br>', $errors);
    }
}

// Handle campaign acceptance/decline
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['respond_to_campaign'])) {
    $campaign_assignment_id = intval($_POST['campaign_assignment_id']);
    $response = $_POST['response']; // 'accepted' or 'declined'
    
    if (in_array($response, ['accepted', 'declined'])) {
        try {
            $db->update('influencer_campaigns', [
                'status' => $response,
                'accepted_at' => $response === 'accepted' ? date('Y-m-d H:i:s') : null
            ], 'id = ? AND influencer_id = ?', [$campaign_assignment_id, $influencer_id]);
            
            $message = $response === 'accepted' ? 'Campaign accepted successfully!' : 'Campaign declined.';
            $_SESSION['success'] = $message;
        } catch (Exception $e) {
            $_SESSION['error'] = 'Failed to update campaign status.';
        }
    }
    redirect('collaborations.php');
}

// Get filter parameters
$status_filter = $_GET['status'] ?? 'all';
$search = $_GET['search'] ?? '';

// Build query conditions
$conditions = ["ic.influencer_id = ?"];
$params = [$influencer_id];

if ($status_filter !== 'all') {
    $conditions[] = "ic.status = ?";
    $params[] = $status_filter;
}

if ($search) {
    $conditions[] = "(c.title LIKE ? OR b.name LIKE ?)";
    $search_param = "%{$search}%";
    $params[] = $search_param;
    $params[] = $search_param;
}

$where_clause = implode(' AND ', $conditions);

// Get collaborations
$collaborations = $db->fetchAll("
    SELECT ic.*, c.title, c.description, c.requirements, c.hashtags, c.start_date, c.end_date,
           b.name as brand_name, b.logo as brand_logo,
           s.post_url, s.screenshot_path, s.submitted_at as submission_date
    FROM influencer_campaigns ic
    JOIN campaigns c ON ic.campaign_id = c.id
    LEFT JOIN brands b ON c.brand_id = b.id
    LEFT JOIN influencer_submissions s ON ic.id = s.campaign_assignment_id
    WHERE {$where_clause}
    ORDER BY ic.assigned_at DESC
", $params);

// Get statistics
$stats = [
    'total' => $db->fetch("SELECT COUNT(*) as count FROM influencer_campaigns WHERE influencer_id = ?", [$influencer_id])['count'],
    'pending' => $db->fetch("SELECT COUNT(*) as count FROM influencer_campaigns WHERE influencer_id = ? AND status = 'invited'", [$influencer_id])['count'],
    'active' => $db->fetch("SELECT COUNT(*) as count FROM influencer_campaigns WHERE influencer_id = ? AND status = 'accepted'", [$influencer_id])['count'],
    'completed' => $db->fetch("SELECT COUNT(*) as count FROM influencer_campaigns WHERE influencer_id = ? AND status IN ('approved', 'paid')", [$influencer_id])['count']
];

$page_title = 'My Collaborations';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-xl-2 sidebar p-0">
            <div class="p-3">
                <h5 class="text-white mb-4">
                    <i class="fas fa-star me-2"></i>Influencer Panel
                </h5>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a class="nav-link" href="campaigns.php">
                        <i class="fas fa-bullhorn"></i>Campaigns
                    </a>
                    <a class="nav-link active" href="collaborations.php">
                        <i class="fas fa-handshake"></i>Collaborations
                    </a>
                    <a class="nav-link" href="analytics.php">
                        <i class="fas fa-chart-line"></i>Analytics
                    </a>
                    <a class="nav-link" href="wallet.php">
                        <i class="fas fa-wallet"></i>Wallet
                    </a>
                    <a class="nav-link" href="profile.php">
                        <i class="fas fa-user-edit"></i>Profile
                    </a>
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10 main-content" style="margin-left: auto;">
            <!-- Top Bar -->
            <div class="bg-white shadow-sm p-3 mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">My Collaborations</h4>
                        <small class="text-muted">Manage your brand partnerships and submissions</small>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-outline-primary d-lg-none" onclick="toggleSidebar()">
                            <i class="fas fa-bars"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="p-3">
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <i class="fas fa-handshake fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['total']; ?></h3>
                                <p class="mb-0">Total Collaborations</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card warning">
                            <div class="card-body text-center">
                                <i class="fas fa-clock fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['pending']; ?></h3>
                                <p class="mb-0">Pending Response</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card info">
                            <div class="card-body text-center">
                                <i class="fas fa-play-circle fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['active']; ?></h3>
                                <p class="mb-0">Active</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card success">
                            <div class="card-body text-center">
                                <i class="fas fa-check-circle fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['completed']; ?></h3>
                                <p class="mb-0">Completed</p>
                            </div>
                        </div>
                    </div>
                </div>
