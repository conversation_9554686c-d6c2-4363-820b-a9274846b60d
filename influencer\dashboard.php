<?php
require_once '../config.php';
require_once '../includes/instagram_verification.php';
require_once '../includes/badge_system.php';
require_login(['influencer']);
require_instagram_verification('influencer');

$db = Database::getInstance();
$influencer_id = get_user_id();

// Get influencer data
$influencer = $db->fetch("SELECT * FROM influencers WHERE id = ?", [$influencer_id]);

// Get verification status
$verification_system = new InstagramVerification();
$verification_status = $verification_system->getVerificationStatus($influencer_id, 'influencer');

// Check if influencer exists
if (!$influencer) {
    $_SESSION['error'] = 'Influencer account not found. Please contact support.';
    redirect('../auth/logout.php');
}

// Check if influencer is approved
if ($influencer['status'] !== 'approved') {
    $page_title = 'Account Status';
    include '../includes/header.php';
    ?>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body text-center">
                        <?php if ($influencer['status'] === 'pending'): ?>
                            <i class="fas fa-clock fa-3x text-warning mb-3"></i>
                            <h4>Account Pending Approval</h4>
                            <p class="text-muted">Your influencer account is currently under review. You will be notified once approved.</p>
                        <?php elseif ($influencer['status'] === 'rejected'): ?>
                            <i class="fas fa-times-circle fa-3x text-danger mb-3"></i>
                            <h4>Account Rejected</h4>
                            <p class="text-muted">Your influencer application has been rejected. Please contact support for more information.</p>
                        <?php else: ?>
                            <i class="fas fa-ban fa-3x text-secondary mb-3"></i>
                            <h4>Account Suspended</h4>
                            <p class="text-muted">Your account has been suspended. Please contact support.</p>
                        <?php endif; ?>
                        <a href="../auth/logout.php" class="btn btn-primary">Logout</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php include '../includes/footer.php'; exit; ?>
<?php } ?>

<?php
// Get dashboard statistics
$stats = [
    'total_campaigns' => $db->fetch("SELECT COUNT(*) as count FROM influencer_campaigns WHERE influencer_id = ?", [$influencer_id])['count'],
    'active_campaigns' => $db->fetch("SELECT COUNT(*) as count FROM influencer_campaigns WHERE influencer_id = ? AND status = 'active'", [$influencer_id])['count'],
    'completed_campaigns' => $db->fetch("SELECT COUNT(*) as count FROM influencer_campaigns WHERE influencer_id = ? AND status = 'completed'", [$influencer_id])['count'],
    'total_earned' => $influencer['total_earned'],
    'pending_payout' => $influencer['pending_payout']
];

// Get recent campaigns
$recent_campaigns = $db->fetchAll("
    SELECT ic.*, c.title, c.description, c.reward_amount, c.end_date, b.company_name as brand_name
    FROM influencer_campaigns ic
    JOIN campaigns c ON ic.campaign_id = c.id
    LEFT JOIN brands b ON c.brand_id = b.id
    WHERE ic.influencer_id = ?
    ORDER BY ic.assigned_at DESC
    LIMIT 10
", [$influencer_id]);

// Get available campaigns
$available_campaigns = $db->fetchAll("
    SELECT c.*, b.company_name as brand_name,
           (c.max_participants - c.current_participants) as slots_remaining
    FROM campaigns c
    LEFT JOIN brands b ON c.brand_id = b.id
    WHERE c.status = 'active'
    AND c.campaign_type IN ('influencer_campaign', 'both')
    AND c.end_date >= CURDATE()
    AND c.current_participants < c.max_participants
    AND c.id NOT IN (
        SELECT campaign_id FROM influencer_campaigns WHERE influencer_id = ?
    )
    ORDER BY c.created_at DESC
    LIMIT 6
", [$influencer_id]);

$page_title = 'Influencer Dashboard';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-xl-2 sidebar p-0">
            <div class="p-3">
                <h5 class="text-white mb-3">
                    <i class="fas fa-star me-2"></i>Influencer Panel
                </h5>

                <!-- Verification Status in Sidebar -->
                <?php if ($verification_status['verified']): ?>
                    <div class="alert alert-success py-2 px-3 mb-3" style="font-size: 0.875rem;">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>Verified</strong>
                    </div>
                <?php elseif ($verification_status['verification_status'] === 'pending'): ?>
                    <div class="alert alert-warning py-2 px-3 mb-3" style="font-size: 0.875rem;">
                        <i class="fas fa-clock me-2"></i>
                        <strong>Verification Pending</strong>
                    </div>
                <?php else: ?>
                    <div class="alert alert-danger py-2 px-3 mb-3" style="font-size: 0.875rem;">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Verification Required</strong>
                    </div>
                <?php endif; ?>
                
                <nav class="nav flex-column">
                    <a class="nav-link active" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a class="nav-link" href="campaigns.php">
                        <i class="fas fa-bullhorn"></i>Campaigns
                    </a>
                    <a class="nav-link" href="collaborations.php">
                        <i class="fas fa-handshake"></i>Collaborations
                    </a>
                    <a class="nav-link" href="assign_users.php">
                        <i class="fas fa-user-plus"></i>Assign to Users
                    </a>
                    <a class="nav-link" href="analytics.php">
                        <i class="fas fa-chart-line"></i>Analytics
                    </a>
                    <a class="nav-link" href="wallet.php">
                        <i class="fas fa-wallet"></i>Wallet
                    </a>
                    <a class="nav-link" href="profile.php">
                        <i class="fas fa-user-edit"></i>Profile
                    </a>
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10 main-content" style="margin-left: auto;">
            <!-- Success Message for New Verification Submission -->
            <?php if (isset($_SESSION['verification_submitted']) && $_SESSION['verification_submitted']): ?>
                <div class="alert alert-success alert-dismissible fade show mb-4" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-check-circle fa-2x me-3 text-success"></i>
                        <div>
                            <h6 class="alert-heading mb-1">Verification Submitted Successfully!</h6>
                            <p class="mb-0">
                                Your Instagram verification request has been submitted. You now have access to your dashboard
                                while our team verifies your follows. This usually takes up to 24 hours.
                            </p>
                        </div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php unset($_SESSION['verification_submitted']); ?>
            <?php endif; ?>

            <!-- Instagram Verification Status Banner -->

            <?php if (!$verification_status['verified']): ?>
                <div class="alert alert-warning alert-dismissible fade show mb-4" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fab fa-instagram fa-2x me-3"></i>
                        <div class="flex-grow-1">
                            <?php if ($verification_status['verification_status'] === 'pending'): ?>
                                <h6 class="alert-heading mb-1">
                                    <i class="fas fa-clock me-2"></i>Instagram Verification Pending
                                </h6>
                                <p class="mb-2">
                                    Your Instagram verification is being reviewed. You have limited access until verification is complete.
                                </p>
                                <small class="text-muted">
                                    <strong>Submitted:</strong> <?php echo date('M j, Y g:i A', strtotime($verification_status['submitted_at'])); ?>
                                    | <strong>Instagram:</strong> @<?php echo htmlspecialchars($verification_status['instagram_username']); ?>
                                </small>
                            <?php else: ?>
                                <h6 class="alert-heading mb-1">
                                    <i class="fas fa-exclamation-triangle me-2"></i>Instagram Verification Required
                                </h6>
                                <p class="mb-2">
                                    You need to follow our Instagram accounts to unlock all features.
                                </p>
                                <a href="../auth/instagram_verification.php" class="btn btn-warning btn-sm">
                                    <i class="fab fa-instagram me-1"></i>Complete Verification
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Top Bar -->
            <div class="bg-white shadow-sm p-3 mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">Welcome back, <?php echo htmlspecialchars($influencer['full_name']); ?>!</h4>
                        <small class="text-muted">Here's your influencer performance overview</small>

                        <!-- Influencer Badges -->
                        <?php
                        $badge_system = new BadgeSystem();
                        $influencer_badges = $badge_system->getUserBadges(get_user_id(), 'influencer');
                        ?>
                        <?php if (!empty($influencer_badges)): ?>
                            <div class="badge-container mt-2">
                                <?php foreach ($influencer_badges as $badge): ?>
                                    <?php echo $badge_system->renderBadge($badge, 'sm'); ?>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-outline-primary d-lg-none" onclick="toggleSidebar()">
                            <i class="fas fa-bars"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="p-3">
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <i class="fas fa-bullhorn fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['total_campaigns']; ?></h3>
                                <p class="mb-0">Total Campaigns</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card warning">
                            <div class="card-body text-center">
                                <i class="fas fa-play-circle fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['active_campaigns']; ?></h3>
                                <p class="mb-0">Active</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card success">
                            <div class="card-body text-center">
                                <i class="fas fa-check-circle fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['completed_campaigns']; ?></h3>
                                <p class="mb-0">Completed</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card info">
                            <div class="card-body text-center">
                                <i class="fas fa-rupee-sign fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo format_currency($stats['total_earned']); ?></h3>
                                <p class="mb-0">Total Earned</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Profile Summary -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-user-circle me-2"></i>Profile Summary
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-2 text-center">
                                        <?php if ($influencer['profile_image']): ?>
                                            <img src="<?php echo htmlspecialchars($influencer['profile_image']); ?>" 
                                                 alt="Profile" class="rounded-circle" 
                                                 style="width: 80px; height: 80px; object-fit: cover;">
                                        <?php else: ?>
                                            <div class="bg-warning rounded-circle d-inline-flex align-items-center justify-content-center" 
                                                 style="width: 80px; height: 80px;">
                                                <i class="fas fa-star fa-2x text-white"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="col-md-6">
                                        <h5 class="mb-1"><?php echo htmlspecialchars($influencer['full_name']); ?></h5>
                                        <p class="text-muted mb-1">@<?php echo htmlspecialchars($influencer['username']); ?></p>
                                        <p class="mb-1">
                                            <strong>Instagram:</strong> @<?php echo htmlspecialchars($influencer['instagram_handle'] ?? ''); ?>
                                            <?php if ($influencer['instagram_verified'] ?? 0): ?>
                                                <i class="fas fa-check-circle text-primary ms-1"></i>
                                            <?php endif; ?>
                                        </p>
                                        <p class="mb-0">
                                            <strong>Category:</strong> <?php echo htmlspecialchars(ucfirst($influencer['category'] ?? 'Not set')); ?>
                                        </p>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="row text-center">
                                            <div class="col-6">
                                                <h5 class="text-primary"><?php echo number_format($influencer['instagram_followers'] ?? 0); ?></h5>
                                                <small class="text-muted">Followers</small>
                                            </div>
                                            <div class="col-6">
                                                <h5 class="text-success"><?php echo format_currency($influencer['rate_per_post'] ?? 0); ?></h5>
                                                <small class="text-muted">Rate per Post</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <!-- Available Campaigns -->
                    <div class="col-lg-8 mb-4">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-bullhorn me-2"></i>Available Campaigns
                                </h5>
                                <a href="campaigns.php" class="btn btn-sm btn-outline-primary">View All</a>
                            </div>
                            <div class="card-body">
                                <?php if (empty($available_campaigns)): ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                        <h6 class="text-muted">No campaigns available</h6>
                                        <p class="text-muted">Check back later for new collaboration opportunities!</p>
                                    </div>
                                <?php else: ?>
                                    <div class="row">
                                        <?php foreach ($available_campaigns as $campaign): ?>
                                            <div class="col-md-6 mb-3">
                                                <div class="card border">
                                                    <div class="card-body">
                                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                                            <h6 class="fw-bold mb-1"><?php echo htmlspecialchars($campaign['title']); ?></h6>
                                                            <span class="badge bg-success"><?php echo format_currency($campaign['reward_amount']); ?></span>
                                                        </div>
                                                        
                                                        <?php if ($campaign['brand_name']): ?>
                                                            <p class="text-muted small mb-2">
                                                                <i class="fas fa-building me-1"></i><?php echo htmlspecialchars($campaign['brand_name']); ?>
                                                            </p>
                                                        <?php endif; ?>
                                                        
                                                        <p class="small mb-2"><?php echo substr(htmlspecialchars($campaign['description']), 0, 100); ?>...</p>
                                                        
                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <small class="text-muted">
                                                                <i class="fas fa-calendar me-1"></i>Ends <?php echo date('M j', strtotime($campaign['end_date'])); ?>
                                                            </small>
                                                        </div>
                                                        
                                                        <div class="mt-2">
                                                            <a href="campaigns.php?campaign=<?php echo $campaign['id']; ?>" class="btn btn-sm btn-primary w-100">
                                                                Apply Now
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recent Activity & Wallet -->
                    <div class="col-lg-4 mb-4">
                        <!-- Wallet Summary -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-wallet me-2"></i>Wallet Summary
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <h6 class="text-success"><?php echo format_currency($stats['total_earned']); ?></h6>
                                        <small class="text-muted">Total Earned</small>
                                    </div>
                                    <div class="col-6">
                                        <h6 class="text-warning"><?php echo format_currency($stats['pending_payout']); ?></h6>
                                        <small class="text-muted">Pending</small>
                                    </div>
                                </div>
                                
                                <?php if ($stats['pending_payout'] >= MIN_PAYOUT_AMOUNT): ?>
                                    <div class="mt-3">
                                        <a href="wallet.php" class="btn btn-success btn-sm w-100">
                                            <i class="fas fa-money-bill-wave me-1"></i>Request Payout
                                        </a>
                                    </div>
                                <?php else: ?>
                                    <div class="mt-3">
                                        <small class="text-muted d-block text-center">
                                            Minimum payout: <?php echo format_currency(MIN_PAYOUT_AMOUNT); ?>
                                        </small>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <!-- Recent Activity -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-history me-2"></i>Recent Activity
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recent_campaigns)): ?>
                                    <div class="text-center py-3">
                                        <i class="fas fa-clock fa-2x text-muted mb-2"></i>
                                        <p class="text-muted small">No recent activity</p>
                                    </div>
                                <?php else: ?>
                                    <?php foreach (array_slice($recent_campaigns, 0, 5) as $campaign): ?>
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="flex-shrink-0">
                                                <?php
                                                $icon_class = '';
                                                $badge_class = '';
                                                switch ($campaign['status']) {
                                                    case 'pending':
                                                        $icon_class = 'fas fa-clock text-warning';
                                                        $badge_class = 'bg-warning';
                                                        break;
                                                    case 'active':
                                                        $icon_class = 'fas fa-play text-info';
                                                        $badge_class = 'bg-info';
                                                        break;
                                                    case 'completed':
                                                        $icon_class = 'fas fa-check text-success';
                                                        $badge_class = 'bg-success';
                                                        break;
                                                    case 'rejected':
                                                        $icon_class = 'fas fa-times text-danger';
                                                        $badge_class = 'bg-danger';
                                                        break;
                                                    default:
                                                        $icon_class = 'fas fa-circle text-secondary';
                                                        $badge_class = 'bg-secondary';
                                                }
                                                ?>
                                                <i class="<?php echo $icon_class; ?>"></i>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <h6 class="mb-1 small"><?php echo htmlspecialchars($campaign['title']); ?></h6>
                                                <p class="mb-0 small text-muted">
                                                    <span class="badge <?php echo $badge_class; ?> me-1"><?php echo ucfirst($campaign['status']); ?></span>
                                                    <?php echo time_ago($campaign['assigned_at']); ?>
                                                </p>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                    
                                    <div class="text-center mt-3">
                                        <a href="collaborations.php" class="btn btn-sm btn-outline-primary">View All</a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
