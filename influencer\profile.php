<?php
require_once '../config.php';
require_once '../includes/instagram_verification.php';
require_login(['influencer']);
require_instagram_verification('influencer');

$db = Database::getInstance();
$influencer_id = get_user_id();

// Get influencer data
$influencer = $db->fetch("SELECT * FROM influencers WHERE id = ?", [$influencer_id]);

// Get verification status
$verification_system = new InstagramVerification();
$verification_status = $verification_system->getVerificationStatus($influencer_id, 'influencer');

$success = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $full_name = trim($_POST['full_name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $instagram_handle = trim($_POST['instagram_handle'] ?? '');
    $instagram_followers = intval($_POST['instagram_followers'] ?? 0);
    $category = trim($_POST['category'] ?? '');
    $bio = trim($_POST['bio'] ?? '');
    $rate_per_post = floatval($_POST['rate_per_post'] ?? 0);
    $instagram_verified = isset($_POST['instagram_verified']) ? 1 : 0;
    
    // Validation
    if (empty($full_name) || empty($email) || empty($instagram_handle)) {
        $error = 'Please fill in all required fields.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Please enter a valid email address.';
    } else {
        try {
            // Check if email is already taken by another user
            $existing = $db->fetch("SELECT id FROM influencers WHERE email = ? AND id != ?", [$email, $influencer_id]);
            if ($existing) {
                $error = 'Email address is already taken.';
            } else {
                // Update influencer profile
                $db->update('influencers', [
                    'full_name' => $full_name,
                    'email' => $email,
                    'phone' => $phone,
                    'instagram_handle' => $instagram_handle,
                    'instagram_followers' => $instagram_followers,
                    'category' => $category,
                    'bio' => $bio,
                    'rate_per_post' => $rate_per_post,
                    'instagram_verified' => $instagram_verified,
                    'updated_at' => date('Y-m-d H:i:s')
                ], 'id = ?', [$influencer_id]);
                
                $success = 'Profile updated successfully!';
                
                // Refresh influencer data
                $influencer = $db->fetch("SELECT * FROM influencers WHERE id = ?", [$influencer_id]);
            }
        } catch (Exception $e) {
            $error = 'Failed to update profile. Please try again.';
        }
    }
}

$page_title = 'Profile';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-xl-2 sidebar p-0">
            <div class="p-3">
                <h5 class="text-white mb-3">
                    <i class="fas fa-star me-2"></i>Influencer Panel
                </h5>
                
                <!-- Verification Status in Sidebar -->
                <?php if ($verification_status['verified']): ?>
                    <div class="alert alert-success py-2 px-3 mb-3" style="font-size: 0.875rem;">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>Verified</strong>
                    </div>
                <?php elseif ($verification_status['verification_status'] === 'pending'): ?>
                    <div class="alert alert-warning py-2 px-3 mb-3" style="font-size: 0.875rem;">
                        <i class="fas fa-clock me-2"></i>
                        <strong>Verification Pending</strong>
                    </div>
                <?php else: ?>
                    <div class="alert alert-danger py-2 px-3 mb-3" style="font-size: 0.875rem;">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Verification Required</strong>
                    </div>
                <?php endif; ?>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a class="nav-link" href="campaigns.php">
                        <i class="fas fa-bullhorn"></i>Campaigns
                    </a>
                    <a class="nav-link" href="collaborations.php">
                        <i class="fas fa-handshake"></i>Collaborations
                    </a>
                    <a class="nav-link" href="assign_users.php">
                        <i class="fas fa-user-plus"></i>Assign to Users
                    </a>
                    <a class="nav-link" href="analytics.php">
                        <i class="fas fa-chart-line"></i>Analytics
                    </a>
                    <a class="nav-link" href="wallet.php">
                        <i class="fas fa-wallet"></i>Wallet
                    </a>
                    <a class="nav-link active" href="profile.php">
                        <i class="fas fa-user-edit"></i>Profile
                    </a>
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10 main-content" style="margin-left: auto;">
            <!-- Success Message for New Verification Submission -->
            <?php if (isset($_SESSION['verification_submitted']) && $_SESSION['verification_submitted']): ?>
                <div class="alert alert-success alert-dismissible fade show mb-4" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-check-circle fa-2x me-3 text-success"></i>
                        <div>
                            <h6 class="alert-heading mb-1">Verification Submitted Successfully!</h6>
                            <p class="mb-0">
                                Your Instagram verification request has been submitted. You now have access to your dashboard 
                                while our team verifies your follows. This usually takes up to 24 hours.
                            </p>
                        </div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php unset($_SESSION['verification_submitted']); ?>
            <?php endif; ?>

            <!-- Instagram Verification Status Banner -->
            <?php if (!$verification_status['verified']): ?>
                <div class="alert alert-warning alert-dismissible fade show mb-4" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fab fa-instagram fa-2x me-3"></i>
                        <div class="flex-grow-1">
                            <?php if ($verification_status['verification_status'] === 'pending'): ?>
                                <h6 class="alert-heading mb-1">
                                    <i class="fas fa-clock me-2"></i>Instagram Verification Pending
                                </h6>
                                <p class="mb-2">
                                    Your Instagram verification is being reviewed. You have limited access until verification is complete.
                                </p>
                                <small class="text-muted">
                                    <strong>Submitted:</strong> <?php echo date('M j, Y g:i A', strtotime($verification_status['submitted_at'])); ?>
                                    | <strong>Instagram:</strong> @<?php echo htmlspecialchars($verification_status['instagram_username']); ?>
                                </small>
                            <?php else: ?>
                                <h6 class="alert-heading mb-1">
                                    <i class="fas fa-exclamation-triangle me-2"></i>Instagram Verification Required
                                </h6>
                                <p class="mb-2">
                                    You need to follow our Instagram accounts to unlock all features.
                                </p>
                                <a href="../auth/instagram_verification.php" class="btn btn-warning btn-sm">
                                    <i class="fab fa-instagram me-1"></i>Complete Verification
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Top Bar -->
            <div class="bg-white shadow-sm p-3 mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">Profile Settings</h4>
                        <small class="text-muted">Manage your influencer profile and Instagram details</small>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-outline-primary d-lg-none" onclick="toggleSidebar()">
                            <i class="fas fa-bars"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="p-3">
                <!-- Success/Error Messages -->
                <?php if ($success): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Profile Form -->
                <div class="row">
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-user-edit me-2"></i>Personal Information
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="full_name" class="form-label">Full Name *</label>
                                            <input type="text" class="form-control" id="full_name" name="full_name" 
                                                   value="<?php echo htmlspecialchars($influencer['full_name']); ?>" required>
                                        </div>
                                        
                                        <div class="col-md-6 mb-3">
                                            <label for="email" class="form-label">Email Address *</label>
                                            <input type="email" class="form-control" id="email" name="email" 
                                                   value="<?php echo htmlspecialchars($influencer['email']); ?>" required>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-12 mb-3">
                                            <label for="phone" class="form-label">Phone Number</label>
                                            <input type="tel" class="form-control" id="phone" name="phone"
                                                   value="<?php echo htmlspecialchars($influencer['phone'] ?? ''); ?>">
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="bio" class="form-label">Bio</label>
                                        <textarea class="form-control" id="bio" name="bio" rows="3"
                                                  placeholder="Tell us about yourself and your content..."><?php echo htmlspecialchars($influencer['bio'] ?? ''); ?></textarea>
                                    </div>

                                    <hr>

                                    <h6 class="fw-bold mb-3">
                                        <i class="fab fa-instagram me-2"></i>Instagram Details
                                    </h6>

                                    <div class="alert alert-info small">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>Note:</strong> This is for your Instagram blue check verification status.
                                        Platform verification (following @thesyedabubakkar and @real_earners.in) is managed separately.
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="instagram_handle" class="form-label">Instagram Username *</label>
                                            <div class="input-group">
                                                <span class="input-group-text">@</span>
                                                <input type="text" class="form-control" id="instagram_handle" name="instagram_handle"
                                                       value="<?php echo htmlspecialchars($influencer['instagram_handle'] ?? ''); ?>" required>
                                            </div>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="instagram_followers" class="form-label">Followers Count</label>
                                            <input type="number" class="form-control" id="instagram_followers" name="instagram_followers"
                                                   value="<?php echo $influencer['instagram_followers'] ?? 0; ?>" min="0">
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="category" class="form-label">Content Category</label>
                                            <select class="form-select" id="category" name="category">
                                                <option value="">Select Category</option>
                                                <option value="lifestyle" <?php echo ($influencer['category'] ?? '') === 'lifestyle' ? 'selected' : ''; ?>>Lifestyle</option>
                                                <option value="fashion" <?php echo ($influencer['category'] ?? '') === 'fashion' ? 'selected' : ''; ?>>Fashion</option>
                                                <option value="beauty" <?php echo ($influencer['category'] ?? '') === 'beauty' ? 'selected' : ''; ?>>Beauty</option>
                                                <option value="fitness" <?php echo ($influencer['category'] ?? '') === 'fitness' ? 'selected' : ''; ?>>Fitness</option>
                                                <option value="food" <?php echo ($influencer['category'] ?? '') === 'food' ? 'selected' : ''; ?>>Food</option>
                                                <option value="travel" <?php echo ($influencer['category'] ?? '') === 'travel' ? 'selected' : ''; ?>>Travel</option>
                                                <option value="technology" <?php echo ($influencer['category'] ?? '') === 'technology' ? 'selected' : ''; ?>>Technology</option>
                                                <option value="business" <?php echo ($influencer['category'] ?? '') === 'business' ? 'selected' : ''; ?>>Business</option>
                                                <option value="entertainment" <?php echo ($influencer['category'] ?? '') === 'entertainment' ? 'selected' : ''; ?>>Entertainment</option>
                                                <option value="other" <?php echo ($influencer['category'] ?? '') === 'other' ? 'selected' : ''; ?>>Other</option>
                                            </select>
                                        </div>


                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="rate_per_post" class="form-label">Rate per Post (₹)</label>
                                            <input type="number" class="form-control" id="rate_per_post" name="rate_per_post"
                                                   value="<?php echo $influencer['rate_per_post'] ?? 0; ?>" min="0" step="0.01">
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <div class="form-check mt-4">
                                                <input class="form-check-input" type="checkbox" id="instagram_verified" name="instagram_verified"
                                                       <?php echo ($influencer['instagram_verified'] ?? 0) ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="instagram_verified">
                                                    <i class="fab fa-instagram me-1"></i>Instagram Verified Account (Blue Check)
                                                </label>
                                                <small class="form-text text-muted d-block">
                                                    Check this if your Instagram account has the official blue verification badge
                                                </small>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>Update Profile
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Profile Summary -->
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-bar me-2"></i>Profile Summary
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="text-center mb-3">
                                    <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center"
                                         style="width: 80px; height: 80px;">
                                        <i class="fas fa-user fa-2x text-white"></i>
                                    </div>
                                    <h5 class="mt-2 mb-1"><?php echo htmlspecialchars($influencer['full_name'] ?? ''); ?></h5>
                                    <small class="text-muted">@<?php echo htmlspecialchars($influencer['instagram_handle'] ?? ''); ?></small>
                                </div>

                                <hr>

                                <div class="row text-center">
                                    <div class="col-6">
                                        <h6 class="text-primary"><?php echo number_format($influencer['instagram_followers'] ?? 0); ?></h6>
                                        <small class="text-muted">Followers</small>
                                    </div>
                                    <div class="col-6">
                                        <h6 class="text-success"><?php echo format_currency($influencer['rate_per_post'] ?? 0); ?></h6>
                                        <small class="text-muted">Rate per Post</small>
                                    </div>
                                </div>

                                <hr>

                                <div class="mb-2">
                                    <small class="text-muted">Category:</small>
                                    <span class="float-end"><?php echo ucfirst($influencer['category'] ?? 'Not set'); ?></span>
                                </div>

                                <div class="mb-2">
                                    <small class="text-muted">Rate per Post:</small>
                                    <span class="float-end"><?php echo format_currency($influencer['rate_per_post'] ?? 0); ?></span>
                                </div>

                                <div class="mb-2">
                                    <small class="text-muted">Instagram Verified:</small>
                                    <span class="float-end">
                                        <?php if ($verification_status['verified']): ?>
                                            <i class="fas fa-check-circle text-success"></i> Yes (Followed Accounts)
                                        <?php else: ?>
                                            <i class="fas fa-times-circle text-danger"></i> No (Follow Required)
                                        <?php endif; ?>
                                    </span>
                                </div>

                                <div class="mb-2">
                                    <small class="text-muted">Blue Check Verified:</small>
                                    <span class="float-end">
                                        <?php if ($influencer['instagram_verified'] ?? 0): ?>
                                            <i class="fas fa-check-circle text-primary"></i> Yes
                                        <?php else: ?>
                                            <i class="fas fa-times-circle text-muted"></i> No
                                        <?php endif; ?>
                                    </span>
                                </div>

                                <div class="mb-2">
                                    <small class="text-muted">Member Since:</small>
                                    <span class="float-end"><?php echo date('M Y', strtotime($influencer['created_at'] ?? 'now')); ?></span>
                                </div>

                                <hr>

                                <div class="text-center">
                                    <a href="analytics.php" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-chart-line me-1"></i>View Analytics
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Tips -->
                        <div class="card mt-3">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-lightbulb me-2"></i>Profile Tips
                                </h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled small">
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        Keep your follower count updated
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        Add an engaging bio
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        Set competitive rates
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        Choose the right category
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
