<?php
require_once '../config.php';
require_once '../includes/instagram_verification.php';
require_login(['influencer']);
require_instagram_verification('influencer');

$db = Database::getInstance();
$influencer_id = get_user_id();

// Check if influencer is approved
$influencer = $db->fetch("SELECT * FROM influencers WHERE id = ?", [$influencer_id]);
if ($influencer['status'] !== 'approved') {
    redirect('dashboard.php');
}

// Handle payout request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['request_payout'])) {
    $amount = floatval($_POST['amount']);
    $upi_id = sanitize_input($_POST['upi_id']);
    
    if ($amount < MIN_PAYOUT_AMOUNT) {
        $_SESSION['error'] = 'Minimum payout amount is ' . format_currency(MIN_PAYOUT_AMOUNT);
    } elseif ($amount > $influencer['pending_payout']) {
        $_SESSION['error'] = 'Insufficient balance. Available: ' . format_currency($influencer['pending_payout']);
    } elseif (empty($upi_id)) {
        $_SESSION['error'] = 'UPI ID is required for payout.';
    } else {
        try {
            // Create payout request
            $payout_id = $db->insert('payouts', [
                'user_type' => 'influencer',
                'user_id' => $influencer_id,
                'amount' => $amount,
                'upi_id' => $upi_id,
                'status' => 'pending'
            ]);
            
            // Update influencer's pending payout
            $new_pending = $influencer['pending_payout'] - $amount;
            $db->update('influencers', ['pending_payout' => $new_pending], 'id = ?', [$influencer_id]);
            
            // Update UPI ID if provided
            if ($upi_id !== $influencer['upi_id']) {
                $db->update('influencers', ['upi_id' => $upi_id], 'id = ?', [$influencer_id]);
            }
            
            $_SESSION['success'] = 'Payout request submitted successfully! You will receive payment within 24-48 hours.';
            redirect('wallet.php');
        } catch (Exception $e) {
            $_SESSION['error'] = 'Failed to submit payout request. Please try again.';
        }
    }
}

// Get earnings statistics
$earnings_stats = $db->fetch("
    SELECT 
        COUNT(*) as total_campaigns,
        SUM(CASE WHEN status = 'completed' THEN reward_amount ELSE 0 END) as total_earned,
        SUM(CASE WHEN status = 'active' THEN reward_amount ELSE 0 END) as pending_earnings
    FROM influencer_campaigns 
    WHERE influencer_id = ?
", [$influencer_id]);

// Get recent payouts
$recent_payouts = $db->fetchAll("
    SELECT * FROM payouts 
    WHERE user_type = 'influencer' AND user_id = ? 
    ORDER BY created_at DESC 
    LIMIT 10
", [$influencer_id]);

// Get recent earnings
$recent_earnings = $db->fetchAll("
    SELECT ic.*, c.title, c.description, b.company_name as brand_name
    FROM influencer_campaigns ic
    JOIN campaigns c ON ic.campaign_id = c.id
    LEFT JOIN brands b ON c.brand_id = b.id
    WHERE ic.influencer_id = ? AND ic.status IN ('approved', 'paid')
    ORDER BY ic.approved_at DESC
    LIMIT 10
", [$influencer_id]);

$page_title = 'Wallet';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-xl-2 sidebar p-0">
            <div class="p-3">
                <h5 class="text-white mb-4">
                    <i class="fas fa-star me-2"></i>Influencer Panel
                </h5>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a class="nav-link" href="campaigns.php">
                        <i class="fas fa-bullhorn"></i>Campaigns
                    </a>
                    <a class="nav-link" href="collaborations.php">
                        <i class="fas fa-handshake"></i>Collaborations
                    </a>
                    <a class="nav-link" href="analytics.php">
                        <i class="fas fa-chart-line"></i>Analytics
                    </a>
                    <a class="nav-link active" href="wallet.php">
                        <i class="fas fa-wallet"></i>Wallet
                    </a>
                    <a class="nav-link" href="profile.php">
                        <i class="fas fa-user-edit"></i>Profile
                    </a>
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10 main-content" style="margin-left: auto;">
            <!-- Top Bar -->
            <div class="bg-white shadow-sm p-3 mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">My Wallet</h4>
                        <small class="text-muted">Manage your earnings and request payouts</small>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-outline-primary d-lg-none" onclick="toggleSidebar()">
                            <i class="fas fa-bars"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="p-3">
                <!-- Wallet Overview -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card success">
                            <div class="card-body text-center">
                                <i class="fas fa-wallet fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo format_currency($influencer['pending_payout']); ?></h3>
                                <p class="mb-0">Available Balance</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card info">
                            <div class="card-body text-center">
                                <i class="fas fa-chart-line fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo format_currency($influencer['total_earned']); ?></h3>
                                <p class="mb-0">Total Earned</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card warning">
                            <div class="card-body text-center">
                                <i class="fas fa-handshake fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $earnings_stats['total_campaigns']; ?></h3>
                                <p class="mb-0">Total Campaigns</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <i class="fas fa-clock fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo format_currency($earnings_stats['pending_earnings'] ?? 0); ?></h3>
                                <p class="mb-0">Pending Earnings</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <!-- Payout Request -->
                    <div class="col-lg-4 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-money-bill-wave me-2"></i>Request Payout
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if ($influencer['pending_payout'] >= MIN_PAYOUT_AMOUNT): ?>
                                    <form method="POST" class="needs-validation" novalidate>
                                        <div class="mb-3">
                                            <label for="amount" class="form-label">Amount</label>
                                            <div class="input-group">
                                                <span class="input-group-text">₹</span>
                                                <input type="number" class="form-control" id="amount" name="amount" 
                                                       min="<?php echo MIN_PAYOUT_AMOUNT; ?>" 
                                                       max="<?php echo $influencer['pending_payout']; ?>" 
                                                       value="<?php echo $influencer['pending_payout']; ?>" 
                                                       step="0.01" required>
                                            </div>
                                            <div class="form-text">
                                                Available: <?php echo format_currency($influencer['pending_payout']); ?>
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="upi_id" class="form-label">UPI ID</label>
                                            <input type="text" class="form-control" id="upi_id" name="upi_id" 
                                                   value="<?php echo htmlspecialchars($influencer['upi_id'] ?? ''); ?>" 
                                                   placeholder="yourname@paytm" required>
                                            <div class="form-text">
                                                Payment will be sent to this UPI ID
                                            </div>
                                        </div>
                                        
                                        <div class="alert alert-info small">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Payouts are processed within 24-48 hours. Minimum amount: <?php echo format_currency(MIN_PAYOUT_AMOUNT); ?>
                                        </div>
                                        
                                        <div class="d-grid">
                                            <button type="submit" name="request_payout" class="btn btn-success">
                                                <i class="fas fa-paper-plane me-2"></i>Request Payout
                                            </button>
                                        </div>
                                    </form>
                                <?php else: ?>
                                    <div class="text-center py-3">
                                        <i class="fas fa-exclamation-triangle fa-2x text-warning mb-3"></i>
                                        <h6 class="text-muted">Insufficient Balance</h6>
                                        <p class="text-muted small">
                                            You need at least <?php echo format_currency(MIN_PAYOUT_AMOUNT); ?> to request a payout.
                                            <br>Current balance: <?php echo format_currency($influencer['pending_payout']); ?>
                                        </p>
                                        <a href="campaigns.php" class="btn btn-primary btn-sm">
                                            <i class="fas fa-search me-1"></i>Find Campaigns
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recent Payouts -->
                    <div class="col-lg-4 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-history me-2"></i>Recent Payouts
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recent_payouts)): ?>
                                    <div class="text-center py-3">
                                        <i class="fas fa-receipt fa-2x text-muted mb-2"></i>
                                        <p class="text-muted small">No payouts yet</p>
                                    </div>
                                <?php else: ?>
                                    <?php foreach ($recent_payouts as $payout): ?>
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <div>
                                                <h6 class="mb-1"><?php echo format_currency($payout['amount']); ?></h6>
                                                <small class="text-muted">
                                                    <?php echo date('M j, Y', strtotime($payout['created_at'])); ?>
                                                </small>
                                            </div>
                                            <div>
                                                <?php
                                                $status_class = '';
                                                switch ($payout['status']) {
                                                    case 'pending':
                                                        $status_class = 'bg-warning';
                                                        break;
                                                    case 'processing':
                                                        $status_class = 'bg-info';
                                                        break;
                                                    case 'completed':
                                                        $status_class = 'bg-success';
                                                        break;
                                                    case 'failed':
                                                        $status_class = 'bg-danger';
                                                        break;
                                                    case 'cancelled':
                                                        $status_class = 'bg-secondary';
                                                        break;
                                                }
                                                ?>
                                                <span class="badge <?php echo $status_class; ?> small">
                                                    <?php echo ucfirst($payout['status']); ?>
                                                </span>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recent Earnings -->
                    <div class="col-lg-4 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-coins me-2"></i>Recent Earnings
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recent_earnings)): ?>
                                    <div class="text-center py-3">
                                        <i class="fas fa-coins fa-2x text-muted mb-2"></i>
                                        <p class="text-muted small">No earnings yet</p>
                                        <a href="campaigns.php" class="btn btn-primary btn-sm">
                                            Start Earning
                                        </a>
                                    </div>
                                <?php else: ?>
                                    <?php foreach (array_slice($recent_earnings, 0, 5) as $earning): ?>
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <div>
                                                <h6 class="mb-1 small"><?php echo htmlspecialchars($earning['title']); ?></h6>
                                                <small class="text-muted">
                                                    <?php echo htmlspecialchars($earning['brand_name'] ?? 'RealEarners'); ?>
                                                </small>
                                            </div>
                                            <div class="text-end">
                                                <h6 class="mb-0 text-success"><?php echo format_currency($earning['reward_amount']); ?></h6>
                                                <small class="text-muted">
                                                    <?php echo time_ago($earning['completed_at']); ?>
                                                </small>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Performance Insights -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>Performance Insights
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php
                        $monthly_stats = $db->fetch("
                            SELECT 
                                COUNT(*) as campaigns_completed,
                                SUM(reward_amount) as month_earnings
                            FROM influencer_campaigns 
                            WHERE influencer_id = ? 
                            AND status = 'completed'
                            AND MONTH(completed_at) = MONTH(CURRENT_DATE())
                            AND YEAR(completed_at) = YEAR(CURRENT_DATE())
                        ", [$influencer_id]);
                        
                        $avg_campaign_value = $earnings_stats['total_campaigns'] > 0 
                            ? $influencer['total_earned'] / $earnings_stats['total_campaigns'] 
                            : 0;
                        ?>
                        
                        <div class="row text-center">
                            <div class="col-md-3">
                                <h5 class="text-primary"><?php echo $monthly_stats['campaigns_completed'] ?? 0; ?></h5>
                                <small class="text-muted">Campaigns This Month</small>
                            </div>
                            <div class="col-md-3">
                                <h5 class="text-success"><?php echo format_currency($monthly_stats['month_earnings'] ?? 0); ?></h5>
                                <small class="text-muted">Monthly Earnings</small>
                            </div>
                            <div class="col-md-3">
                                <h5 class="text-info"><?php echo format_currency($avg_campaign_value); ?></h5>
                                <small class="text-muted">Avg Campaign Value</small>
                            </div>
                            <div class="col-md-3">
                                <h5 class="text-warning"><?php echo number_format($influencer['instagram_followers']); ?></h5>
                                <small class="text-muted">Instagram Followers</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
