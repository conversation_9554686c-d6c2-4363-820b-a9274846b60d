<?php
require_once 'config.php';

$db = Database::getInstance();

echo "Creating badges system...\n";
echo "========================\n\n";

try {
    // Create badges table
    $db->query("
        CREATE TABLE IF NOT EXISTS badges (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            icon VARCHAR(50) NOT NULL,
            color VARCHAR(20) NOT NULL DEFAULT 'primary',
            badge_type ENUM('achievement', 'status', 'special', 'verification', 'tier') NOT NULL DEFAULT 'achievement',
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    echo "✓ Created badges table\n";

    // Create user_badges table
    $db->query("
        CREATE TABLE IF NOT EXISTS user_badges (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            user_type ENUM('user', 'influencer', 'brand') NOT NULL,
            badge_id INT NOT NULL,
            assigned_by INT NOT NULL,
            assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            notes TEXT,
            is_active TINYINT(1) DEFAULT 1,
            UNIQUE KEY unique_user_badge (user_id, user_type, badge_id)
        )
    ");
    echo "✓ Created user_badges table\n";

    // Check if badges already exist
    $existing_badges = $db->fetch("SELECT COUNT(*) as count FROM badges");
    if ($existing_badges['count'] == 0) {
        // Insert default badges
        $badges = [
            ['Top Performer', 'Awarded to users with exceptional performance', 'fas fa-trophy', 'warning', 'achievement'],
            ['Verified Creator', 'Verified content creator status', 'fas fa-check-circle', 'success', 'verification'],
            ['Premium Member', 'Premium membership status', 'fas fa-crown', 'warning', 'tier'],
            ['Early Adopter', 'One of the first users on the platform', 'fas fa-rocket', 'info', 'special'],
            ['Content Master', 'Expert in creating quality content', 'fas fa-star', 'primary', 'achievement'],
            ['Influencer Elite', 'Top tier influencer status', 'fas fa-gem', 'danger', 'tier'],
            ['Brand Partner', 'Official brand partnership', 'fas fa-handshake', 'success', 'status'],
            ['Community Leader', 'Active community contributor', 'fas fa-users', 'info', 'achievement'],
            ['High Earner', 'Consistently high earnings', 'fas fa-dollar-sign', 'success', 'achievement'],
            ['Trending Creator', 'Currently trending content creator', 'fas fa-fire', 'danger', 'status'],
            ['Loyal Member', 'Long-term platform member', 'fas fa-heart', 'danger', 'special'],
            ['Quality Contributor', 'Consistently high-quality submissions', 'fas fa-medal', 'warning', 'achievement'],
            ['Social Media Expert', 'Expert in social media marketing', 'fas fa-share-alt', 'primary', 'achievement'],
            ['Brand Ambassador', 'Official brand ambassador', 'fas fa-flag', 'info', 'status'],
            ['VIP Member', 'VIP membership status', 'fas fa-vip', 'warning', 'tier']
        ];

        foreach ($badges as $badge) {
            $db->insert('badges', [
                'name' => $badge[0],
                'description' => $badge[1],
                'icon' => $badge[2],
                'color' => $badge[3],
                'badge_type' => $badge[4]
            ]);
        }
        echo "✓ Inserted " . count($badges) . " default badges\n";
    } else {
        echo "✓ Badges already exist (" . $existing_badges['count'] . " badges)\n";
    }

    // Add indexes (skip if they cause errors)
    try {
        $db->query("CREATE INDEX IF NOT EXISTS idx_user_badges_user ON user_badges(user_id, user_type)");
        $db->query("CREATE INDEX IF NOT EXISTS idx_user_badges_badge ON user_badges(badge_id)");
        $db->query("CREATE INDEX IF NOT EXISTS idx_user_badges_active ON user_badges(is_active)");
        $db->query("CREATE INDEX IF NOT EXISTS idx_badges_type ON badges(badge_type)");
        $db->query("CREATE INDEX IF NOT EXISTS idx_badges_active ON badges(is_active)");
        echo "✓ Created indexes\n";
    } catch (Exception $e) {
        echo "⚠ Index creation skipped (may already exist)\n";
    }

    echo "\n✅ Badges system created successfully!\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
