<?php
require_once 'config.php';

$db = Database::getInstance();

echo "Setting up advanced settings configuration...\n";
echo "=============================================\n\n";

try {
    // First, add new columns to settings table if they don't exist
    echo "Adding new columns to settings table...\n";
    
    $columns_to_add = [
        "ADD COLUMN IF NOT EXISTS category VARCHAR(50) DEFAULT 'general'",
        "ADD COLUMN IF NOT EXISTS setting_type ENUM('text', 'textarea', 'number', 'boolean', 'select', 'password', 'email', 'url') DEFAULT 'text'",
        "ADD COLUMN IF NOT EXISTS options TEXT NULL",
        "ADD COLUMN IF NOT EXISTS is_sensitive TINYINT(1) DEFAULT 0",
        "ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
    ];
    
    foreach ($columns_to_add as $column_sql) {
        try {
            $db->query("ALTER TABLE settings $column_sql");
            echo "✓ Added column\n";
        } catch (Exception $e) {
            echo "⚠️  Column may already exist: " . $e->getMessage() . "\n";
        }
    }
    
    // Clear existing settings
    echo "\nClearing existing settings...\n";
    $db->query("DELETE FROM settings");
    
    // Insert comprehensive settings
    echo "Inserting advanced settings...\n";
    
    $advanced_settings = [
        // Basic Site Settings
        ['site_name', 'RealEarners', 'Website Name', 'general', 'text'],
        ['site_description', 'Earn Real Money with Real Opportunities', 'Website Description', 'general', 'textarea'],
        ['site_url', 'https://realearners.in', 'Website URL', 'general', 'url'],
        ['site_logo', '/assets/images/logo.png', 'Website Logo Path', 'general', 'text'],
        ['site_favicon', '/assets/images/favicon.ico', 'Website Favicon Path', 'general', 'text'],
        ['site_email', '<EMAIL>', 'Support Email', 'general', 'email'],
        ['admin_email', '<EMAIL>', 'Admin Email', 'general', 'email'],
        ['contact_phone', '+91-9876543210', 'Contact Phone Number', 'general', 'text'],
        ['contact_address', 'Mumbai, Maharashtra, India', 'Contact Address', 'general', 'textarea'],
        
        // Payment Settings
        ['razorpay_key', '', 'Razorpay API Key', 'payment', 'password', 1],
        ['razorpay_secret', '', 'Razorpay Secret Key', 'payment', 'password', 1],
        ['razorpay_webhook_secret', '', 'Razorpay Webhook Secret', 'payment', 'password', 1],
        ['min_payout_amount', '100', 'Minimum Payout Amount (INR)', 'payment', 'number'],
        ['max_payout_amount', '50000', 'Maximum Payout Amount (INR)', 'payment', 'number'],
        ['platform_commission', '10', 'Platform Commission Percentage', 'payment', 'number'],
        ['payout_processing_fee', '5', 'Payout Processing Fee (INR)', 'payment', 'number'],
        ['auto_payout_enabled', '0', 'Enable Automatic Payouts', 'payment', 'boolean'],
        ['payout_schedule', 'weekly', 'Payout Schedule', 'payment', 'select', 0, 'daily,weekly,monthly'],
        
        // Email Settings
        ['email_smtp_host', 'smtp.gmail.com', 'SMTP Host for Email', 'email', 'text'],
        ['email_smtp_port', '587', 'SMTP Port', 'email', 'number'],
        ['email_smtp_username', '', 'SMTP Username', 'email', 'text'],
        ['email_smtp_password', '', 'SMTP Password', 'email', 'password', 1],
        ['email_smtp_encryption', 'tls', 'SMTP Encryption', 'email', 'select', 0, 'tls,ssl'],
        ['email_from_name', 'RealEarners Team', 'Email From Name', 'email', 'text'],
        ['email_from_address', '<EMAIL>', 'Email From Address', 'email', 'email'],
        ['email_notifications_enabled', '1', 'Enable Email Notifications', 'email', 'boolean'],
        
        // SMS/WhatsApp Settings
        ['whatsapp_api_key', '', 'WhatsApp API Key for Notifications', 'notifications', 'password', 1],
        ['whatsapp_api_url', '', 'WhatsApp API URL', 'notifications', 'url'],
        ['sms_api_key', '', 'SMS API Key', 'notifications', 'password', 1],
        ['sms_api_url', '', 'SMS API URL', 'notifications', 'url'],
        ['sms_sender_id', 'REALERN', 'SMS Sender ID', 'notifications', 'text'],
        ['whatsapp_notifications_enabled', '0', 'Enable WhatsApp Notifications', 'notifications', 'boolean'],
        ['sms_notifications_enabled', '0', 'Enable SMS Notifications', 'notifications', 'boolean'],
        
        // Instagram Settings
        ['instagram_account_1', 'thesyedabubakkar', 'Primary Instagram Account', 'instagram', 'text'],
        ['instagram_account_2', 'real_earners.in', 'Secondary Instagram Account', 'instagram', 'text'],
        ['instagram_verification_required', '1', 'Require Instagram Verification', 'instagram', 'boolean'],
        ['instagram_grace_period_days', '2', 'Grace Period for Verification (Days)', 'instagram', 'number'],
        ['instagram_auto_suspend', '1', 'Auto Suspend Non-Followers', 'instagram', 'boolean'],
        ['instagram_api_key', '', 'Instagram API Key', 'instagram', 'password', 1],
        ['instagram_api_secret', '', 'Instagram API Secret', 'instagram', 'password', 1],
        
        // Security Settings
        ['session_timeout', '3600', 'Session Timeout (Seconds)', 'security', 'number'],
        ['max_login_attempts', '5', 'Maximum Login Attempts', 'security', 'number'],
        ['login_lockout_duration', '900', 'Login Lockout Duration (Seconds)', 'security', 'number'],
        ['password_min_length', '8', 'Minimum Password Length', 'security', 'number'],
        ['require_email_verification', '1', 'Require Email Verification', 'security', 'boolean'],
        ['two_factor_auth_enabled', '0', 'Enable Two-Factor Authentication', 'security', 'boolean'],
        ['ip_whitelist_enabled', '0', 'Enable IP Whitelist for Admin', 'security', 'boolean'],
        ['admin_ip_whitelist', '', 'Admin IP Whitelist (comma separated)', 'security', 'textarea'],
        
        // Campaign Settings
        ['max_campaigns_per_brand', '10', 'Maximum Campaigns per Brand', 'campaigns', 'number'],
        ['campaign_approval_required', '1', 'Require Campaign Approval', 'campaigns', 'boolean'],
        ['auto_assign_campaigns', '0', 'Auto Assign Campaigns to Users', 'campaigns', 'boolean'],
        ['campaign_deadline_buffer_hours', '24', 'Campaign Deadline Buffer (Hours)', 'campaigns', 'number'],
        ['max_participants_per_campaign', '1000', 'Maximum Participants per Campaign', 'campaigns', 'number'],
        
        // User Settings
        ['user_registration_enabled', '1', 'Enable User Registration', 'users', 'boolean'],
        ['influencer_registration_enabled', '1', 'Enable Influencer Registration', 'users', 'boolean'],
        ['brand_registration_enabled', '1', 'Enable Brand Registration', 'users', 'boolean'],
        ['user_approval_required', '0', 'Require User Approval', 'users', 'boolean'],
        ['influencer_approval_required', '1', 'Require Influencer Approval', 'users', 'boolean'],
        ['brand_approval_required', '1', 'Require Brand Approval', 'users', 'boolean'],
        ['max_tasks_per_user_daily', '5', 'Maximum Tasks per User Daily', 'users', 'number'],
        ['user_referral_bonus', '50', 'User Referral Bonus (INR)', 'users', 'number'],
        
        // Badge Settings
        ['badges_enabled', '1', 'Enable Badge System', 'badges', 'boolean'],
        ['auto_badge_assignment', '1', 'Enable Automatic Badge Assignment', 'badges', 'boolean'],
        ['badge_notifications_enabled', '1', 'Enable Badge Notifications', 'badges', 'boolean'],
        ['achievement_tracking_enabled', '1', 'Enable Achievement Tracking', 'badges', 'boolean'],
        
        // Analytics Settings
        ['google_analytics_id', '', 'Google Analytics ID', 'analytics', 'text'],
        ['facebook_pixel_id', '', 'Facebook Pixel ID', 'analytics', 'text'],
        ['analytics_enabled', '1', 'Enable Analytics Tracking', 'analytics', 'boolean'],
        ['user_activity_tracking', '1', 'Enable User Activity Tracking', 'analytics', 'boolean'],
        
        // Maintenance Settings
        ['maintenance_mode', '0', 'Enable Maintenance Mode', 'maintenance', 'boolean'],
        ['maintenance_message', 'We are currently performing scheduled maintenance. Please check back soon.', 'Maintenance Message', 'maintenance', 'textarea'],
        ['backup_enabled', '1', 'Enable Automatic Backups', 'maintenance', 'boolean'],
        ['backup_frequency', 'daily', 'Backup Frequency', 'maintenance', 'select', 0, 'daily,weekly'],
        ['backup_retention_days', '30', 'Backup Retention Days', 'maintenance', 'number'],
        
        // API Settings
        ['api_enabled', '1', 'Enable API Access', 'api', 'boolean'],
        ['api_rate_limit', '1000', 'API Rate Limit (requests per hour)', 'api', 'number'],
        ['api_key_required', '1', 'Require API Key for Access', 'api', 'boolean'],
        ['webhook_enabled', '1', 'Enable Webhook Support', 'api', 'boolean'],
        
        // File Upload Settings
        ['max_file_size_mb', '10', 'Maximum File Size (MB)', 'uploads', 'number'],
        ['allowed_file_types', 'jpg,jpeg,png,gif,pdf,doc,docx', 'Allowed File Types', 'uploads', 'text'],
        ['upload_path', '/uploads/', 'Upload Directory Path', 'uploads', 'text'],
        ['image_compression_quality', '80', 'Image Compression Quality (1-100)', 'uploads', 'number'],
        
        // Performance Settings
        ['cache_enabled', '1', 'Enable Caching', 'performance', 'boolean'],
        ['cache_duration', '3600', 'Cache Duration (Seconds)', 'performance', 'number'],
        ['image_optimization_enabled', '1', 'Enable Image Optimization', 'performance', 'boolean'],
        ['cdn_enabled', '0', 'Enable CDN', 'performance', 'boolean'],
        ['cdn_url', '', 'CDN URL', 'performance', 'url']
    ];
    
    foreach ($advanced_settings as $setting) {
        $setting_key = $setting[0];
        $setting_value = $setting[1];
        $description = $setting[2];
        $category = $setting[3];
        $setting_type = $setting[4];
        $is_sensitive = isset($setting[5]) ? $setting[5] : 0;
        $options = isset($setting[6]) ? $setting[6] : null;
        
        $db->insert('settings', [
            'setting_key' => $setting_key,
            'setting_value' => $setting_value,
            'description' => $description,
            'category' => $category,
            'setting_type' => $setting_type,
            'is_sensitive' => $is_sensitive,
            'options' => $options
        ]);
        
        echo "✓ Added setting: $setting_key\n";
    }
    
    echo "\n✅ Advanced settings configuration completed successfully!\n";
    echo "Total settings configured: " . count($advanced_settings) . "\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
