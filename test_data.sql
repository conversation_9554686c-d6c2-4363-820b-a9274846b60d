-- Test data for RealEarners platform
-- Run this after importing the main database.sql

-- Insert some test influencers with pending status
INSERT INTO influencers (username, email, password, full_name, phone, instagram_handle, instagram_followers, category, status, created_at) VALUES
('test_influencer1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<PERSON>', '9876543210', 'sarah_lifestyle', 15000, 'lifestyle', 'pending', NOW()),
('test_influencer2', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Mike Fitness', '9876543211', 'mike_fitness', 25000, 'fitness', 'pending', NOW()),
('test_influencer3', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Emma Fashion', '9876543212', 'emma_fashion', 35000, 'fashion', 'pending', NOW()),
('test_influencer4', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Alex Tech', '9876543213', 'alex_tech', 45000, 'technology', 'pending', NOW()),
('approved_influencer', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Lisa Approved', '9876543214', 'lisa_approved', 55000, 'beauty', 'approved', NOW());

-- Insert some test users
INSERT INTO users (username, email, password, full_name, phone, instagram_handle, status, created_at) VALUES
('test_user1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'John Doe', '9876543215', 'john_doe', 'active', NOW()),
('test_user2', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Jane Smith', '9876543216', 'jane_smith', 'active', NOW()),
('test_user3', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Bob Wilson', '**********', 'bob_wilson', 'active', NOW());

-- Insert some test brands
INSERT INTO brands (name, description, website, contact_email, status, created_at) VALUES
('TechCorp', 'Leading technology company', 'https://techcorp.com', '<EMAIL>', 'active', NOW()),
('FashionHub', 'Trendy fashion brand', 'https://fashionhub.com', '<EMAIL>', 'active', NOW()),
('FitLife', 'Health and fitness products', 'https://fitlife.com', '<EMAIL>', 'active', NOW());

-- Insert some test campaigns
INSERT INTO campaigns (brand_id, title, description, campaign_type, target_audience, post_format, reward_amount, total_budget, max_participants, start_date, end_date, requirements, hashtags, status, created_by, created_at) VALUES
(1, 'TechCorp Product Launch', 'Promote our new smartphone with engaging content', 'both', 'Tech enthusiasts, Young professionals', 'post', 150.00, 15000.00, 100, CURDATE(), DATE_ADD(CURDATE(), INTERVAL 30 DAY), 'Create authentic content showcasing product features', '#TechCorp #NewPhone #Innovation', 'active', 1, NOW()),
(2, 'Summer Fashion Collection', 'Showcase our latest summer collection', 'influencer_campaign', 'Fashion lovers, Women 18-35', 'post', 500.00, 25000.00, 50, CURDATE(), DATE_ADD(CURDATE(), INTERVAL 45 DAY), 'Style the outfits creatively and tag our brand', '#FashionHub #SummerStyle #OOTD', 'active', 1, NOW()),
(3, 'Fitness Challenge', 'Promote our fitness equipment and supplements', 'user_task', 'Fitness enthusiasts, Health conscious people', 'story', 75.00, 7500.00, 100, CURDATE(), DATE_ADD(CURDATE(), INTERVAL 20 DAY), 'Show yourself using our products during workout', '#FitLife #FitnessChallenge #HealthyLiving', 'active', 1, NOW());

-- Insert some platform settings
INSERT INTO settings (setting_key, setting_value, description) VALUES
('site_name', 'RealEarners', 'Platform name'),
('site_email', '<EMAIL>', 'Platform email'),
('min_payout_amount', '100', 'Minimum payout amount in rupees'),
('platform_commission', '10', 'Platform commission percentage'),
('razorpay_key_id', '', 'Razorpay API Key ID'),
('razorpay_key_secret', '', 'Razorpay API Key Secret'),
('email_smtp_host', '', 'SMTP host for email'),
('email_smtp_port', '587', 'SMTP port'),
('email_smtp_username', '', 'SMTP username'),
('email_smtp_password', '', 'SMTP password'),
('whatsapp_api_key', '', 'WhatsApp API key for notifications'),
('whatsapp_api_url', '', 'WhatsApp API URL');

-- Note: All test accounts use password: "password"
-- Admin account is already created in main database.sql
