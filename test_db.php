<?php
try {
    $pdo = new PDO('mysql:host=localhost;dbname=realearners', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Test if new tables exist
    $result = $pdo->query('SHOW TABLES LIKE "brand_campaigns"');
    if ($result->rowCount() > 0) {
        echo 'Database tables already exist. System ready!' . PHP_EOL;
    } else {
        echo 'Need to run database.sql to create new tables.' . PHP_EOL;
    }
    
    // Test brand login
    $brand = $pdo->query('SELECT * FROM brands LIMIT 1')->fetch();
    if ($brand) {
        echo 'Sample brand found: ' . $brand['company_name'] . PHP_EOL;
    } else {
        echo 'No brands found in database.' . PHP_EOL;
    }
    
} catch (Exception $e) {
    echo 'Database connection error: ' . $e->getMessage() . PHP_EOL;
}
?>
