-- Advanced Settings Configuration
-- Add more comprehensive settings for platform management

-- Clear existing settings and add comprehensive configuration
DELETE FROM settings;

INSERT INTO settings (setting_key, setting_value, description, category) VALUES 
-- Basic Site Settings
('site_name', 'RealEarners', 'Website Name', 'general'),
('site_description', 'Earn Real Money with Real Opportunities', 'Website Description', 'general'),
('site_url', 'https://realearners.in', 'Website URL', 'general'),
('site_logo', '/assets/images/logo.png', 'Website Logo Path', 'general'),
('site_favicon', '/assets/images/favicon.ico', 'Website Favicon Path', 'general'),
('site_email', '<EMAIL>', 'Support Email', 'general'),
('admin_email', '<EMAIL>', 'Admin Email', 'general'),
('contact_phone', '+91-9876543210', 'Contact Phone Number', 'general'),
('contact_address', 'Mumbai, Maharashtra, India', 'Contact Address', 'general'),

-- Payment Settings
('razorpay_key', '', 'Razorpay API Key', 'payment'),
('razorpay_secret', '', 'Razorpay Secret Key', 'payment'),
('razorpay_webhook_secret', '', 'Razorpay Webhook Secret', 'payment'),
('min_payout_amount', '100', 'Minimum Payout Amount (INR)', 'payment'),
('max_payout_amount', '50000', 'Maximum Payout Amount (INR)', 'payment'),
('platform_commission', '10', 'Platform Commission Percentage', 'payment'),
('payout_processing_fee', '5', 'Payout Processing Fee (INR)', 'payment'),
('auto_payout_enabled', '0', 'Enable Automatic Payouts', 'payment'),
('payout_schedule', 'weekly', 'Payout Schedule (daily/weekly/monthly)', 'payment'),

-- Email Settings
('email_smtp_host', 'smtp.gmail.com', 'SMTP Host for Email', 'email'),
('email_smtp_port', '587', 'SMTP Port', 'email'),
('email_smtp_username', '', 'SMTP Username', 'email'),
('email_smtp_password', '', 'SMTP Password', 'email'),
('email_smtp_encryption', 'tls', 'SMTP Encryption (tls/ssl)', 'email'),
('email_from_name', 'RealEarners Team', 'Email From Name', 'email'),
('email_from_address', '<EMAIL>', 'Email From Address', 'email'),
('email_notifications_enabled', '1', 'Enable Email Notifications', 'email'),

-- SMS/WhatsApp Settings
('whatsapp_api_key', '', 'WhatsApp API Key for Notifications', 'notifications'),
('whatsapp_api_url', '', 'WhatsApp API URL', 'notifications'),
('sms_api_key', '', 'SMS API Key', 'notifications'),
('sms_api_url', '', 'SMS API URL', 'notifications'),
('sms_sender_id', 'REALERN', 'SMS Sender ID', 'notifications'),
('whatsapp_notifications_enabled', '0', 'Enable WhatsApp Notifications', 'notifications'),
('sms_notifications_enabled', '0', 'Enable SMS Notifications', 'notifications'),

-- Instagram Settings
('instagram_account_1', 'thesyedabubakkar', 'Primary Instagram Account', 'instagram'),
('instagram_account_2', 'real_earners.in', 'Secondary Instagram Account', 'instagram'),
('instagram_verification_required', '1', 'Require Instagram Verification', 'instagram'),
('instagram_grace_period_days', '2', 'Grace Period for Verification (Days)', 'instagram'),
('instagram_auto_suspend', '1', 'Auto Suspend Non-Followers', 'instagram'),
('instagram_api_key', '', 'Instagram API Key', 'instagram'),
('instagram_api_secret', '', 'Instagram API Secret', 'instagram'),

-- Security Settings
('session_timeout', '3600', 'Session Timeout (Seconds)', 'security'),
('max_login_attempts', '5', 'Maximum Login Attempts', 'security'),
('login_lockout_duration', '900', 'Login Lockout Duration (Seconds)', 'security'),
('password_min_length', '8', 'Minimum Password Length', 'security'),
('require_email_verification', '1', 'Require Email Verification', 'security'),
('two_factor_auth_enabled', '0', 'Enable Two-Factor Authentication', 'security'),
('ip_whitelist_enabled', '0', 'Enable IP Whitelist for Admin', 'security'),
('admin_ip_whitelist', '', 'Admin IP Whitelist (comma separated)', 'security'),

-- Campaign Settings
('max_campaigns_per_brand', '10', 'Maximum Campaigns per Brand', 'campaigns'),
('campaign_approval_required', '1', 'Require Campaign Approval', 'campaigns'),
('auto_assign_campaigns', '0', 'Auto Assign Campaigns to Users', 'campaigns'),
('campaign_deadline_buffer_hours', '24', 'Campaign Deadline Buffer (Hours)', 'campaigns'),
('max_participants_per_campaign', '1000', 'Maximum Participants per Campaign', 'campaigns'),

-- User Settings
('user_registration_enabled', '1', 'Enable User Registration', 'users'),
('influencer_registration_enabled', '1', 'Enable Influencer Registration', 'users'),
('brand_registration_enabled', '1', 'Enable Brand Registration', 'users'),
('user_approval_required', '0', 'Require User Approval', 'users'),
('influencer_approval_required', '1', 'Require Influencer Approval', 'users'),
('brand_approval_required', '1', 'Require Brand Approval', 'users'),
('max_tasks_per_user_daily', '5', 'Maximum Tasks per User Daily', 'users'),
('user_referral_bonus', '50', 'User Referral Bonus (INR)', 'users'),

-- Badge Settings
('badges_enabled', '1', 'Enable Badge System', 'badges'),
('auto_badge_assignment', '1', 'Enable Automatic Badge Assignment', 'badges'),
('badge_notifications_enabled', '1', 'Enable Badge Notifications', 'badges'),
('achievement_tracking_enabled', '1', 'Enable Achievement Tracking', 'badges'),

-- Analytics Settings
('google_analytics_id', '', 'Google Analytics ID', 'analytics'),
('facebook_pixel_id', '', 'Facebook Pixel ID', 'analytics'),
('analytics_enabled', '1', 'Enable Analytics Tracking', 'analytics'),
('user_activity_tracking', '1', 'Enable User Activity Tracking', 'analytics'),

-- Maintenance Settings
('maintenance_mode', '0', 'Enable Maintenance Mode', 'maintenance'),
('maintenance_message', 'We are currently performing scheduled maintenance. Please check back soon.', 'Maintenance Message', 'maintenance'),
('backup_enabled', '1', 'Enable Automatic Backups', 'maintenance'),
('backup_frequency', 'daily', 'Backup Frequency (daily/weekly)', 'maintenance'),
('backup_retention_days', '30', 'Backup Retention Days', 'maintenance'),

-- API Settings
('api_enabled', '1', 'Enable API Access', 'api'),
('api_rate_limit', '1000', 'API Rate Limit (requests per hour)', 'api'),
('api_key_required', '1', 'Require API Key for Access', 'api'),
('webhook_enabled', '1', 'Enable Webhook Support', 'api'),

-- Social Media Settings
('facebook_app_id', '', 'Facebook App ID', 'social'),
('facebook_app_secret', '', 'Facebook App Secret', 'social'),
('twitter_api_key', '', 'Twitter API Key', 'social'),
('twitter_api_secret', '', 'Twitter API Secret', 'social'),
('linkedin_client_id', '', 'LinkedIn Client ID', 'social'),
('linkedin_client_secret', '', 'LinkedIn Client Secret', 'social'),

-- File Upload Settings
('max_file_size_mb', '10', 'Maximum File Size (MB)', 'uploads'),
('allowed_file_types', 'jpg,jpeg,png,gif,pdf,doc,docx', 'Allowed File Types', 'uploads'),
('upload_path', '/uploads/', 'Upload Directory Path', 'uploads'),
('image_compression_quality', '80', 'Image Compression Quality (1-100)', 'uploads'),

-- Performance Settings
('cache_enabled', '1', 'Enable Caching', 'performance'),
('cache_duration', '3600', 'Cache Duration (Seconds)', 'performance'),
('image_optimization_enabled', '1', 'Enable Image Optimization', 'performance'),
('cdn_enabled', '0', 'Enable CDN', 'performance'),
('cdn_url', '', 'CDN URL', 'performance');

-- Add category column if it doesn't exist
ALTER TABLE settings ADD COLUMN IF NOT EXISTS category VARCHAR(50) DEFAULT 'general';
ALTER TABLE settings ADD COLUMN IF NOT EXISTS setting_type ENUM('text', 'textarea', 'number', 'boolean', 'select', 'password', 'email', 'url') DEFAULT 'text';
ALTER TABLE settings ADD COLUMN IF NOT EXISTS options TEXT NULL;
ALTER TABLE settings ADD COLUMN IF NOT EXISTS is_sensitive TINYINT(1) DEFAULT 0;
ALTER TABLE settings ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

-- Update setting types for better form rendering
UPDATE settings SET setting_type = 'email' WHERE setting_key LIKE '%email%';
UPDATE settings SET setting_type = 'password' WHERE setting_key LIKE '%password%' OR setting_key LIKE '%secret%' OR setting_key LIKE '%key%';
UPDATE settings SET setting_type = 'url' WHERE setting_key LIKE '%url%' OR setting_key = 'site_url';
UPDATE settings SET setting_type = 'number' WHERE setting_key LIKE '%amount%' OR setting_key LIKE '%commission%' OR setting_key LIKE '%timeout%' OR setting_key LIKE '%limit%' OR setting_key LIKE '%max%' OR setting_key LIKE '%min%';
UPDATE settings SET setting_type = 'boolean' WHERE setting_key LIKE '%enabled%' OR setting_key LIKE '%required%' OR setting_key = 'maintenance_mode';
UPDATE settings SET setting_type = 'textarea' WHERE setting_key = 'maintenance_message' OR setting_key = 'site_description';
UPDATE settings SET setting_type = 'select', options = 'daily,weekly,monthly' WHERE setting_key = 'payout_schedule' OR setting_key = 'backup_frequency';
UPDATE settings SET setting_type = 'select', options = 'tls,ssl' WHERE setting_key = 'email_smtp_encryption';

-- Mark sensitive settings
UPDATE settings SET is_sensitive = 1 WHERE setting_key LIKE '%password%' OR setting_key LIKE '%secret%' OR setting_key LIKE '%key%';
