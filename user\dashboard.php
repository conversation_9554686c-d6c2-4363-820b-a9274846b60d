<?php
require_once '../config.php';
require_once '../includes/instagram_verification.php';
require_once '../includes/badge_system.php';
require_login(['user']);
require_instagram_verification('user');

$db = Database::getInstance();
$user_id = get_user_id();

// Get user data
$user = $db->fetch("SELECT * FROM users WHERE id = ?", [$user_id]);

// Get dashboard statistics
$stats = [
    'total_tasks' => $db->fetch("SELECT COUNT(*) as count FROM user_tasks WHERE user_id = ?", [$user_id])['count'],
    'completed_tasks' => $db->fetch("SELECT COUNT(*) as count FROM user_tasks WHERE user_id = ? AND status = 'approved'", [$user_id])['count'],
    'pending_tasks' => $db->fetch("SELECT COUNT(*) as count FROM user_tasks WHERE user_id = ? AND status IN ('assigned', 'submitted')", [$user_id])['count'],
    'total_earned' => $user['total_earned'],
    'pending_payout' => $user['pending_payout']
];

// Get recent tasks
$recent_tasks = $db->fetchAll("
    SELECT ut.*, c.title, c.description, c.reward_amount, c.end_date, b.company_name as brand_name
    FROM user_tasks ut
    JOIN campaigns c ON ut.campaign_id = c.id
    LEFT JOIN brands b ON c.brand_id = b.id
    WHERE ut.user_id = ?
    ORDER BY ut.assigned_at DESC
    LIMIT 10
", [$user_id]);

// Get available campaigns
$available_campaigns = $db->fetchAll("
    SELECT c.*, b.company_name as brand_name,
           (c.max_participants - c.current_participants) as slots_remaining
    FROM campaigns c
    LEFT JOIN brands b ON c.brand_id = b.id
    WHERE c.status = 'active'
    AND c.campaign_type IN ('user_task', 'both')
    AND c.end_date >= CURDATE()
    AND c.current_participants < c.max_participants
    AND c.id NOT IN (
        SELECT campaign_id FROM user_tasks WHERE user_id = ?
    )
    ORDER BY c.created_at DESC
    LIMIT 6
", [$user_id]);

$page_title = 'User Dashboard';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-xl-2 sidebar p-0">
            <div class="p-3">
                <h5 class="text-white mb-4">
                    <i class="fas fa-user me-2"></i>User Panel
                </h5>
                
                <nav class="nav flex-column">
                    <a class="nav-link active" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a class="nav-link" href="tasks.php">
                        <i class="fas fa-tasks"></i>Available Tasks
                    </a>
                    <a class="nav-link" href="submissions.php">
                        <i class="fas fa-upload"></i>My Submissions
                    </a>
                    <a class="nav-link" href="wallet.php">
                        <i class="fas fa-wallet"></i>Wallet
                    </a>
                    <a class="nav-link" href="profile.php">
                        <i class="fas fa-user-edit"></i>Profile
                    </a>
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10 main-content" style="margin-left: auto;">
            <!-- Top Bar -->
            <div class="bg-white shadow-sm p-3 mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">Welcome back, <?php echo htmlspecialchars($user['full_name']); ?>!</h4>
                        <small class="text-muted">Here's what's happening with your account today.</small>

                        <!-- User Badges -->
                        <?php
                        $badge_system = new BadgeSystem();
                        $badge_system->checkAutomaticBadges(get_user_id(), 'user');
                        $user_badges = get_user_badges(get_user_id(), 'user');
                        ?>
                        <?php if (!empty($user_badges)): ?>
                            <div class="badge-container mt-2">
                                <?php foreach ($user_badges as $badge): ?>
                                    <?php echo $badge_system->renderBadge($badge, 'small'); ?>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-outline-primary d-lg-none" onclick="toggleSidebar()">
                            <i class="fas fa-bars"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="p-3">
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <i class="fas fa-tasks fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['total_tasks']; ?></h3>
                                <p class="mb-0">Total Tasks</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card success">
                            <div class="card-body text-center">
                                <i class="fas fa-check-circle fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['completed_tasks']; ?></h3>
                                <p class="mb-0">Completed</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card warning">
                            <div class="card-body text-center">
                                <i class="fas fa-clock fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $stats['pending_tasks']; ?></h3>
                                <p class="mb-0">Pending</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card info">
                            <div class="card-body text-center">
                                <i class="fas fa-rupee-sign fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo format_currency($stats['total_earned']); ?></h3>
                                <p class="mb-0">Total Earned</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-bolt me-2"></i>Quick Actions
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3 mb-2">
                                        <a href="tasks.php" class="btn btn-primary w-100">
                                            <i class="fas fa-search me-2"></i>Find Tasks
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <a href="submissions.php" class="btn btn-success w-100">
                                            <i class="fas fa-upload me-2"></i>Submit Work
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <a href="wallet.php" class="btn btn-warning w-100">
                                            <i class="fas fa-wallet me-2"></i>Check Wallet
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <a href="profile.php" class="btn btn-info w-100">
                                            <i class="fas fa-user-edit me-2"></i>Edit Profile
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <!-- Available Campaigns -->
                    <div class="col-lg-8 mb-4">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-bullhorn me-2"></i>Available Campaigns
                                </h5>
                                <a href="tasks.php" class="btn btn-sm btn-outline-primary">View All</a>
                            </div>
                            <div class="card-body">
                                <?php if (empty($available_campaigns)): ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                        <h6 class="text-muted">No campaigns available right now</h6>
                                        <p class="text-muted">Check back later for new opportunities!</p>
                                    </div>
                                <?php else: ?>
                                    <div class="row">
                                        <?php foreach ($available_campaigns as $campaign): ?>
                                            <div class="col-md-6 mb-3">
                                                <div class="card border">
                                                    <div class="card-body">
                                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                                            <h6 class="fw-bold mb-1"><?php echo htmlspecialchars($campaign['title']); ?></h6>
                                                            <span class="badge bg-success"><?php echo format_currency($campaign['reward_amount']); ?></span>
                                                        </div>
                                                        
                                                        <?php if ($campaign['brand_name']): ?>
                                                            <p class="text-muted small mb-2">
                                                                <i class="fas fa-building me-1"></i><?php echo htmlspecialchars($campaign['brand_name']); ?>
                                                            </p>
                                                        <?php endif; ?>
                                                        
                                                        <p class="small mb-2"><?php echo substr(htmlspecialchars($campaign['description']), 0, 100); ?>...</p>
                                                        
                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <small class="text-muted">
                                                                <i class="fas fa-users me-1"></i><?php echo $campaign['slots_remaining']; ?> slots left
                                                            </small>
                                                            <small class="text-muted">
                                                                <i class="fas fa-calendar me-1"></i>Ends <?php echo date('M j', strtotime($campaign['end_date'])); ?>
                                                            </small>
                                                        </div>
                                                        
                                                        <div class="mt-2">
                                                            <a href="tasks.php?campaign=<?php echo $campaign['id']; ?>" class="btn btn-sm btn-primary w-100">
                                                                Apply Now
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recent Activity -->
                    <div class="col-lg-4 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-history me-2"></i>Recent Activity
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recent_tasks)): ?>
                                    <div class="text-center py-3">
                                        <i class="fas fa-clock fa-2x text-muted mb-2"></i>
                                        <p class="text-muted small">No recent activity</p>
                                    </div>
                                <?php else: ?>
                                    <?php foreach (array_slice($recent_tasks, 0, 5) as $task): ?>
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="flex-shrink-0">
                                                <?php
                                                $icon_class = '';
                                                $badge_class = '';
                                                switch ($task['status']) {
                                                    case 'assigned':
                                                        $icon_class = 'fas fa-clock text-warning';
                                                        $badge_class = 'bg-warning';
                                                        break;
                                                    case 'submitted':
                                                        $icon_class = 'fas fa-upload text-info';
                                                        $badge_class = 'bg-info';
                                                        break;
                                                    case 'approved':
                                                        $icon_class = 'fas fa-check text-success';
                                                        $badge_class = 'bg-success';
                                                        break;
                                                    case 'rejected':
                                                        $icon_class = 'fas fa-times text-danger';
                                                        $badge_class = 'bg-danger';
                                                        break;
                                                    default:
                                                        $icon_class = 'fas fa-circle text-secondary';
                                                        $badge_class = 'bg-secondary';
                                                }
                                                ?>
                                                <i class="<?php echo $icon_class; ?>"></i>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <h6 class="mb-1 small"><?php echo htmlspecialchars($task['title']); ?></h6>
                                                <p class="mb-0 small text-muted">
                                                    <span class="badge <?php echo $badge_class; ?> me-1"><?php echo ucfirst($task['status']); ?></span>
                                                    <?php echo time_ago($task['assigned_at']); ?>
                                                </p>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                    
                                    <div class="text-center mt-3">
                                        <a href="submissions.php" class="btn btn-sm btn-outline-primary">View All</a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <!-- Wallet Summary -->
                        <div class="card mt-3">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-wallet me-2"></i>Wallet Summary
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <h6 class="text-success"><?php echo format_currency($stats['total_earned']); ?></h6>
                                        <small class="text-muted">Total Earned</small>
                                    </div>
                                    <div class="col-6">
                                        <h6 class="text-warning"><?php echo format_currency($stats['pending_payout']); ?></h6>
                                        <small class="text-muted">Pending</small>
                                    </div>
                                </div>
                                
                                <?php if ($stats['pending_payout'] >= MIN_PAYOUT_AMOUNT): ?>
                                    <div class="mt-3">
                                        <a href="wallet.php" class="btn btn-success btn-sm w-100">
                                            <i class="fas fa-money-bill-wave me-1"></i>Request Payout
                                        </a>
                                    </div>
                                <?php else: ?>
                                    <div class="mt-3">
                                        <small class="text-muted d-block text-center">
                                            Minimum payout: <?php echo format_currency(MIN_PAYOUT_AMOUNT); ?>
                                        </small>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
