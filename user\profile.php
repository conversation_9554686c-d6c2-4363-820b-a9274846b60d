<?php
require_once '../config.php';
require_login(['user']);

$db = Database::getInstance();
$user_id = get_user_id();

// Handle profile update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_profile'])) {
    $full_name = sanitize_input($_POST['full_name']);
    $phone = sanitize_input($_POST['phone']);
    $instagram_handle = sanitize_input($_POST['instagram_handle']);
    $upi_id = sanitize_input($_POST['upi_id']);
    
    $errors = [];
    
    if (empty($full_name)) {
        $errors[] = 'Full name is required.';
    }
    
    if (empty($phone)) {
        $errors[] = 'Phone number is required.';
    }
    
    if (empty($errors)) {
        $update_data = [
            'full_name' => $full_name,
            'phone' => $phone,
            'instagram_handle' => $instagram_handle,
            'upi_id' => $upi_id
        ];
        
        // Handle profile image upload
        if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] === UPLOAD_ERR_OK) {
            try {
                $profile_image = upload_file($_FILES['profile_image'], 'uploads/profiles/');
                $update_data['profile_image'] = $profile_image;
            } catch (Exception $e) {
                $errors[] = 'Failed to upload profile image: ' . $e->getMessage();
            }
        }
        
        if (empty($errors)) {
            try {
                $db->update('users', $update_data, 'id = ?', [$user_id]);
                $_SESSION['success'] = 'Profile updated successfully!';
                
                // Update session data
                $_SESSION['full_name'] = $full_name;
                
                redirect('profile.php');
            } catch (Exception $e) {
                $errors[] = 'Failed to update profile. Please try again.';
            }
        }
    }
    
    if (!empty($errors)) {
        $_SESSION['error'] = implode('<br>', $errors);
    }
}

// Handle password change
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['change_password'])) {
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];
    
    $errors = [];
    
    if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
        $errors[] = 'All password fields are required.';
    } elseif ($new_password !== $confirm_password) {
        $errors[] = 'New passwords do not match.';
    } elseif (strlen($new_password) < 6) {
        $errors[] = 'New password must be at least 6 characters long.';
    } else {
        // Verify current password
        $user = $db->fetch("SELECT password FROM users WHERE id = ?", [$user_id]);
        
        if (!password_verify($current_password, $user['password'])) {
            $errors[] = 'Current password is incorrect.';
        }
    }
    
    if (empty($errors)) {
        try {
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
            $db->update('users', ['password' => $hashed_password], 'id = ?', [$user_id]);
            $_SESSION['success'] = 'Password changed successfully!';
            redirect('profile.php');
        } catch (Exception $e) {
            $errors[] = 'Failed to change password. Please try again.';
        }
    }
    
    if (!empty($errors)) {
        $_SESSION['error'] = implode('<br>', $errors);
    }
}

// Get user data
$user = $db->fetch("SELECT * FROM users WHERE id = ?", [$user_id]);

// Get user statistics
$user_stats = $db->fetch("
    SELECT 
        COUNT(*) as total_tasks,
        SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as completed_tasks,
        SUM(CASE WHEN status = 'submitted' THEN 1 ELSE 0 END) as pending_tasks,
        MIN(assigned_at) as member_since
    FROM user_tasks 
    WHERE user_id = ?
", [$user_id]);

$page_title = 'Profile';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-xl-2 sidebar p-0">
            <div class="p-3">
                <h5 class="text-white mb-4">
                    <i class="fas fa-user me-2"></i>User Panel
                </h5>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a class="nav-link" href="tasks.php">
                        <i class="fas fa-tasks"></i>Available Tasks
                    </a>
                    <a class="nav-link" href="submissions.php">
                        <i class="fas fa-upload"></i>My Submissions
                    </a>
                    <a class="nav-link" href="wallet.php">
                        <i class="fas fa-wallet"></i>Wallet
                    </a>
                    <a class="nav-link active" href="profile.php">
                        <i class="fas fa-user-edit"></i>Profile
                    </a>
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10 main-content" style="margin-left: auto;">
            <!-- Top Bar -->
            <div class="bg-white shadow-sm p-3 mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">My Profile</h4>
                        <small class="text-muted">Manage your account settings and information</small>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-outline-primary d-lg-none" onclick="toggleSidebar()">
                            <i class="fas fa-bars"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="p-3">
                <div class="row">
                    <!-- Profile Information -->
                    <div class="col-lg-8 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-user-edit me-2"></i>Profile Information
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                                    <div class="row">
                                        <div class="col-md-4 text-center mb-4">
                                            <div class="position-relative d-inline-block">
                                                <?php if ($user['profile_image']): ?>
                                                    <img src="<?php echo htmlspecialchars($user['profile_image']); ?>" 
                                                         alt="Profile Image" class="rounded-circle" 
                                                         style="width: 120px; height: 120px; object-fit: cover;" id="profilePreview">
                                                <?php else: ?>
                                                    <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" 
                                                         style="width: 120px; height: 120px;" id="profilePreview">
                                                        <i class="fas fa-user fa-3x text-white"></i>
                                                    </div>
                                                <?php endif; ?>
                                                
                                                <label for="profile_image" class="btn btn-sm btn-primary position-absolute bottom-0 end-0 rounded-circle" 
                                                       style="width: 35px; height: 35px; cursor: pointer;">
                                                    <i class="fas fa-camera"></i>
                                                </label>
                                                <input type="file" id="profile_image" name="profile_image" 
                                                       accept="image/*" style="display: none;">
                                            </div>
                                            <p class="text-muted small mt-2">Click camera icon to change</p>
                                        </div>
                                        
                                        <div class="col-md-8">
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label for="username" class="form-label">Username</label>
                                                    <input type="text" class="form-control" id="username" 
                                                           value="<?php echo htmlspecialchars($user['username']); ?>" 
                                                           disabled>
                                                    <div class="form-text">Username cannot be changed</div>
                                                </div>
                                                
                                                <div class="col-md-6 mb-3">
                                                    <label for="email" class="form-label">Email</label>
                                                    <input type="email" class="form-control" id="email" 
                                                           value="<?php echo htmlspecialchars($user['email']); ?>" 
                                                           disabled>
                                                    <div class="form-text">Email cannot be changed</div>
                                                </div>
                                            </div>
                                            
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label for="full_name" class="form-label">Full Name</label>
                                                    <input type="text" class="form-control" id="full_name" name="full_name" 
                                                           value="<?php echo htmlspecialchars($user['full_name']); ?>" 
                                                           required>
                                                    <div class="invalid-feedback">Please provide your full name.</div>
                                                </div>
                                                
                                                <div class="col-md-6 mb-3">
                                                    <label for="phone" class="form-label">Phone Number</label>
                                                    <input type="tel" class="form-control" id="phone" name="phone" 
                                                           value="<?php echo htmlspecialchars($user['phone']); ?>" 
                                                           required>
                                                    <div class="invalid-feedback">Please provide your phone number.</div>
                                                </div>
                                            </div>
                                            
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label for="instagram_handle" class="form-label">Instagram Handle</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text">@</span>
                                                        <input type="text" class="form-control" id="instagram_handle" name="instagram_handle" 
                                                               value="<?php echo htmlspecialchars($user['instagram_handle'] ?? ''); ?>" 
                                                               placeholder="your_username">
                                                    </div>
                                                </div>
                                                
                                                <div class="col-md-6 mb-3">
                                                    <label for="upi_id" class="form-label">UPI ID</label>
                                                    <input type="text" class="form-control" id="upi_id" name="upi_id" 
                                                           value="<?php echo htmlspecialchars($user['upi_id'] ?? ''); ?>" 
                                                           placeholder="yourname@paytm">
                                                    <div class="form-text">For receiving payments</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="text-end">
                                        <button type="submit" name="update_profile" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>Update Profile
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                        
                        <!-- Change Password -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-lock me-2"></i>Change Password
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" class="needs-validation" novalidate>
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label for="current_password" class="form-label">Current Password</label>
                                            <input type="password" class="form-control" id="current_password" name="current_password" required>
                                            <div class="invalid-feedback">Please enter your current password.</div>
                                        </div>
                                        
                                        <div class="col-md-4 mb-3">
                                            <label for="new_password" class="form-label">New Password</label>
                                            <input type="password" class="form-control" id="new_password" name="new_password" 
                                                   minlength="6" required>
                                            <div class="invalid-feedback">Password must be at least 6 characters.</div>
                                        </div>
                                        
                                        <div class="col-md-4 mb-3">
                                            <label for="confirm_password" class="form-label">Confirm New Password</label>
                                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                                   minlength="6" required>
                                            <div class="invalid-feedback">Please confirm your new password.</div>
                                        </div>
                                    </div>
                                    
                                    <div class="text-end">
                                        <button type="submit" name="change_password" class="btn btn-warning">
                                            <i class="fas fa-key me-2"></i>Change Password
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Profile Stats -->
                    <div class="col-lg-4 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-bar me-2"></i>Account Statistics
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row text-center mb-3">
                                    <div class="col-6">
                                        <h4 class="text-primary"><?php echo $user_stats['total_tasks'] ?? 0; ?></h4>
                                        <small class="text-muted">Total Tasks</small>
                                    </div>
                                    <div class="col-6">
                                        <h4 class="text-success"><?php echo $user_stats['completed_tasks'] ?? 0; ?></h4>
                                        <small class="text-muted">Completed</small>
                                    </div>
                                </div>
                                
                                <div class="row text-center mb-3">
                                    <div class="col-6">
                                        <h4 class="text-warning"><?php echo $user_stats['pending_tasks'] ?? 0; ?></h4>
                                        <small class="text-muted">Pending</small>
                                    </div>
                                    <div class="col-6">
                                        <h4 class="text-info"><?php echo format_currency($user['total_earned']); ?></h4>
                                        <small class="text-muted">Total Earned</small>
                                    </div>
                                </div>
                                
                                <hr>
                                
                                <div class="text-center">
                                    <p class="text-muted small mb-2">Member Since</p>
                                    <p class="fw-bold">
                                        <?php 
                                        if ($user_stats['member_since']) {
                                            echo date('F Y', strtotime($user_stats['member_since']));
                                        } else {
                                            echo date('F Y', strtotime($user['created_at']));
                                        }
                                        ?>
                                    </p>
                                </div>
                                
                                <div class="text-center mt-3">
                                    <span class="badge bg-<?php echo $user['status'] === 'active' ? 'success' : 'warning'; ?> fs-6">
                                        <i class="fas fa-circle me-1"></i>
                                        <?php echo ucfirst($user['status']); ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Account Actions -->
                        <div class="card mt-3">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-cog me-2"></i>Account Actions
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <a href="wallet.php" class="btn btn-outline-success btn-sm">
                                        <i class="fas fa-wallet me-2"></i>View Wallet
                                    </a>
                                    <a href="submissions.php" class="btn btn-outline-info btn-sm">
                                        <i class="fas fa-upload me-2"></i>My Submissions
                                    </a>
                                    <a href="tasks.php" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-search me-2"></i>Find Tasks
                                    </a>
                                </div>
                                
                                <hr>
                                
                                <div class="text-center">
                                    <small class="text-muted">Need help?</small><br>
                                    <a href="mailto:<EMAIL>" class="btn btn-link btn-sm">
                                        <i class="fas fa-envelope me-1"></i>Contact Support
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Profile image preview
document.getElementById('profile_image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById('profilePreview');
            preview.innerHTML = '<img src="' + e.target.result + '" alt="Profile Preview" class="rounded-circle" style="width: 120px; height: 120px; object-fit: cover;">';
        };
        reader.readAsDataURL(file);
    }
});

// Password confirmation validation
document.getElementById('confirm_password').addEventListener('input', function() {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = this.value;
    
    if (newPassword !== confirmPassword) {
        this.setCustomValidity('Passwords do not match');
    } else {
        this.setCustomValidity('');
    }
});
</script>

<?php include '../includes/footer.php'; ?>
