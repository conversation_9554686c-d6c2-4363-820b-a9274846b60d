<?php
require_once '../config.php';
require_login(['user']);

$db = Database::getInstance();
$user_id = get_user_id();

// Handle submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_task'])) {
    $task_id = intval($_POST['task_id']);
    $post_url = sanitize_input($_POST['post_url']);
    $caption = sanitize_input($_POST['caption']);
    $hashtags_used = sanitize_input($_POST['hashtags_used']);
    $submission_notes = sanitize_input($_POST['submission_notes']);
    
    // Verify task belongs to user and is in assigned status
    $task = $db->fetch("
        SELECT ut.*, c.title, c.campaign_type 
        FROM user_tasks ut 
        JOIN campaigns c ON ut.campaign_id = c.id 
        WHERE ut.id = ? AND ut.user_id = ? AND ut.status = 'assigned'
    ", [$task_id, $user_id]);
    
    if ($task) {
        $screenshot_path = '';
        
        // Handle file upload
        if (isset($_FILES['screenshot']) && $_FILES['screenshot']['error'] === UPLOAD_ERR_OK) {
            try {
                $screenshot_path = upload_file($_FILES['screenshot'], 'uploads/submissions/');
            } catch (Exception $e) {
                $_SESSION['error'] = 'Failed to upload screenshot: ' . $e->getMessage();
                redirect('submissions.php?task=' . $task_id);
            }
        }
        
        if (empty($post_url) && empty($screenshot_path)) {
            $_SESSION['error'] = 'Please provide either a post URL or upload a screenshot.';
        } else {
            try {
                // Insert submission
                $submission_id = $db->insert('user_submissions', [
                    'task_id' => $task_id,
                    'user_id' => $user_id,
                    'campaign_id' => $task['campaign_id'],
                    'post_url' => $post_url,
                    'screenshot_path' => $screenshot_path,
                    'caption' => $caption,
                    'hashtags_used' => $hashtags_used,
                    'submission_notes' => $submission_notes
                ]);
                
                // Update task status
                $db->update('user_tasks', 
                    ['status' => 'submitted', 'submitted_at' => date('Y-m-d H:i:s')], 
                    'id = ?', [$task_id]
                );
                
                $_SESSION['success'] = 'Submission successful! Your work is now under review.';
                redirect('submissions.php');
            } catch (Exception $e) {
                $_SESSION['error'] = 'Failed to submit work. Please try again.';
            }
        }
    } else {
        $_SESSION['error'] = 'Invalid task or task already submitted.';
    }
}

// Get specific task for submission if provided
$submit_task = null;
if (isset($_GET['task'])) {
    $task_id = intval($_GET['task']);
    $submit_task = $db->fetch("
        SELECT ut.*, c.title, c.description, c.requirements, c.hashtags, c.post_format, c.end_date,
               b.name as brand_name
        FROM user_tasks ut
        JOIN campaigns c ON ut.campaign_id = c.id
        LEFT JOIN brands b ON c.brand_id = b.id
        WHERE ut.id = ? AND ut.user_id = ? AND ut.status = 'assigned'
    ", [$task_id, $user_id]);
}

// Get user's tasks with submissions
$tasks = $db->fetchAll("
    SELECT ut.*, c.title, c.description, c.reward_amount, c.end_date,
           b.name as brand_name,
           us.id as submission_id, us.post_url, us.screenshot_path, 
           us.submitted_at, us.submission_notes
    FROM user_tasks ut
    JOIN campaigns c ON ut.campaign_id = c.id
    LEFT JOIN brands b ON c.brand_id = b.id
    LEFT JOIN user_submissions us ON ut.id = us.task_id
    WHERE ut.user_id = ?
    ORDER BY ut.assigned_at DESC
", [$user_id]);

$page_title = 'My Submissions';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-xl-2 sidebar p-0">
            <div class="p-3">
                <h5 class="text-white mb-4">
                    <i class="fas fa-user me-2"></i>User Panel
                </h5>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a class="nav-link" href="tasks.php">
                        <i class="fas fa-tasks"></i>Available Tasks
                    </a>
                    <a class="nav-link active" href="submissions.php">
                        <i class="fas fa-upload"></i>My Submissions
                    </a>
                    <a class="nav-link" href="wallet.php">
                        <i class="fas fa-wallet"></i>Wallet
                    </a>
                    <a class="nav-link" href="profile.php">
                        <i class="fas fa-user-edit"></i>Profile
                    </a>
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10 main-content" style="margin-left: auto;">
            <!-- Top Bar -->
            <div class="bg-white shadow-sm p-3 mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">My Submissions</h4>
                        <small class="text-muted">Submit your work and track approval status</small>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-outline-primary d-lg-none" onclick="toggleSidebar()">
                            <i class="fas fa-bars"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="p-3">
                <?php if ($submit_task): ?>
                    <!-- Submission Form -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-upload me-2"></i>Submit Work: <?php echo htmlspecialchars($submit_task['title']); ?>
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-4">
                                <div class="col-md-8">
                                    <h6 class="fw-bold">Campaign Details</h6>
                                    <p class="mb-2"><?php echo htmlspecialchars($submit_task['description']); ?></p>
                                    
                                    <?php if ($submit_task['requirements']): ?>
                                        <div class="alert alert-info">
                                            <strong>Requirements:</strong><br>
                                            <?php echo nl2br(htmlspecialchars($submit_task['requirements'])); ?>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($submit_task['hashtags']): ?>
                                        <div class="alert alert-warning">
                                            <strong>Required Hashtags:</strong><br>
                                            <code><?php echo htmlspecialchars($submit_task['hashtags']); ?></code>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="col-md-4">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h6 class="fw-bold">Task Info</h6>
                                            <p class="mb-1"><strong>Reward:</strong> <?php echo format_currency($submit_task['reward_amount']); ?></p>
                                            <p class="mb-1"><strong>Format:</strong> <?php echo ucfirst($submit_task['post_format']); ?></p>
                                            <p class="mb-0"><strong>Deadline:</strong> <?php echo date('M j, Y', strtotime($submit_task['end_date'])); ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                                <input type="hidden" name="task_id" value="<?php echo $submit_task['id']; ?>">
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="post_url" class="form-label">Instagram Post URL</label>
                                        <input type="url" class="form-control" id="post_url" name="post_url" 
                                               placeholder="https://www.instagram.com/p/...">
                                        <div class="form-text">Copy the link to your Instagram post</div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="screenshot" class="form-label">Screenshot (Alternative)</label>
                                        <input type="file" class="form-control file-input" id="screenshot" name="screenshot" 
                                               accept="image/*">
                                        <div class="form-text">Upload a screenshot if you can't provide the URL</div>
                                        <div class="file-preview mt-2"></div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="caption" class="form-label">Caption Used</label>
                                    <textarea class="form-control" id="caption" name="caption" rows="3" 
                                              placeholder="Paste the caption you used for the post..."></textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="hashtags_used" class="form-label">Hashtags Used</label>
                                    <input type="text" class="form-control" id="hashtags_used" name="hashtags_used" 
                                           placeholder="#hashtag1 #hashtag2 #hashtag3">
                                    <div class="form-text">Include all hashtags you used in the post</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="submission_notes" class="form-label">Additional Notes (Optional)</label>
                                    <textarea class="form-control" id="submission_notes" name="submission_notes" rows="2" 
                                              placeholder="Any additional information about your submission..."></textarea>
                                </div>
                                
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>Important:</strong> Make sure your post follows all the requirements and includes the required hashtags. 
                                    Submissions that don't meet the requirements may be rejected.
                                </div>
                                
                                <div class="d-flex gap-2">
                                    <button type="submit" name="submit_task" class="btn btn-success">
                                        <i class="fas fa-paper-plane me-2"></i>Submit Work
                                    </button>
                                    <a href="submissions.php" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Back to Submissions
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                <?php endif; ?>
                
                <!-- Tasks List -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>All My Tasks
                        </h5>
                        <a href="tasks.php" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus me-1"></i>Find More Tasks
                        </a>
                    </div>
                    <div class="card-body">
                        <?php if (empty($tasks)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No tasks yet</h5>
                                <p class="text-muted">Start by applying for available campaigns!</p>
                                <a href="tasks.php" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>Browse Tasks
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Campaign</th>
                                            <th>Brand</th>
                                            <th>Reward</th>
                                            <th>Status</th>
                                            <th>Deadline</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($tasks as $task): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($task['title']); ?></strong>
                                                    <br><small class="text-muted"><?php echo substr(htmlspecialchars($task['description']), 0, 60); ?>...</small>
                                                </td>
                                                <td><?php echo htmlspecialchars($task['brand_name'] ?? 'RealEarners'); ?></td>
                                                <td><strong class="text-success"><?php echo format_currency($task['reward_amount']); ?></strong></td>
                                                <td>
                                                    <?php
                                                    $status_class = '';
                                                    $status_icon = '';
                                                    switch ($task['status']) {
                                                        case 'assigned':
                                                            $status_class = 'bg-warning';
                                                            $status_icon = 'fas fa-clock';
                                                            break;
                                                        case 'submitted':
                                                            $status_class = 'bg-info';
                                                            $status_icon = 'fas fa-upload';
                                                            break;
                                                        case 'approved':
                                                            $status_class = 'bg-success';
                                                            $status_icon = 'fas fa-check';
                                                            break;
                                                        case 'rejected':
                                                            $status_class = 'bg-danger';
                                                            $status_icon = 'fas fa-times';
                                                            break;
                                                        case 'paid':
                                                            $status_class = 'bg-primary';
                                                            $status_icon = 'fas fa-money-bill';
                                                            break;
                                                    }
                                                    ?>
                                                    <span class="badge <?php echo $status_class; ?>">
                                                        <i class="<?php echo $status_icon; ?> me-1"></i>
                                                        <?php echo ucfirst($task['status']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php 
                                                    $deadline = strtotime($task['end_date']);
                                                    $now = time();
                                                    $days_left = ceil(($deadline - $now) / (24 * 60 * 60));
                                                    
                                                    if ($days_left < 0) {
                                                        echo '<span class="text-danger">Expired</span>';
                                                    } elseif ($days_left <= 1) {
                                                        echo '<span class="text-danger">' . date('M j', $deadline) . '</span>';
                                                    } elseif ($days_left <= 3) {
                                                        echo '<span class="text-warning">' . date('M j', $deadline) . '</span>';
                                                    } else {
                                                        echo '<span class="text-muted">' . date('M j', $deadline) . '</span>';
                                                    }
                                                    ?>
                                                </td>
                                                <td>
                                                    <?php if ($task['status'] === 'assigned'): ?>
                                                        <a href="submissions.php?task=<?php echo $task['id']; ?>" class="btn btn-sm btn-primary">
                                                            <i class="fas fa-upload me-1"></i>Submit
                                                        </a>
                                                    <?php elseif ($task['status'] === 'submitted'): ?>
                                                        <span class="text-muted small">
                                                            <i class="fas fa-clock me-1"></i>Under Review
                                                        </span>
                                                    <?php elseif ($task['submission_id']): ?>
                                                        <button class="btn btn-sm btn-outline-info" 
                                                                data-bs-toggle="modal" 
                                                                data-bs-target="#submissionModal<?php echo $task['submission_id']; ?>">
                                                            <i class="fas fa-eye me-1"></i>View
                                                        </button>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                            
                                            <?php if ($task['submission_id']): ?>
                                                <!-- Submission Details Modal -->
                                                <div class="modal fade" id="submissionModal<?php echo $task['submission_id']; ?>" tabindex="-1">
                                                    <div class="modal-dialog modal-lg">
                                                        <div class="modal-content">
                                                            <div class="modal-header">
                                                                <h5 class="modal-title">Submission Details</h5>
                                                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                            </div>
                                                            <div class="modal-body">
                                                                <h6 class="fw-bold"><?php echo htmlspecialchars($task['title']); ?></h6>
                                                                <p class="text-muted mb-3">Submitted on <?php echo date('F j, Y \a\t g:i A', strtotime($task['submitted_at'])); ?></p>
                                                                
                                                                <?php if ($task['post_url']): ?>
                                                                    <div class="mb-3">
                                                                        <strong>Post URL:</strong><br>
                                                                        <a href="<?php echo htmlspecialchars($task['post_url']); ?>" target="_blank" class="text-primary">
                                                                            <?php echo htmlspecialchars($task['post_url']); ?>
                                                                        </a>
                                                                    </div>
                                                                <?php endif; ?>
                                                                
                                                                <?php if ($task['screenshot_path']): ?>
                                                                    <div class="mb-3">
                                                                        <strong>Screenshot:</strong><br>
                                                                        <img src="<?php echo htmlspecialchars($task['screenshot_path']); ?>" 
                                                                             alt="Submission Screenshot" class="img-fluid rounded" style="max-height: 300px;">
                                                                    </div>
                                                                <?php endif; ?>
                                                                
                                                                <?php if ($task['submission_notes']): ?>
                                                                    <div class="mb-3">
                                                                        <strong>Notes:</strong><br>
                                                                        <?php echo nl2br(htmlspecialchars($task['submission_notes'])); ?>
                                                                    </div>
                                                                <?php endif; ?>
                                                            </div>
                                                            <div class="modal-footer">
                                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
