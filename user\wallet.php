<?php
require_once '../config.php';
require_login(['user']);

$db = Database::getInstance();
$user_id = get_user_id();

// Handle payout request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['request_payout'])) {
    $amount = floatval($_POST['amount']);
    $upi_id = sanitize_input($_POST['upi_id']);
    
    // Get current user data
    $user = $db->fetch("SELECT * FROM users WHERE id = ?", [$user_id]);
    
    if ($amount < MIN_PAYOUT_AMOUNT) {
        $_SESSION['error'] = 'Minimum payout amount is ' . format_currency(MIN_PAYOUT_AMOUNT);
    } elseif ($amount > $user['pending_payout']) {
        $_SESSION['error'] = 'Insufficient balance. Available: ' . format_currency($user['pending_payout']);
    } elseif (empty($upi_id)) {
        $_SESSION['error'] = 'UPI ID is required for payout.';
    } else {
        try {
            // Create payout request
            $payout_id = $db->insert('payouts', [
                'user_type' => 'user',
                'user_id' => $user_id,
                'amount' => $amount,
                'upi_id' => $upi_id,
                'status' => 'pending'
            ]);
            
            // Update user's pending payout
            $new_pending = $user['pending_payout'] - $amount;
            $db->update('users', ['pending_payout' => $new_pending], 'id = ?', [$user_id]);
            
            // Update UPI ID if provided
            if ($upi_id !== $user['upi_id']) {
                $db->update('users', ['upi_id' => $upi_id], 'id = ?', [$user_id]);
            }
            
            $_SESSION['success'] = 'Payout request submitted successfully! You will receive payment within 24-48 hours.';
            redirect('wallet.php');
        } catch (Exception $e) {
            $_SESSION['error'] = 'Failed to submit payout request. Please try again.';
        }
    }
}

// Get user data
$user = $db->fetch("SELECT * FROM users WHERE id = ?", [$user_id]);

// Get earnings statistics
$earnings_stats = $db->fetch("
    SELECT 
        COUNT(*) as total_tasks,
        SUM(CASE WHEN status = 'approved' THEN reward_amount ELSE 0 END) as total_earned,
        SUM(CASE WHEN status = 'paid' THEN reward_amount ELSE 0 END) as total_paid,
        SUM(CASE WHEN status IN ('approved', 'paid') THEN reward_amount ELSE 0 END) as lifetime_earnings
    FROM user_tasks 
    WHERE user_id = ?
", [$user_id]);

// Get recent payouts
$recent_payouts = $db->fetchAll("
    SELECT * FROM payouts 
    WHERE user_type = 'user' AND user_id = ? 
    ORDER BY created_at DESC 
    LIMIT 10
", [$user_id]);

// Get recent earnings (approved tasks)
$recent_earnings = $db->fetchAll("
    SELECT ut.*, c.title, c.description, b.company_name as brand_name
    FROM user_tasks ut
    JOIN campaigns c ON ut.campaign_id = c.id
    LEFT JOIN brands b ON c.brand_id = b.id
    WHERE ut.user_id = ? AND ut.status IN ('approved', 'paid')
    ORDER BY ut.approved_at DESC
    LIMIT 10
", [$user_id]);

$page_title = 'Wallet';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-xl-2 sidebar p-0">
            <div class="p-3">
                <h5 class="text-white mb-4">
                    <i class="fas fa-user me-2"></i>User Panel
                </h5>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>Dashboard
                    </a>
                    <a class="nav-link" href="tasks.php">
                        <i class="fas fa-tasks"></i>Available Tasks
                    </a>
                    <a class="nav-link" href="submissions.php">
                        <i class="fas fa-upload"></i>My Submissions
                    </a>
                    <a class="nav-link active" href="wallet.php">
                        <i class="fas fa-wallet"></i>Wallet
                    </a>
                    <a class="nav-link" href="profile.php">
                        <i class="fas fa-user-edit"></i>Profile
                    </a>
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt"></i>Logout
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-xl-10 main-content" style="margin-left: auto;">
            <!-- Top Bar -->
            <div class="bg-white shadow-sm p-3 mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">My Wallet</h4>
                        <small class="text-muted">Manage your earnings and request payouts</small>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-outline-primary d-lg-none" onclick="toggleSidebar()">
                            <i class="fas fa-bars"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="p-3">
                <!-- Wallet Overview -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card success">
                            <div class="card-body text-center">
                                <i class="fas fa-wallet fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo format_currency($user['pending_payout']); ?></h3>
                                <p class="mb-0">Available Balance</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card info">
                            <div class="card-body text-center">
                                <i class="fas fa-chart-line fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo format_currency($user['total_earned']); ?></h3>
                                <p class="mb-0">Total Earned</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card warning">
                            <div class="card-body text-center">
                                <i class="fas fa-tasks fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo $earnings_stats['total_tasks']; ?></h3>
                                <p class="mb-0">Completed Tasks</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                                <h3 class="fw-bold"><?php echo format_currency($earnings_stats['total_paid'] ?? 0); ?></h3>
                                <p class="mb-0">Total Paid Out</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <!-- Payout Request -->
                    <div class="col-lg-4 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-money-bill-wave me-2"></i>Request Payout
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if ($user['pending_payout'] >= MIN_PAYOUT_AMOUNT): ?>
                                    <form method="POST" class="needs-validation" novalidate>
                                        <div class="mb-3">
                                            <label for="amount" class="form-label">Amount</label>
                                            <div class="input-group">
                                                <span class="input-group-text">₹</span>
                                                <input type="number" class="form-control" id="amount" name="amount" 
                                                       min="<?php echo MIN_PAYOUT_AMOUNT; ?>" 
                                                       max="<?php echo $user['pending_payout']; ?>" 
                                                       value="<?php echo $user['pending_payout']; ?>" 
                                                       step="0.01" required>
                                            </div>
                                            <div class="form-text">
                                                Available: <?php echo format_currency($user['pending_payout']); ?>
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="upi_id" class="form-label">UPI ID</label>
                                            <input type="text" class="form-control" id="upi_id" name="upi_id" 
                                                   value="<?php echo htmlspecialchars($user['upi_id'] ?? ''); ?>" 
                                                   placeholder="yourname@paytm" required>
                                            <div class="form-text">
                                                Payment will be sent to this UPI ID
                                            </div>
                                        </div>
                                        
                                        <div class="alert alert-info small">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Payouts are processed within 24-48 hours. Minimum amount: <?php echo format_currency(MIN_PAYOUT_AMOUNT); ?>
                                        </div>
                                        
                                        <div class="d-grid">
                                            <button type="submit" name="request_payout" class="btn btn-success">
                                                <i class="fas fa-paper-plane me-2"></i>Request Payout
                                            </button>
                                        </div>
                                    </form>
                                <?php else: ?>
                                    <div class="text-center py-3">
                                        <i class="fas fa-exclamation-triangle fa-2x text-warning mb-3"></i>
                                        <h6 class="text-muted">Insufficient Balance</h6>
                                        <p class="text-muted small">
                                            You need at least <?php echo format_currency(MIN_PAYOUT_AMOUNT); ?> to request a payout.
                                            <br>Current balance: <?php echo format_currency($user['pending_payout']); ?>
                                        </p>
                                        <a href="tasks.php" class="btn btn-primary btn-sm">
                                            <i class="fas fa-search me-1"></i>Find More Tasks
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <!-- Quick Stats -->
                        <div class="card mt-3">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-chart-pie me-2"></i>This Month
                                </h6>
                            </div>
                            <div class="card-body">
                                <?php
                                $monthly_stats = $db->fetch("
                                    SELECT 
                                        COUNT(*) as tasks_completed,
                                        SUM(reward_amount) as month_earnings
                                    FROM user_tasks 
                                    WHERE user_id = ? 
                                    AND status IN ('approved', 'paid')
                                    AND MONTH(approved_at) = MONTH(CURRENT_DATE())
                                    AND YEAR(approved_at) = YEAR(CURRENT_DATE())
                                ", [$user_id]);
                                ?>
                                <div class="row text-center">
                                    <div class="col-6">
                                        <h6 class="text-primary"><?php echo $monthly_stats['tasks_completed'] ?? 0; ?></h6>
                                        <small class="text-muted">Tasks</small>
                                    </div>
                                    <div class="col-6">
                                        <h6 class="text-success"><?php echo format_currency($monthly_stats['month_earnings'] ?? 0); ?></h6>
                                        <small class="text-muted">Earned</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recent Payouts -->
                    <div class="col-lg-4 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-history me-2"></i>Recent Payouts
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recent_payouts)): ?>
                                    <div class="text-center py-3">
                                        <i class="fas fa-receipt fa-2x text-muted mb-2"></i>
                                        <p class="text-muted small">No payouts yet</p>
                                    </div>
                                <?php else: ?>
                                    <?php foreach ($recent_payouts as $payout): ?>
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <div>
                                                <h6 class="mb-1"><?php echo format_currency($payout['amount']); ?></h6>
                                                <small class="text-muted">
                                                    <?php echo date('M j, Y', strtotime($payout['created_at'])); ?>
                                                </small>
                                            </div>
                                            <div>
                                                <?php
                                                $status_class = '';
                                                switch ($payout['status']) {
                                                    case 'pending':
                                                        $status_class = 'bg-warning';
                                                        break;
                                                    case 'processing':
                                                        $status_class = 'bg-info';
                                                        break;
                                                    case 'completed':
                                                        $status_class = 'bg-success';
                                                        break;
                                                    case 'failed':
                                                        $status_class = 'bg-danger';
                                                        break;
                                                    case 'cancelled':
                                                        $status_class = 'bg-secondary';
                                                        break;
                                                }
                                                ?>
                                                <span class="badge <?php echo $status_class; ?> small">
                                                    <?php echo ucfirst($payout['status']); ?>
                                                </span>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recent Earnings -->
                    <div class="col-lg-4 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-coins me-2"></i>Recent Earnings
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recent_earnings)): ?>
                                    <div class="text-center py-3">
                                        <i class="fas fa-coins fa-2x text-muted mb-2"></i>
                                        <p class="text-muted small">No earnings yet</p>
                                        <a href="tasks.php" class="btn btn-primary btn-sm">
                                            Start Earning
                                        </a>
                                    </div>
                                <?php else: ?>
                                    <?php foreach (array_slice($recent_earnings, 0, 5) as $earning): ?>
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <div>
                                                <h6 class="mb-1 small"><?php echo htmlspecialchars($earning['title']); ?></h6>
                                                <small class="text-muted">
                                                    <?php echo htmlspecialchars($earning['brand_name'] ?? 'RealEarners'); ?>
                                                </small>
                                            </div>
                                            <div class="text-end">
                                                <h6 class="mb-0 text-success"><?php echo format_currency($earning['reward_amount']); ?></h6>
                                                <small class="text-muted">
                                                    <?php echo time_ago($earning['approved_at']); ?>
                                                </small>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Detailed Transaction History -->
                <?php if (!empty($recent_payouts) || !empty($recent_earnings)): ?>
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-list me-2"></i>Transaction History
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Type</th>
                                            <th>Description</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        // Combine and sort transactions
                                        $transactions = [];
                                        
                                        foreach ($recent_payouts as $payout) {
                                            $transactions[] = [
                                                'date' => $payout['created_at'],
                                                'type' => 'Payout',
                                                'description' => 'Withdrawal to ' . $payout['upi_id'],
                                                'amount' => -$payout['amount'],
                                                'status' => $payout['status']
                                            ];
                                        }
                                        
                                        foreach ($recent_earnings as $earning) {
                                            $transactions[] = [
                                                'date' => $earning['approved_at'],
                                                'type' => 'Earning',
                                                'description' => $earning['title'],
                                                'amount' => $earning['reward_amount'],
                                                'status' => $earning['status']
                                            ];
                                        }
                                        
                                        // Sort by date descending
                                        usort($transactions, function($a, $b) {
                                            return strtotime($b['date']) - strtotime($a['date']);
                                        });
                                        
                                        foreach (array_slice($transactions, 0, 15) as $transaction):
                                        ?>
                                            <tr>
                                                <td><?php echo date('M j, Y', strtotime($transaction['date'])); ?></td>
                                                <td>
                                                    <span class="badge <?php echo $transaction['type'] === 'Earning' ? 'bg-success' : 'bg-info'; ?>">
                                                        <?php echo $transaction['type']; ?>
                                                    </span>
                                                </td>
                                                <td><?php echo htmlspecialchars($transaction['description']); ?></td>
                                                <td>
                                                    <strong class="<?php echo $transaction['amount'] > 0 ? 'text-success' : 'text-danger'; ?>">
                                                        <?php echo ($transaction['amount'] > 0 ? '+' : '') . format_currency(abs($transaction['amount'])); ?>
                                                    </strong>
                                                </td>
                                                <td>
                                                    <?php
                                                    $status_class = '';
                                                    switch ($transaction['status']) {
                                                        case 'approved':
                                                        case 'completed':
                                                            $status_class = 'bg-success';
                                                            break;
                                                        case 'pending':
                                                        case 'processing':
                                                            $status_class = 'bg-warning';
                                                            break;
                                                        case 'failed':
                                                        case 'rejected':
                                                            $status_class = 'bg-danger';
                                                            break;
                                                        default:
                                                            $status_class = 'bg-secondary';
                                                    }
                                                    ?>
                                                    <span class="badge <?php echo $status_class; ?>">
                                                        <?php echo ucfirst($transaction['status']); ?>
                                                    </span>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
